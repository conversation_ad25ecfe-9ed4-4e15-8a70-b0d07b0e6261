# ImageGenerationPopup Adaptive Positioning Implementation

## Overview
The ImageGenerationPopup component has been modified to implement adaptive positioning behavior that responds to viewport constraints and guidepopup dimensions.

## Requirements Implemented

### 1. Default Positioning
- **External positioning**: The popup displays to the right side of the guidepopup with a 15px gap
- **Positioning logic**: Uses `guidePopupPos.left + guidePopupPos.width + gap` to calculate external position

### 2. Guidepopup Width Constraints
- **Maximum width**: The guidepopup can expand up to 1200px (enforced in CanvasSettings.tsx)
- **Dynamic width**: The popup responds to actual guidepopup width changes in real-time

### 3. Responsive Positioning Logic
- **Space detection**: Calculates available viewport space using `window.innerWidth`
- **Threshold**: Uses 20px margin from viewport edge for safety
- **Fallback behavior**: When insufficient space, aligns to the right side within the guidepopup

### 4. Implementation Features
- **Smooth transitions**: Uses cubic-bezier easing for professional animations
- **Real-time updates**: Responds to window resize, scroll, and guidepopup changes
- **Visual feedback**: Different styling for external vs internal positioning
- **Performance optimized**: Uses ResizeObserver and MutationObserver for efficient updates

## Technical Implementation

### Key Functions

#### `getGuidePopupPosition()`
```typescript
const getGuidePopupPosition = () => {
  const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||
                  document.getElementById('guide-popup');
  if (element) {
    const rect = element.getBoundingClientRect();
    return { top: rect.top, left: rect.left, width: rect.width, height: rect.height };
  }
  return null;
};
```

#### `calculatePosition()`
- Determines whether to use external or internal positioning
- Calculates exact pixel positions based on viewport and guidepopup dimensions
- Handles edge cases and viewport boundaries

### State Management
```typescript
const [popupPosition, setPopupPosition] = useState<{
  position: 'external' | 'internal';
  top: number;
  left: number;
  right?: number;
}>({
  position: 'external',
  top: 55,
  left: 0,
  right: 0
});
```

### Event Listeners
- **Window resize**: Recalculates position when viewport changes
- **Window scroll**: Updates position during page scroll
- **ResizeObserver**: Monitors guidepopup size changes
- **MutationObserver**: Detects guidepopup style/class changes

## Styling Changes

### External Positioning
- **Shadow**: `-2px 0 8px rgba(0, 0, 0, 0.2)` (left shadow)
- **Border radius**: `0` (full height sidebar appearance)
- **Z-index**: `1000`

### Internal Positioning
- **Shadow**: `0 4px 12px rgba(0, 0, 0, 0.15)` (floating appearance)
- **Border radius**: `8px` (rounded corners)
- **Border**: `1px solid rgba(95, 158, 160, 0.2)` (subtle border)
- **Z-index**: `1001` (above external elements)
- **Max height**: `500px` (constrained when internal)

### Transitions
- **Duration**: `0.3s`
- **Easing**: `cubic-bezier(0.4, 0, 0.2, 1)` (Material Design standard)
- **Properties**: `left`, `box-shadow`, `border-radius`

## Testing
- Unit tests included in `__tests__/ImageGenerationPopup.test.tsx`
- Tests cover external/internal positioning logic
- Tests verify responsive behavior
- Tests ensure proper cleanup of event listeners

## Browser Compatibility
- **Modern browsers**: Full support with ResizeObserver and MutationObserver
- **Fallback**: Graceful degradation to basic positioning if observers unavailable
- **Performance**: Debounced updates to prevent excessive recalculations

## Usage
The component automatically handles positioning without requiring additional props or configuration. The adaptive behavior is transparent to parent components and maintains backward compatibility.
