{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\AI\\\\TrainingField.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './EnableAIButton.css';\nimport { useTranslation } from 'react-i18next';\nimport { Dialog, DialogContent, DialogContentText, Button, TextField, DialogActions } from '@mui/material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrainingField = ({\n  setShowtrainingField,\n  showtrainingField,\n  handleEnableAI\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [data, setData] = useState('');\n  const handleClick = () => {\n    setShowtrainingField(false);\n    handleEnableAI();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showtrainingField,\n      onClose: () => setShowtrainingField(false),\n      PaperProps: {\n        style: {\n          borderRadius: \"4px\",\n          width: \"400px\",\n          textAlign: \"center\",\n          height: \"188px\",\n          boxShadow: \"none\"\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          padding: \"20px !important\"\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          style: {\n            fontSize: \"14px\",\n            color: \"#000\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"training-name-input\",\n            style: {\n              fontSize: \"17px\"\n            },\n            children: translate('Training Name')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            id: \"training-name-input\",\n            style: {\n              marginTop: \"16px\"\n            },\n            placeholder: translate(\"Name\"),\n            value: data,\n            onChange: e => {\n              const newValue = e.target.value;\n              setData(newValue);\n            },\n            fullWidth: true,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          justifyContent: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClick,\n          sx: {\n            backgroundColor: \"var(--primarycolor)\",\n            color: \"#FFF\",\n            borderRadius: \"8px\",\n            textTransform: \"capitalize\",\n            padding: \"var(--button-padding)\",\n            lineHeight: \"var(--button-lineheight)\"\n            // \"&:hover\": {\n            // \tbackgroundColor: \"#D32F2F\",\n            // },\n          },\n          children: translate(\"Start\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(TrainingField, \"IdHjImh1JBPZ0BrAJ7wjNK485Oo=\", false, function () {\n  return [useTranslation];\n});\n_c = TrainingField;\nexport default TrainingField;\nvar _c;\n$RefreshReg$(_c, \"TrainingField\");", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "<PERSON><PERSON>", "TextField", "DialogActions", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrainingField", "setShowtrainingField", "showtrainingField", "handleEnableAI", "_s", "t", "translate", "data", "setData", "handleClick", "children", "open", "onClose", "PaperProps", "style", "borderRadius", "width", "textAlign", "height", "boxShadow", "sx", "padding", "fontSize", "color", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "marginTop", "placeholder", "value", "onChange", "e", "newValue", "target", "fullWidth", "required", "justifyContent", "onClick", "backgroundColor", "textTransform", "lineHeight", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/AI/TrainingField.tsx"], "sourcesContent": ["import React, { useContext,useState } from 'react';\r\nimport { stopScraping } from '../../services/ScrapingService';\r\nimport './EnableAIButton.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport { useTranslation } from 'react-i18next';\r\nimport {\r\n    Dialog,\r\n\tDialogContent,\r\n\tInputAdornment,\r\n\tDialogContentText,\r\n    Grid,\r\n    Box,\r\n    Button,\r\n    Container,\r\n    TextField,\r\n    DialogTitle,\r\n    DialogActions,\r\n    FormControl,\r\n    InputLabel,\r\n    Select,\r\n    MenuItem,\r\n    IconButton,\r\n    Tooltip,\r\n    Alert,\r\n    Chip\r\n} from '@mui/material';\r\n\r\nconst TrainingField = ({ setShowtrainingField, showtrainingField, handleEnableAI }: { setShowtrainingField: any; showtrainingField: any; handleEnableAI:any}) => {\r\n    const { t: translate } = useTranslation()\r\n    const [data, setData] = useState('');\r\n    const handleClick = () =>\r\n    {\r\n        setShowtrainingField(false);\r\n        handleEnableAI();\r\n\r\n        }\r\n    return (\r\n      <>\r\n    \r\n            \r\n            <Dialog\r\n\t\t\t\topen={showtrainingField}\r\n\t\t\t\tonClose={() => setShowtrainingField(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\twidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\theight: \"188px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\r\n                \r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n                    <DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n                        <label htmlFor=\"training-name-input\" style={{ fontSize: \"17px\" }}>{translate('Training Name')}</label>\r\n                        <TextField\r\n                            id=\"training-name-input\"\r\n                            style={{marginTop:\"16px\"}}\r\n                            placeholder={translate(\"Name\")}\r\n                            value={data}\r\n                            onChange={(e) => {\r\n                                const newValue = e.target.value;\r\n                                \r\n                                    setData(newValue);\r\n                                \r\n                            }}\r\n                            \r\n                            fullWidth\r\n                            required\r\n                        />\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"center\"}}>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleClick}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n                        {translate(\"Start\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n            </>\r\n  );\r\n};\r\n\r\nexport default TrainingField;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,QAAQ,QAAQ,OAAO;AAElD,OAAO,sBAAsB;AAE7B,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACIC,MAAM,EACTC,aAAa,EAEbC,iBAAiB,EAGdC,MAAM,EAENC,SAAS,EAETC,aAAa,QASV,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvB,MAAMC,aAAa,GAAGA,CAAC;EAAEC,oBAAoB;EAAEC,iBAAiB;EAAEC;AAAyF,CAAC,KAAK;EAAAC,EAAA;EAC7J,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAMqB,WAAW,GAAGA,CAAA,KACpB;IACIR,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,cAAc,CAAC,CAAC;EAEhB,CAAC;EACL,oBACEN,OAAA,CAAAE,SAAA;IAAAW,QAAA,eAGMb,OAAA,CAACP,MAAM;MACfqB,IAAI,EAAET,iBAAkB;MACxBU,OAAO,EAAEA,CAAA,KAAMX,oBAAoB,CAAC,KAAK,CAAE;MAC3CY,UAAU,EAAE;QACXC,KAAK,EAAE;UACNC,YAAY,EAAE,KAAK;UACnBC,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,QAAQ;UACnBC,MAAM,EAAE,OAAO;UACfC,SAAS,EAAE;QACZ;MACD,CAAE;MAAAT,QAAA,gBAIFb,OAAA,CAACN,aAAa;QAAC6B,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAkB,CAAE;QAAAX,QAAA,eAClCb,OAAA,CAACL,iBAAiB;UAACsB,KAAK,EAAE;YAAEQ,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,gBAC1Db,OAAA;YAAO2B,OAAO,EAAC,qBAAqB;YAACV,KAAK,EAAE;cAAEQ,QAAQ,EAAE;YAAO,CAAE;YAAAZ,QAAA,EAAEJ,SAAS,CAAC,eAAe;UAAC;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtG/B,OAAA,CAACH,SAAS;YACNmC,EAAE,EAAC,qBAAqB;YACxBf,KAAK,EAAE;cAACgB,SAAS,EAAC;YAAM,CAAE;YAC1BC,WAAW,EAAEzB,SAAS,CAAC,MAAM,CAAE;YAC/B0B,KAAK,EAAEzB,IAAK;YACZ0B,QAAQ,EAAGC,CAAC,IAAK;cACb,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAACJ,KAAK;cAE3BxB,OAAO,CAAC2B,QAAQ,CAAC;YAEzB,CAAE;YAEFE,SAAS;YACTC,QAAQ;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEhB/B,OAAA,CAACF,aAAa;QAACyB,EAAE,EAAE;UAAEmB,cAAc,EAAE;QAAQ,CAAE;QAAA7B,QAAA,eAE9Cb,OAAA,CAACJ,MAAM;UACN+C,OAAO,EAAE/B,WAAY;UACrBW,EAAE,EAAE;YACHqB,eAAe,EAAE,qBAAqB;YACtClB,KAAK,EAAE,MAAM;YACbR,YAAY,EAAE,KAAK;YACnB2B,aAAa,EAAE,YAAY;YAC3BrB,OAAO,EAAE,uBAAuB;YAChCsB,UAAU,EAAE;YACZ;YACA;YACA;UACD,CAAE;UAAAjC,QAAA,EAEiBJ,SAAS,CAAC,OAAO;QAAC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC,gBACE,CAAC;AAEf,CAAC;AAACxB,EAAA,CAvEIJ,aAAa;EAAA,QACUX,cAAc;AAAA;AAAAuD,EAAA,GADrC5C,aAAa;AAyEnB,eAAeA,aAAa;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}