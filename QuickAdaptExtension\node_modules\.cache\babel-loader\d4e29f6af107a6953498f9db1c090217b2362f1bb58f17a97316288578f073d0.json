{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Imagesection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport { magicPen } from '../../../assets/icons/icons';\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"./PopupSections.css\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport ImageGenerationPopup from \"./ImageGenerationPopup\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  setImageSrc,\n  setImageName,\n  onDelete,\n  onClone,\n  isCloneDisabled\n}) => {\n  _s();\n  var _imagesContainer$find;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadImage,\n    imagesContainer,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceImage,\n    cloneImageContainer,\n    deleteImageContainer,\n    updateImageContainer,\n    toggleFit,\n    setImageSrc: storeImageSrc\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const [openGenAiImagePopup, setOpenGenAiImagePopup] = useState(false);\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n\n      // Check for double extensions (e.g. file.html.png) or missing/invalid extension\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        setImageSrc(base64Image);\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n    setModelOpen(false);\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      setImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.FileName,\n          id: imageAnchorEl.buttonId,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files3;\n    const file = (_event$target$files3 = event.target.files) === null || _event$target$files3 === void 0 ? void 0 : _event$target$files3[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.name,\n          id: imageAnchorEl.buttonId,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    // @ts-ignore\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n  };\n  const open = Boolean(imageAnchorEl.value);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n    // setModelOpen(true);\n    // setReplaceImage(true);\n  };\n  const currentContainerColor = ((_imagesContainer$find = imagesContainer.find(item => item.id === imageAnchorEl.containerId)) === null || _imagesContainer$find === void 0 ? void 0 : _imagesContainer$find.style.backgroundColor) || \"transparent\";\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n\n    // Call the delete function from the store\n    deleteImageContainer(imageAnchorEl.containerId);\n\n    // Call the onDelete callback if provided\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneImageContainer(imageAnchorEl.containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [imagesContainer.map(item => {\n      var _item$images$, _item$images$2, _item$images$3, _item$style;\n      const imageSrc = (_item$images$ = item.images[0]) === null || _item$images$ === void 0 ? void 0 : _item$images$.url;\n      const imageId = (_item$images$2 = item.images[0]) === null || _item$images$2 === void 0 ? void 0 : _item$images$2.id;\n      const objectFit = ((_item$images$3 = item.images[0]) === null || _item$images$3 === void 0 ? void 0 : _item$images$3.objectFit) || IMG_OBJECT_FIT;\n      const currentSecHeight = (item === null || item === void 0 ? void 0 : (_item$style = item.style) === null || _item$style === void 0 ? void 0 : _item$style.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n      const id = item.id;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-start\",\n          alignItems: \"center\",\n          // padding: \"5px\",\n          margin: \"0px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: item.style.backgroundColor,\n            height: `${item.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n          },\n          children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 9\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: iconTextStyle,\n              component: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: uploadfile\n                },\n                style: {\n                  display: \"inline-block\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                align: \"center\",\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Upload file\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                fontSize: \"14px\"\n              },\n              children: translate(\"Drag & Drop to upload file\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                marginTop: \"8px\",\n                fontSize: \"14px\"\n              },\n              children: translate(\"Or\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 10\n            }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n              value: imageLink,\n              onChange: e => setImageLink(e.target.value),\n              onKeyDown: handleLinkSubmit,\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 11\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: iconRowStyle,\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      pointerEvents: \"auto\",\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: hyperlink\n                      },\n                      style: {\n                        color: \"black\",\n                        cursor: \"pointer\",\n                        fontSize: \"32px\",\n                        opacity: \"0.5\",\n                        pointerEvents: \"none\"\n                      },\n                      id: \"hyperlink\",\n                      className: \"qadpt-image-upload\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 5\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 3\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    onClick: event => {\n                      //setModelOpen(true);\n                    },\n                    dangerouslySetInnerHTML: {\n                      __html: files\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\",\n                      opacity: \"0.5\"\n                    },\n                    id: \"folder\",\n                    className: \"qadpt-image-upload\"\n                    //title=\"Coming Soon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Upload File\"),\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    onClick: event => {\n                      var _document$getElementB2;\n                      event === null || event === void 0 ? void 0 : event.stopPropagation();\n                      (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n                    },\n                    id: \"file-upload1\",\n                    className: \"qadpt-image-upload\",\n                    dangerouslySetInnerHTML: {\n                      __html: uploadicon\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"file-upload\",\n                  style: {\n                    display: \"none\"\n                  },\n                  accept: \"image/*\",\n                  onChange: handleImageUpload\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n                  open: snackbarOpen,\n                  autoHideDuration: 3000,\n                  onClose: closeSnackbar,\n                  anchorOrigin: {\n                    vertical: 'bottom',\n                    horizontal: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Alert, {\n                    onClose: closeSnackbar,\n                    severity: snackbarSeverity,\n                    sx: {\n                      width: '100%'\n                    },\n                    children: snackbarMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 13\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-genai-container\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: event => {\n                    event.stopPropagation(); // Prevent the parent onClick from firing\n\n                    // Set up the imageAnchorEl context for AI generation\n                    setImageAnchorEl({\n                      buttonId: '',\n                      // Empty for new image generation\n                      containerId: id,\n                      // Use the current container ID\n                      value: null\n                    });\n                    setReplaceImage(false); // This is for new image, not replacement\n                    setOpenGenAiImagePopup(true);\n                  },\n                  className: \"qadpt-genai-btn\",\n                  children: translate(\"Generate With AI\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 7\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 6\n      }, this);\n    }), Boolean(imageAnchorEl.value) ? /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-imgsec-popover\",\n      id: id,\n      open: open,\n      anchorEl: imageAnchorEl.value,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"20px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"6px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: replaceimageicon\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            sx: {\n              cursor: \"pointer\"\n            },\n            onClick: triggerImageUpload,\n            children: translate(\"Replace\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"replace-upload\",\n            style: {\n              display: \"none\"\n            },\n            accept: \"image/*\",\n            onChange: handleReplaceImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Generated Image with AI\"),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => {\n                var _imagesContainer$find2, _imagesContainer$find3, _imagesContainer$find4;\n                setImageAnchorEl({\n                  buttonId: currentImageSectionInfo.currentContainerId ? (_imagesContainer$find2 = (_imagesContainer$find3 = imagesContainer.find(c => c.id === currentImageSectionInfo.currentContainerId)) === null || _imagesContainer$find3 === void 0 ? void 0 : (_imagesContainer$find4 = _imagesContainer$find3.images[0]) === null || _imagesContainer$find4 === void 0 ? void 0 : _imagesContainer$find4.id) !== null && _imagesContainer$find2 !== void 0 ? _imagesContainer$find2 : '' : '',\n                  containerId: currentImageSectionInfo.currentContainerId,\n                  value: null\n                });\n                setReplaceImage(true);\n                setOpenGenAiImagePopup(true);\n              },\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: \"20px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: blackMagicPen\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 7\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 5\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 3\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 1\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              className: \"qadpt-imgset\",\n              open: openSettingsPopover,\n              anchorEl: settingsAnchorEl,\n              onClose: handleCloseSettingsPopover,\n              anchorOrigin: {\n                vertical: \"center\",\n                horizontal: \"right\"\n              },\n              transformOrigin: {\n                vertical: \"center\",\n                horizontal: \"left\"\n              },\n              slotProps: {\n                paper: {\n                  sx: {\n                    mt: 12,\n                    ml: 20,\n                    width: \"205px\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 730,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 732,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 735,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      // Get current image's objectFit to determine selected state\n                      const currentContainer = imagesContainer.find(c => c.id === imageAnchorEl.containerId);\n                      const currentImage = currentContainer === null || currentContainer === void 0 ? void 0 : currentContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n\n                      // Determine if this button should be selected\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item)\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 762,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: selectedColor,\n                  borderRadius: \"100%\",\n                  width: \"20px\",\n                  height: \"20px\",\n                  display: \"inline-block\",\n                  marginTop: \"-3px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  marginTop: \"-3px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 5\n    }, this) : null, isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp,\n      setFormOfUpload: setFormOfUpload,\n      formOfUpload: formOfUpload,\n      handleReplaceImage: handleReplaceImage,\n      isReplaceImage: isReplaceImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 4\n    }, this), openGenAiImagePopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(ImageGenerationPopup, {\n        openGenAiImagePopup: openGenAiImagePopup,\n        setOpenGenAiImagePopup: setOpenGenAiImagePopup,\n        handleImageUploadFormApp: handleImageUploadFormApp,\n        setReplaceImage: setReplaceImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 7\n      }, this)\n    }, void 0, false)]\n  }, void 0, true);\n};\n_s(ImageSection, \"AYOFVU7iWIQZFZcYxrwHKLHjrgM=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "magicPen", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "ImageGenerationPopup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "_s", "_imagesContainer$find", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "openGenAiImagePopup", "setOpenGenAiImagePopup", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "blackMagicPen", "replace", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "type", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "transform<PERSON><PERSON>in", "_imagesContainer$find2", "_imagesContainer$find3", "_imagesContainer$find4", "c", "size", "disabled", "slotProps", "paper", "mt", "ml", "p", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n\tBox,\r\n\tTypography,\r\n\tPopover,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert,\r\n} from \"@mui/material\";\r\nimport {  magicPen } from '../../../assets/icons/icons';\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport CachedIcon from '@mui/icons-material/Cached';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\nimport ImageGenerationPopup from \"./ImageGenerationPopup\"\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\tconst [openGenAiImagePopup, setOpenGenAiImagePopup] = useState(false);\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n   \t\t // Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t\t// setModelOpen(true);\r\n\t\t// setReplaceImage(true);\r\n\t};\r\n\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Call the delete function from the store\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\t// Check if cloning is disabled due to section limits\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\r\n\t\t// Call the clone function from the store\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onClone callback if provided\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\tconst blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t// padding: \"5px\",\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t className=\"qadpt-genai-container\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\tevent.stopPropagation(); // Prevent the parent onClick from firing\r\n\r\n\t\t\t\t\t\t\t\t\t\t// Set up the imageAnchorEl context for AI generation\r\n\t\t\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\t\t\tbuttonId: '', // Empty for new image generation\r\n\t\t\t\t\t\t\t\t\t\t\tcontainerId: id, // Use the current container ID\r\n\t\t\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\tsetReplaceImage(false); // This is for new image, not replacement\r\n\t\t\t\t\t\t\t\t\t\tsetOpenGenAiImagePopup(true);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-genai-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t {translate(\"Generate With AI\")}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={imageAnchorEl.value}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"20px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n  className=\"qadpt-tool-items\"\r\n  sx={{ display: \"flex\", alignItems: \"center\", gap: \"6px\" }}\r\n>\r\n  <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n  <Typography\r\n    fontSize=\"12px\"\r\n    sx={{ cursor: \"pointer\" }}\r\n    onClick={triggerImageUpload}\r\n  >\r\n    {translate(\"Replace\")}\r\n  </Typography>\r\n  <input\r\n    type=\"file\"\r\n    id=\"replace-upload\"\r\n    style={{ display: \"none\" }}\r\n    accept=\"image/*\"\r\n    onChange={handleReplaceImage}\r\n  />\r\n  <Tooltip title={translate(\"Generated Image with AI\")}>\r\n    <IconButton\r\n      onClick={() => {\r\n        setImageAnchorEl({\r\n          buttonId: currentImageSectionInfo.currentContainerId\r\n            ? imagesContainer.find(c => c.id === currentImageSectionInfo.currentContainerId)?.images[0]?.id ?? ''\r\n            : '',\r\n          containerId: currentImageSectionInfo.currentContainerId,\r\n          value: null,\r\n        });\r\n        setReplaceImage(true);\r\n        setOpenGenAiImagePopup(true);\r\n      }}\r\n      size=\"small\"\r\n    >\r\n      <span style={{fontSize:\"20px\"}} dangerouslySetInnerHTML={{ __html: blackMagicPen }} />\r\n    </IconButton>\r\n  </Tooltip>\r\n</Box>\r\n\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={settingsAnchorEl}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tmt: 12,\r\n\t\t\t\t\t\t\t\t\t\tml: 20,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t    display: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\tmarginTop:\"-3px\"\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")}>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} style={{ marginTop: \"-3px\" }}/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\r\n\t\t\t{\r\n\t\t\t\topenGenAiImagePopup && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<ImageGenerationPopup openGenAiImagePopup={openGenAiImagePopup} setOpenGenAiImagePopup={setOpenGenAiImagePopup} handleImageUploadFormApp={handleImageUploadFormApp} setReplaceImage={setReplaceImage} />\r\n\t\t\t\t\t</>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,QAAQ,QAAQ,OAAO;AAClD,SACCC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACC,eAAe;AACtB,SAAUC,QAAQ,QAAQ,6BAA6B;AACvD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAK9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,QACR,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,qBAAqB;AAC5B,OAAOC,0BAA0B,MAAM,yCAAyC;AAChF,OAAOC,oBAAoB,MAAM,wBAAwB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLkC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,SAAS;IACTjB,WAAW,EAAEkB;EACd,CAAC,GAAGhC,cAAc,CAAEiC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAMmE,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAkD;IAC7GwE,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC4E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7E,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC8E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG/E,QAAQ,CAInE;IAAEwE,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEvD;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACoF,WAAW,EAAEC,YAAY,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAAC4F,cAAc,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EAGrE,MAAMgG,mBAAmB,GAAGC,OAAO,CAACT,gBAAgB,CAAC;EAErD,MAAMU,kBAAkB,GAAIC,KAAU,IAAK;IAC1ChB,iBAAiB,CAACgB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrEV,mBAAmB,CAACU,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxCf,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMgB,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACbzB,MAAM,EAAE,MAAM;IACd0B,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCP,KAAK,EAAE,MAAM;IACbzB,MAAM,EAAE,MAAM;IACd8B,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBF,KAAK,EAAE;EACR,CAAC;EAED,MAAMc,iBAAiB,GAAIrB,KAA0C,IAAK;IAAA,IAAAsB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGtB,KAAK,CAACC,MAAM,CAACnF,KAAK,cAAAwG,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MAAA,IAAAC,oBAAA;MACT,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;;MAE7B;MACC,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvCjE,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrBuC,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BnE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrBuC,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACD7D,YAAY,EAAAmF,oBAAA,GAACxB,KAAK,CAACC,MAAM,CAACnF,KAAK,cAAA0G,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC;MAE1C,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3C7E,aAAa,CAAC4E,WAAW,CAAC;QAC1B9F,WAAW,CAAC8F,WAAW,CAAC;QACxBrF,WAAW,CAACE,aAAa,CAACqF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEP,WAAW;UAChBQ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEjH;QACZ,CAAC,CAAC;MACH,CAAC;MACDqG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;IACArC,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM2D,wBAAwB,GAAItB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACTjE,aAAa,CAACiE,IAAI,CAACuB,GAAG,CAAC;MACvB1G,WAAW,CAACmF,IAAI,CAACuB,GAAG,CAAC;MACrB,IAAIrD,cAAc,EAAE;QACnBxC,YAAY,CAACF,aAAa,CAACqF,WAAW,EAAErF,aAAa,CAACgG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEvF,aAAa,CAACgG,QAAQ;UAC1BN,GAAG,EAAElB,IAAI,CAACuB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEjH;QACZ,CAAC,CAAC;QACFgE,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACN7C,WAAW,CAACE,aAAa,CAACqF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAElB,IAAI,CAACuB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEjH;QACZ,CAAC,CAAC;MACH;IACD;IACAwD,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,MAAM+D,kBAAkB,GAAIjD,KAA0C,IAAK;IAAA,IAAAkD,oBAAA;IAC1E,MAAM3B,IAAI,IAAA2B,oBAAA,GAAGlD,KAAK,CAACC,MAAM,CAACnF,KAAK,cAAAoI,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI3B,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxBhF,YAAY,CAACF,aAAa,CAACqF,WAAW,EAAErF,aAAa,CAACgG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEvF,aAAa,CAACgG,QAAQ;UAC1BN,GAAG,EAAEV,MAAM,CAACI,MAAM;UAClBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEjH;QACZ,CAAC,CAAC;MACH,CAAC;MACDqG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM4B,WAAW,GAAGA,CACnBnD,KAAoC,EACpCoC,WAAmB,EACnBgB,OAAe,EACfvE,OAAgB,EAChBwE,aAAqB,KACjB;IACJ;IACA,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,EAAE;IAC5DtF,gBAAgB,CAAC;MAChB+F,QAAQ,EAAEK,OAAO;MACjBhB,WAAW,EAAEA,WAAW;MACxB;MACAlC,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFd,mBAAmB,CAAC,IAAI,CAAC;IACzBV,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAE+D,WAAW;MAC/BvD,OAAO;MACPC,MAAM,EAAEuE;IACT,CAAC,CAAC;IACFjF,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAMiF,WAAW,GAAGA,CAAA,KAAM;IACzBvG,gBAAgB,CAAC;MAChB+F,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EAED,MAAMsD,IAAI,GAAG1D,OAAO,CAAC/C,aAAa,CAACmD,KAAK,CAAC;EACzC,MAAMuD,eAAe,GAAG3D,OAAO,CAACrB,mBAAmB,CAAC;EAEpD,MAAM6D,EAAE,GAAGkB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIpI,wBAAwB,EAAE;IAC5C,MAAMqI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAGjI,cAAc,EAAEH,wBAAwB,CAAC;IACjF4B,oBAAoB,CAACL,aAAa,CAACqF,WAAW,EAAE,OAAO,EAAE;MACxDtD,MAAM,EAAE+E;IACT,CAAC,CAAC;IACFjF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAE+E;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAInI,wBAAwB,EAAE;IAC5C,MAAMoI,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAGjI,cAAc,EAAEF,wBAAwB,CAAC;IACjF2B,oBAAoB,CAACL,aAAa,CAACqF,WAAW,EAAE,OAAO,EAAE;MACxDtD,MAAM,EAAE+E;IACT,CAAC,CAAC;IACFjF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAE+E;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;IAClD;IACA;EACD,CAAC;EAED,MAAMC,qBAAqB,GAC1B,EAAA7H,qBAAA,GAAAI,eAAe,CAAC0H,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAKvF,aAAa,CAACqF,WAAW,CAAC,cAAA1F,qBAAA,uBAArEA,qBAAA,CAAuEgI,KAAK,CAAChC,eAAe,KAAI,aAAa;EAC9G;EACA,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IACjC3H,gBAAgB,CAAC;MAChB+F,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACA/C,oBAAoB,CAACJ,aAAa,CAACqF,WAAW,CAAC;;IAE/C;IACA,IAAI9F,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EAGD,MAAMsI,gBAAgB,GAAI5E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC6E,GAAG,KAAK,OAAO,IAAItG,SAAS,EAAE;MACvC1B,WAAW,CAACE,aAAa,CAACqF,WAAW,EAAE;QACtCC,OAAO,EAAE,WAAW;QACpBC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAElE,SAAS;QACdmE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAEjH;MACZ,CAAC,CAAC;MACF0C,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAMwG,uBAAuB,GAAGA,CAAA,KAAM;IACrC;IACA,IAAItI,eAAe,EAAE;MACpB,OAAO,CAAC;IACT;;IAEA;IACAU,mBAAmB,CAACH,aAAa,CAACqF,WAAW,CAAC;;IAE9C;IACA,IAAI7F,OAAO,EAAE;MACZA,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EAED,MAAMwI,sBAAsB,GAAGA,CAAA,KAAM;IACpCrG,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsG,iBAAiB,GAAIC,KAAkB,IAAK;IACjDzF,gBAAgB,CAACyF,KAAK,CAACC,GAAG,CAAC;IAC3B9H,oBAAoB,CAACL,aAAa,CAACqF,WAAW,EAAE,OAAO,EAAE;MACxDM,eAAe,EAAEuC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,0BAA0B,GAAInF,KAAoC,IAAK;IAC5EtB,sBAAsB,CAACsB,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EACD,MAAMgF,aAAa,GAAG5K,QAAQ,CAAC6K,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAG3E,oBACCrJ,OAAA,CAAAE,SAAA;IAAAoJ,QAAA,GACExI,eAAe,CAACyI,GAAG,CAAEd,IAAI,IAAK;MAAA,IAAAe,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,WAAA;MAC9B,MAAMC,QAAQ,IAAAJ,aAAA,GAAGf,IAAI,CAACoB,MAAM,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAdA,aAAA,CAAgB/C,GAAG;MACpC,MAAMW,OAAO,IAAAqC,cAAA,GAAGhB,IAAI,CAACoB,MAAM,CAAC,CAAC,CAAC,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBnD,EAAE;MAClC,MAAMK,SAAS,GAAG,EAAA+C,cAAA,GAAAjB,IAAI,CAACoB,MAAM,CAAC,CAAC,CAAC,cAAAH,cAAA,uBAAdA,cAAA,CAAgB/C,SAAS,KAAIjH,cAAc;MAC7D,MAAMoK,gBAAgB,GAAG,CAACrB,IAAI,aAAJA,IAAI,wBAAAkB,WAAA,GAAJlB,IAAI,CAAEC,KAAK,cAAAiB,WAAA,uBAAXA,WAAA,CAAa7G,MAAM,KAAevD,4BAA4B;MACxF,MAAM+G,EAAE,GAAGmC,IAAI,CAACnC,EAAE;MAClB,oBACCtG,OAAA,CAAClC,GAAG;QAEHiM,EAAE,EAAE;UACHxF,KAAK,EAAE,MAAM;UACbzB,MAAM,EAAE,MAAM;UACd0B,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,YAAY;UAC5BC,UAAU,EAAE,QAAQ;UACpB;UACAE,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACX,CAAE;QAAAyE,QAAA,eAEFtJ,OAAA,CAAClC,GAAG;UACHiM,EAAE,EAAE;YACH,GAAGzF,mBAAmB;YACtBoC,eAAe,EAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe;YAC3C5D,MAAM,EAAE,GAAG2F,IAAI,CAACC,KAAK,CAAC5F,MAAM;UAC7B,CAAE;UACFkH,OAAO,EAAGC,CAAC,IAAK9C,WAAW,CAAC8C,CAAC,EAAE3D,EAAE,EAAEc,OAAO,EAAEwC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEE,gBAAgB,CAAE;UACvFI,SAAS,EAAE,KAAM;UACjB5D,EAAE,EAAEA,EAAG;UACP6D,WAAW,EAAEA,CAAA,KAAM;YAClBnJ,gBAAgB,CAAC;cAChB+F,QAAQ,EAAEK,OAAO;cACjBhB,WAAW,EAAEE,EAAE;cACfpC,KAAK,EAAE;YACR,CAAC,CAAC;UACH,CAAE;UAAAoF,QAAA,EAEDM,QAAQ,gBACR5J,OAAA;YACCoK,GAAG,EAAER,QAAS;YACdS,GAAG,EAAC,UAAU;YACd3B,KAAK,EAAE;cAAE,GAAG5D,UAAU;cAAE6B;YAAU;UAAE;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEFzK,OAAA,CAAClC,GAAG;YACHiM,EAAE,EAAE;cACHW,SAAS,EAAE,QAAQ;cACnBnG,KAAK,EAAE,MAAM;cACbzB,MAAM,EAAE,MAAM;cACd0B,OAAO,EAAE,MAAM;cACfY,aAAa,EAAE,QAAQ;cACvBX,cAAc,EAAE;YACjB,CAAE;YAAA6E,QAAA,gBAEFtJ,OAAA,CAAClC,GAAG;cACHiM,EAAE,EAAE5E,aAAc;cAClB+E,SAAS,EAAE,KAAM;cAAAZ,QAAA,gBAEjBtJ,OAAA;gBACC2K,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhM;gBAAW,CAAE;gBAChD8J,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAe;cAAE;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFzK,OAAA,CAACjC,UAAU;gBACV8M,OAAO,EAAC,IAAI;gBACZC,KAAK,EAAC,QAAQ;gBACdf,EAAE,EAAE;kBAAEgB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EAE1C1I,SAAS,CAAC,aAAa;cAAC;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENzK,OAAA,CAACjC,UAAU;cACV8M,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd7B,KAAK,EAAC,eAAe;cACrBc,EAAE,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEvB1I,SAAS,CAAC,4BAA4B;YAAC;cAAA0J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbzK,OAAA,CAACjC,UAAU;cACV8M,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd7B,KAAK,EAAC,eAAe;cACrBc,EAAE,EAAE;gBAAE7E,SAAS,EAAE,KAAK;gBAAE6F,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEzC1I,SAAS,CAAC,IAAI;YAAC;cAAA0J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACZtI,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAKiE,EAAE,gBACzEtG,OAAA,CAAC9B,SAAS;cACTgG,KAAK,EAAE3B,SAAU;cACjB0I,QAAQ,EAAGhB,CAAC,IAAKzH,YAAY,CAACyH,CAAC,CAAChG,MAAM,CAACC,KAAK,CAAE;cAC9CgH,SAAS,EAAEtC,gBAAiB;cAC5BuC,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEAzK,OAAA,CAAClC,GAAG;cAAAwL,QAAA,gBACHtJ,OAAA,CAAClC,GAAG;gBAACiM,EAAE,EAAE/E,YAAa;gBAAAsE,QAAA,gBACtBtJ,OAAA,CAAC3B,OAAO;kBAAC+M,KAAK,EAAExK,SAAS,CAAC,aAAa,CAAE;kBAAA0I,QAAA,eACpDtJ,OAAA;oBAAK0I,KAAK,EAAE;sBAAE2C,aAAa,EAAE,MAAM;sBAAEC,MAAM,EAAC;oBAAS,CAAE;oBAAAhC,QAAA,eACrDtJ,OAAA;sBACE2K,uBAAuB,EAAE;wBAAEC,MAAM,EAAE/L;sBAAU,CAAE;sBAC/C6J,KAAK,EAAE;wBACLO,KAAK,EAAE,OAAO;wBACdqC,MAAM,EAAE,SAAS;wBACjBP,QAAQ,EAAE,MAAM;wBAChBQ,OAAO,EAAE,KAAK;wBACdF,aAAa,EAAE;sBACjB,CAAE;sBACF/E,EAAE,EAAC,WAAW;sBACPkF,SAAS,EAAC;oBAAoB;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGGzK,OAAA,CAAC3B,OAAO;kBAAC+M,KAAK,EAAExK,SAAS,CAAC,aAAa,CAAE;kBAAA0I,QAAA,eACxCtJ,OAAA;oBACCgK,OAAO,EAAGhG,KAAK,IAAK;sBACnB;oBAAA,CACC;oBACL2G,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9L;oBAAM,CAAE;oBAC3C4J,KAAK,EAAE;sBAAEO,KAAK,EAAE,OAAO;sBAAEqC,MAAM,EAAE,SAAS;sBAAEP,QAAQ,EAAE,MAAM;sBAAEQ,OAAO,EAAE;oBAAM,CAAE;oBAC/EjF,EAAE,EAAC,QAAQ;oBACXkF,SAAS,EAAC;oBACV;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACVzK,OAAA,CAAC3B,OAAO;kBAAC+M,KAAK,EAAExK,SAAS,CAAC,aAAa,CAAE;kBAAA0I,QAAA,eAC3CtJ,OAAA;oBACIgK,OAAO,EAAGhG,KAAK,IAAK;sBAAA,IAAAyH,sBAAA;sBAEtBzH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0H,eAAe,CAAC,CAAC;sBACxB,CAAAD,sBAAA,GAAArD,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAoD,sBAAA,uBAAtCA,sBAAA,CAAwCnD,KAAK,CAAC,CAAC;oBAChD,CAAE;oBACFhC,EAAE,EAAC,cAAc;oBACjBkF,SAAS,EAAC,oBAAoB;oBAC9Bb,uBAAuB,EAAE;sBAAEC,MAAM,EAAE7L;oBAAW,CAAE;oBAChD2J,KAAK,EAAE;sBAAEO,KAAK,EAAE,OAAO;sBAAEqC,MAAM,EAAE,SAAS;sBAAEP,QAAQ,EAAE;oBAAO;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACbzK,OAAA;kBACC2L,IAAI,EAAC,MAAM;kBACXrF,EAAE,EAAC,aAAa;kBAChBoC,KAAK,EAAE;oBAAElE,OAAO,EAAE;kBAAO,CAAE;kBAC3BoH,MAAM,EAAC,SAAS;kBAChBX,QAAQ,EAAE5F;gBAAkB;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACFzK,OAAA,CAAC1B,QAAQ;kBAACkJ,IAAI,EAAEhG,YAAa;kBAACqK,gBAAgB,EAAE,IAAK;kBAACC,OAAO,EAAE5J,aAAc;kBAAC6J,YAAY,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA3C,QAAA,eACxItJ,OAAA,CAACzB,KAAK;oBAACuN,OAAO,EAAE5J,aAAc;oBAACgK,QAAQ,EAAEtK,gBAAiB;oBAACmI,EAAE,EAAE;sBAAExF,KAAK,EAAE;oBAAO,CAAE;oBAAA+E,QAAA,EAC/E5H;kBAAe;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACVzK,OAAA,CAAClC,GAAG;gBACH0N,SAAS,EAAC,uBAAuB;gBAAAlC,QAAA,eAElCtJ,OAAA;kBACCgK,OAAO,EAAGhG,KAAK,IAAK;oBACpBA,KAAK,CAAC0H,eAAe,CAAC,CAAC,CAAC,CAAC;;oBAEzB;oBACA1K,gBAAgB,CAAC;sBAChB+F,QAAQ,EAAE,EAAE;sBAAE;sBACdX,WAAW,EAAEE,EAAE;sBAAE;sBACjBpC,KAAK,EAAE;oBACR,CAAC,CAAC;oBACFR,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxBE,sBAAsB,CAAC,IAAI,CAAC;kBAC5B,CAAE;kBACG4H,SAAS,EAAC,iBAAiB;kBAAAlC,QAAA,EAG9B1I,SAAS,CAAC,kBAAkB;gBAAC;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GA5KDnE,EAAE;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6KH,CAAC;IAER,CAAC,CAAC,EACD3G,OAAO,CAAC/C,aAAa,CAACmD,KAAK,CAAC,gBAC5BlE,OAAA,CAAChC,OAAO;MACPwN,SAAS,EAAC,sBAAsB;MAChClF,EAAE,EAAEA,EAAG;MACPkB,IAAI,EAAEA,IAAK;MACX2E,QAAQ,EAAEpL,aAAa,CAACmD,KAAM;MAC9B4H,OAAO,EAAEvE,WAAY;MACrBwE,YAAY,EAAE;QACbC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFG,eAAe,EAAE;QAChBJ,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MAAA3C,QAAA,eAEFtJ,OAAA,CAAClC,GAAG;QACHiM,EAAE,EAAE;UACHvF,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXnC,MAAM,EAAE,MAAM;UACd6B,OAAO,EAAE,QAAQ;UACjBoG,QAAQ,EAAE;QACX,CAAE;QAAAzB,QAAA,gBAEFtJ,OAAA,CAAClC,GAAG;UACR0N,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAEvF,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE;UAAM,CAAE;UAAAqE,QAAA,gBAE1DtJ,OAAA;YAAM2K,uBAAuB,EAAE;cAAEC,MAAM,EAAE5L;YAAiB;UAAE;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DzK,OAAA,CAACjC,UAAU;YACTgN,QAAQ,EAAC,MAAM;YACfhB,EAAE,EAAE;cAAEuB,MAAM,EAAE;YAAU,CAAE;YAC1BtB,OAAO,EAAE9B,kBAAmB;YAAAoB,QAAA,EAE3B1I,SAAS,CAAC,SAAS;UAAC;YAAA0J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACbzK,OAAA;YACE2L,IAAI,EAAC,MAAM;YACXrF,EAAE,EAAC,gBAAgB;YACnBoC,KAAK,EAAE;cAAElE,OAAO,EAAE;YAAO,CAAE;YAC3BoH,MAAM,EAAC,SAAS;YAChBX,QAAQ,EAAEhE;UAAmB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFzK,OAAA,CAAC3B,OAAO;YAAC+M,KAAK,EAAExK,SAAS,CAAC,yBAAyB,CAAE;YAAA0I,QAAA,eACnDtJ,OAAA,CAAC/B,UAAU;cACT+L,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAqC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;gBACbvL,gBAAgB,CAAC;kBACf+F,QAAQ,EAAEpE,uBAAuB,CAACN,kBAAkB,IAAAgK,sBAAA,IAAAC,sBAAA,GAChDxL,eAAe,CAAC0H,IAAI,CAACgE,CAAC,IAAIA,CAAC,CAAClG,EAAE,KAAK3D,uBAAuB,CAACN,kBAAkB,CAAC,cAAAiK,sBAAA,wBAAAC,sBAAA,GAA9ED,sBAAA,CAAgFzC,MAAM,CAAC,CAAC,CAAC,cAAA0C,sBAAA,uBAAzFA,sBAAA,CAA2FjG,EAAE,cAAA+F,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GACnG,EAAE;kBACNjG,WAAW,EAAEzD,uBAAuB,CAACN,kBAAkB;kBACvD6B,KAAK,EAAE;gBACT,CAAC,CAAC;gBACFR,eAAe,CAAC,IAAI,CAAC;gBACrBE,sBAAsB,CAAC,IAAI,CAAC;cAC9B,CAAE;cACF6I,IAAI,EAAC,OAAO;cAAAnD,QAAA,eAEZtJ,OAAA;gBAAM0I,KAAK,EAAE;kBAACqC,QAAQ,EAAC;gBAAM,CAAE;gBAACJ,uBAAuB,EAAE;kBAAEC,MAAM,EAAExB;gBAAc;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNzK,OAAA,CAAClC,GAAG;UACG0N,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAEvF,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAA4E,QAAA,gBAE9CtJ,OAAA;YAAM2K,uBAAuB,EAAE;cAAEC,MAAM,EAAEzL;YAAc;UAAE;YAAAmL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DzK,OAAA,CAAC3B,OAAO;YAAC+M,KAAK,EAAEzI,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAGmB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA0I,QAAA,eAC/ItJ,OAAA;cAAAsJ,QAAA,eACCtJ,OAAA,CAAC/B,UAAU;gBACV+L,OAAO,EAAEA,CAAA,KAAMhC,oBAAoB,CAACrF,uBAAuB,CAACG,MAAM,CAAE;gBACpE2J,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAE/J,uBAAuB,CAACG,MAAM,IAAIrD,wBAAyB;gBACrEsK,EAAE,EAAE;kBACHwB,OAAO,EAAE5I,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E6L,MAAM,EAAE3I,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAA6J,QAAA,eAEFtJ,OAAA,CAACvB,UAAU;kBAACsM,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACVzK,OAAA,CAACjC,UAAU;YAACgN,QAAQ,EAAC,MAAM;YAAAzB,QAAA,EAAE3G,uBAAuB,CAACG;UAAM;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzEzK,OAAA,CAAC3B,OAAO;YAAC+M,KAAK,EAAEzI,uBAAuB,CAACG,MAAM,IAAItD,wBAAwB,GAAGoB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA0I,QAAA,eAC/ItJ,OAAA;cAAAsJ,QAAA,eACCtJ,OAAA,CAAC/B,UAAU;gBACV+L,OAAO,EAAEA,CAAA,KAAMrC,oBAAoB,CAAChF,uBAAuB,CAACG,MAAM,CAAE;gBACpE2J,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAE/J,uBAAuB,CAACG,MAAM,IAAItD,wBAAyB;gBACrEuK,EAAE,EAAE;kBACHwB,OAAO,EAAE5I,uBAAuB,CAACG,MAAM,IAAItD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E8L,MAAM,EAAE3I,uBAAuB,CAACG,MAAM,IAAItD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAA8J,QAAA,eAEFtJ,OAAA,CAACtB,OAAO;kBAACqM,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNzK,OAAA,CAAC3B,OAAO;UAAC+M,KAAK,EAAExK,SAAS,CAAC,UAAU,CAAE;UAAA0I,QAAA,eACtCtJ,OAAA,CAAClC,GAAG;YAAAwL,QAAA,gBACHtJ,OAAA,CAAClC,GAAG;cAAC0N,SAAS,EAAC,kBAAkB;cAAAlC,QAAA,eAChCtJ,OAAA,CAAC/B,UAAU;gBACVwO,IAAI,EAAC,OAAO;gBACZzC,OAAO,EAAE7F,mBAAoB;gBAAAmF,QAAA,eAE7BtJ,OAAA;kBACC2K,uBAAuB,EAAE;oBAAEC,MAAM,EAAExL;kBAAS,CAAE;kBAC9CsJ,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAQ;gBAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAELzK,OAAA,CAAChC,OAAO;cACEwN,SAAS,EAAC,cAAc;cAClChE,IAAI,EAAE3D,mBAAoB;cAC1BsI,QAAQ,EAAE9I,gBAAiB;cAC3ByI,OAAO,EAAEzH,0BAA2B;cACpC0H,YAAY,EAAE;gBACbC,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACb,CAAE;cACFG,eAAe,EAAE;gBAChBJ,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACb,CAAE;cACFU,SAAS,EAAE;gBACVC,KAAK,EAAE;kBACP7C,EAAE,EAAE;oBACH8C,EAAE,EAAE,EAAE;oBACNC,EAAE,EAAE,EAAE;oBACNvI,KAAK,EAAE;kBACR;gBACA;cACD,CAAE;cAAA+E,QAAA,eAEFtJ,OAAA,CAAClC,GAAG;gBAACiP,CAAC,EAAE,CAAE;gBAAAzD,QAAA,gBACTtJ,OAAA,CAAClC,GAAG;kBACH0G,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAA4E,QAAA,gBAEnBtJ,OAAA,CAACjC,UAAU;oBACV8M,OAAO,EAAC,WAAW;oBACnBd,EAAE,EAAE;sBAAEd,KAAK,EAAE;oBAAwB,CAAE;oBAAAK,QAAA,EAErC1I,SAAS,CAAC,kBAAkB;kBAAC;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbzK,OAAA,CAAC/B,UAAU;oBACVwO,IAAI,EAAC,OAAO;oBACZzC,OAAO,EAAE3F,0BAA2B;oBAAAiF,QAAA,eAEpCtJ,OAAA;sBACC2K,uBAAuB,EAAE;wBAAEC,MAAM,EAAEvL;sBAAU,CAAE;sBAC/CqJ,KAAK,EAAE;wBAAEO,KAAK,EAAE;sBAAQ;oBAAE;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNzK,OAAA,CAAC3B,OAAO;kBAAC+M,KAAK,EAAExK,SAAS,CAAC,aAAa,CAAE;kBAAA0I,QAAA,eAC1CtJ,OAAA,CAAClC,GAAG;oBAAC+O,EAAE,EAAE,CAAE;oBAAAvD,QAAA,gBACVtJ,OAAA,CAACjC,UAAU;sBACV8M,OAAO,EAAC,OAAO;sBACb5B,KAAK,EAAC,eAAe;sBACrBc,EAAE,EAAE;wBAAEiD,YAAY,EAAE;sBAAO,CAAE;sBAAA1D,QAAA,EAE5B1I,SAAS,CAAC,eAAe;oBAAC;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACbzK,OAAA,CAAC9B,SAAS;sBACT+O,MAAM;sBACNC,SAAS;sBACTrC,OAAO,EAAC,UAAU;sBAClB4B,IAAI,EAAC,OAAO;sBACZvI,KAAK,EAAEnB,cAAe;sBACtBkI,QAAQ,EAAElH,kBAAmB;sBAC7BgG,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3BoD,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFT,QAAQ;sBAAApD,QAAA,gBAENtJ,OAAA,CAAC7B,QAAQ;wBAAC+F,KAAK,EAAC,MAAM;wBAAAoF,QAAA,EAAE1I,SAAS,CAAC,MAAM;sBAAC;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACrDzK,OAAA,CAAC7B,QAAQ;wBAAC+F,KAAK,EAAC,cAAc;wBAAAoF,QAAA,EAAE1I,SAAS,CAAC,eAAe;sBAAC;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtEzK,OAAA,CAAC7B,QAAQ;wBAAC+F,KAAK,EAAC,SAAS;wBAAAoF,QAAA,EAAE1I,SAAS,CAAC,UAAU;sBAAC;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC5DzK,OAAA,CAAC7B,QAAQ;wBAAC+F,KAAK,EAAC,cAAc;wBAAAoF,QAAA,EAAE1I,SAAS,CAAC,eAAe;sBAAC;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtEzK,OAAA,CAAC7B,QAAQ;wBAAC+F,KAAK,EAAC,WAAW;wBAAAoF,QAAA,EAAE1I,SAAS,CAAC,YAAY;sBAAC;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChEzK,OAAA,CAAC7B,QAAQ;wBAAC+F,KAAK,EAAC,kBAAkB;wBAAAoF,QAAA,EAAE1I,SAAS,CAAC,oBAAoB;sBAAC;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACVzK,OAAA,CAAClC,GAAG;kBAAC+O,EAAE,EAAE,CAAE;kBAAAvD,QAAA,gBACVtJ,OAAA,CAACjC,UAAU;oBACV8M,OAAO,EAAC,OAAO;oBACf5B,KAAK,EAAC,eAAe;oBAAAK,QAAA,EAEnB1I,SAAS,CAAC,kBAAkB;kBAAC;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbzK,OAAA,CAAClC,GAAG;oBACH0G,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACP4H,EAAE,EAAE,CAAE;oBAAAvD,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,GAAG,CAAEd,IAAI,IAAK;sBAC9B;sBACA,MAAM2E,gBAAgB,GAAGtM,eAAe,CAAC0H,IAAI,CAAEgE,CAAC,IAAKA,CAAC,CAAClG,EAAE,KAAKvF,aAAa,CAACqF,WAAW,CAAC;sBACxF,MAAMiH,YAAY,GAAGD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEvD,MAAM,CAACrB,IAAI,CAAE8E,GAAG,IAAKA,GAAG,CAAChH,EAAE,KAAKvF,aAAa,CAACgG,QAAQ,CAAC;sBAC9F,MAAMwG,gBAAgB,GAAG,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1G,SAAS,KAAIjH,cAAc;;sBAElE;sBACA,MAAM8N,UAAU,GAAI/E,IAAI,KAAK,MAAM,IAAI8E,gBAAgB,KAAK,OAAO,IAC5D9E,IAAI,KAAK,KAAK,IAAI8E,gBAAgB,KAAK,SAAU;sBAExD,oBACCvN,OAAA,CAAC5B,MAAM;wBAEN4L,OAAO,EAAEA,CAAA,KACR3I,SAAS,CAACN,aAAa,CAACqF,WAAW,EAAErF,aAAa,CAACgG,QAAQ,EAAE0B,IAAsB,CACnF;wBACDoC,OAAO,EAAC,UAAU;wBAClB4B,IAAI,EAAC,OAAO;wBACZ1C,EAAE,EAAE;0BACHxF,KAAK,EAAE,QAAQ;0BACfzB,MAAM,EAAE,MAAM;0BACd6B,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/B0I,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtC9G,eAAe,EACd8G,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BzE,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVvC,eAAe,EACd8G,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAAlE,QAAA,EAED1I,SAAS,CAAC6H,IAAI;sBAAC,GA1BXA,IAAI;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACVzK,OAAA,CAAC3B,OAAO;UAAC+M,KAAK,EAAExK,SAAS,CAAC,kBAAkB,CAAE;UAAA0I,QAAA,eAC9CtJ,OAAA,CAAClC,GAAG;YAAC0N,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCtJ,OAAA,CAAC/B,UAAU;cACV+L,OAAO,EAAEb,0BAA2B;cACpCsD,IAAI,EAAC,OAAO;cAAAnD,QAAA,eAEZtJ,OAAA;gBACA0I,KAAK,EAAE;kBACNhC,eAAe,EAAEnD,aAAa;kBAC9BwB,YAAY,EAAE,MAAM;kBACpBR,KAAK,EAAE,MAAM;kBACbzB,MAAM,EAAE,MAAM;kBACX0B,OAAO,EAAE,cAAc;kBAC1BU,SAAS,EAAC;gBACX;cAAE;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEVzK,OAAA,CAAC3B,OAAO;UAAC+M,KAAK,EAAE5K,eAAe,GAAGI,SAAS,CAAC,2CAA2C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAAA0I,QAAA,eACtHtJ,OAAA,CAAClC,GAAG;YAAC0N,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCtJ,OAAA,CAAC/B,UAAU;cACV+L,OAAO,EAAElB,uBAAwB;cACjC2D,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAElM,eAAgB;cAAA8I,QAAA,eAE1BtJ,OAAA;gBACC2K,uBAAuB,EAAE;kBAAEC,MAAM,EAAE3L;gBAAS,CAAE;gBAC9CyJ,KAAK,EAAE;kBAAE6C,OAAO,EAAE/K,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACVzK,OAAA,CAAC3B,OAAO;UAAC+M,KAAK,EAAExK,SAAS,CAAC,gBAAgB,CAAE;UAAA0I,QAAA,eAE5CtJ,OAAA,CAAClC,GAAG;YAAC0N,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChCtJ,OAAA,CAAC/B,UAAU;cACV+L,OAAO,EAAErB,mBAAoB;cAC7B8D,IAAI,EAAC,OAAO;cAAAnD,QAAA,eAEZtJ,OAAA;gBAAM2K,uBAAuB,EAAE;kBAAEC,MAAM,EAAE1L;gBAAW,CAAE;gBAACwJ,KAAK,EAAE;kBAAExD,SAAS,EAAE;gBAAO;cAAE;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACP,IAAI,EAEPxH,WAAW,iBACVjD,OAAA,CAACH,0BAA0B;MAACyC,MAAM,EAAEW,WAAY;MAAC0K,gBAAgB,EAAEA,CAAA,KAAMzK,YAAY,CAAC,KAAK,CAAE;MAAC0K,aAAa,EAAE/G,wBAAyB;MAACzD,eAAe,EAAEA,eAAgB;MAACD,YAAY,EAAEA,YAAa;MAAC8D,kBAAkB,EAAEA,kBAAmB;MAACxD,cAAc,EAAEA;IAAe;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAC7Q,eAEFzK,OAAA,CAAChC,OAAO;MACPwJ,IAAI,EAAEC,eAAgB;MACtB0E,QAAQ,EAAE1J,mBAAoB;MAC9BqJ,OAAO,EAAE/C,sBAAuB;MAChCgD,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFG,eAAe,EAAE;QAChBJ,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA3C,QAAA,eAEFtJ,OAAA,CAAClC,GAAG;QAAAwL,QAAA,gBACHtJ,OAAA,CAACJ,YAAY;UACZqJ,KAAK,EAAEV,qBAAsB;UAC7B0C,QAAQ,EAAEjC;QAAkB;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACAzK,OAAA;UAAAsJ,QAAA,EACF;AACL;AACA;AACA;AACA;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGT9G,mBAAmB,iBAClB3D,OAAA,CAAAE,SAAA;MAAAoJ,QAAA,eACCtJ,OAAA,CAACF,oBAAoB;QAAC6D,mBAAmB,EAAEA,mBAAoB;QAACC,sBAAsB,EAAEA,sBAAuB;QAACiD,wBAAwB,EAAEA,wBAAyB;QAACnD,eAAe,EAAEA;MAAgB;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC,gBACvM,CACF;EAAA,eAGD,CAAC;AAEL,CAAC;AAAChK,EAAA,CA50BIN,YAAY;EAAA,QACQxB,cAAc,EAYnCW,cAAc;AAAA;AAAAuO,EAAA,GAbb1N,YAAY;AA80BlB,eAAeA,YAAY;AAAC,IAAA0N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}