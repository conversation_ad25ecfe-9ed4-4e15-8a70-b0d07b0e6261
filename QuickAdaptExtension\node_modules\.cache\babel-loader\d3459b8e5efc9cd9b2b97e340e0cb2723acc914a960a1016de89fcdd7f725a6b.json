{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\ImageGenerationPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState, useEffect } from 'react';\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\nimport { Box, Typography } from '@mui/material';\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\nimport { AccountContext } from '../../login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { useSnackbar } from '../guideList/SnackbarContext';\nimport useDrawerStore from '../../../store/drawerStore';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageGenerationPopup = ({\n  openGenAiImagePopup,\n  setOpenGenAiImagePopup,\n  handleImageUploadFormApp,\n  setReplaceImage\n}) => {\n  _s();\n  //const [isOpen, setIsOpen] = useState(false);\n\n  //const togglePopup = () => setIsOpen(!isOpen);\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    openSnackbar\n  } = useSnackbar();\n\n  // Get imageAnchorEl from store to know which image to replace\n  const {\n    imageAnchorEl\n  } = useDrawerStore(state => state);\n  const [isHovered, setIsHovered] = useState(false);\n  const [abortController, setAbortController] = useState(null);\n  const [popupPosition, setPopupPosition] = useState({\n    position: 'external',\n    top: 55,\n    left: 0,\n    right: 0\n  });\n  const [selectedStyle, setSelectedStyle] = useState('Professional');\n  const [selectedColor, setSelectedColor] = useState('Black & White');\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\n  const ratios = ['1:1', '16:9', '9:16'];\n  const [description, setDescription] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [isEnhancing, setIsEnhancing] = useState(false);\n\n  // Function to get guidepopup position and dimensions\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n\n  // Function to calculate adaptive positioning\n  const calculatePosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return;\n    const viewportWidth = window.innerWidth;\n    const popupWidth = 280; // ImageGenerationPopup width\n    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup\n\n    // Calculate if there's enough space for external positioning\n    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;\n    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20; // 20px margin from viewport edge\n\n    if (hasSpaceForExternal) {\n      // External positioning: to the right of guidepopup with gap\n      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;\n      setPopupPosition({\n        position: 'external',\n        top: 55,\n        // Keep consistent with current top positioning\n        left: Math.min(externalLeft, viewportWidth - popupWidth - 10) // Ensure it doesn't overflow viewport\n      });\n    } else {\n      // Internal positioning: align to the right side within guidepopup\n      const internalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15; // 15px padding from right edge\n      setPopupPosition({\n        position: 'internal',\n        top: Math.max(55, guidePopupPos.top),\n        // Align with guidepopup top or maintain minimum top\n        left: Math.max(guidePopupPos.left + 15, internalLeft) // Ensure it doesn't go beyond left edge with 15px padding\n      });\n    }\n  };\n\n  // Update positioning when popup opens or guidepopup changes\n  useEffect(() => {\n    if (openGenAiImagePopup) {\n      calculatePosition();\n\n      // Set up listeners for responsive behavior\n      const handleResize = () => calculatePosition();\n      const handleScroll = () => calculatePosition();\n      window.addEventListener('resize', handleResize);\n      window.addEventListener('scroll', handleScroll);\n\n      // Observer for guidepopup changes\n      const guidePopupElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');\n      let resizeObserver = null;\n      let mutationObserver = null;\n      if (guidePopupElement && window.ResizeObserver) {\n        resizeObserver = new ResizeObserver(() => {\n          setTimeout(calculatePosition, 50);\n        });\n        resizeObserver.observe(guidePopupElement);\n      }\n      if (guidePopupElement && window.MutationObserver) {\n        mutationObserver = new MutationObserver(() => {\n          setTimeout(calculatePosition, 50);\n        });\n        mutationObserver.observe(guidePopupElement, {\n          attributes: true,\n          attributeFilter: ['style', 'class']\n        });\n      }\n\n      // Cleanup function\n      return () => {\n        window.removeEventListener('resize', handleResize);\n        window.removeEventListener('scroll', handleScroll);\n        if (resizeObserver) resizeObserver.disconnect();\n        if (mutationObserver) mutationObserver.disconnect();\n      };\n    }\n  }, [openGenAiImagePopup]);\n  const handleClose = () => {\n    setOpenGenAiImagePopup(false);\n  };\n  const onImageGenerated = imageUrl => {\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\n\n    let file = {\n      Url: imageUrl,\n      FileName: `Generated Image ${Date.now()}`,\n      IsAiGenerated: true\n    };\n    handleImageUploadFormApp(file);\n    setOpenGenAiImagePopup(false);\n  };\n  const handleEnhanceDescription = async () => {\n    if (description.trim() === \"\") {\n      openSnackbar(\"Please enter a description first.\", \"error\");\n      return;\n    }\n    setIsEnhancing(true);\n    try {\n      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\n      if (enhancedPrompt) {\n        setDescription(enhancedPrompt);\n      }\n    } catch (error) {\n      console.error(\"Error enhancing description:\", error);\n    } finally {\n      setIsEnhancing(false);\n    }\n  };\n  const GenerateImage = () => {\n    if (description === \"\") return;\n\n    // Validate that we have the correct image context\n    // containerId is required, but buttonId can be empty for new image generation\n    if (!imageAnchorEl.containerId) {\n      openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\n      return;\n    }\n    setIsGenerating(true); // Start loader\n    const controller = new AbortController();\n    setAbortController(controller);\n    const userPromptWithSelectedOptions = `User Asked: ${description} ${selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"} ${selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"} ${selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"}`;\n    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, imageUrl => {\n      onImageGenerated(imageUrl);\n      setIsGenerating(false); // Stop loader\n      setAbortController(null);\n    }, openSnackbar, controller.signal);\n  };\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: `${popupPosition.top}px`,\n        height: 'calc(100vh - 55px)',\n        width: '280px',\n        left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',\n        backgroundColor: '#fff',\n        boxShadow: popupPosition.position === 'external' ? '-2px 0 8px rgba(0, 0, 0, 0.2)' : '0 4px 12px rgba(0, 0, 0, 0.15)',\n        transition: 'left 0.3s ease-in-out, box-shadow 0.3s ease-in-out',\n        zIndex: popupPosition.position === 'external' ? 1000 : 1001,\n        padding: '12px',\n        alignItems: \"end\",\n        borderRadius: popupPosition.position === 'internal' ? '8px' : '0',\n        maxHeight: popupPosition.position === 'internal' ? '500px' : 'calc(100vh - 55px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"18px !important\"\n          },\n          children: translate(\"Generate Images\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer'\n          },\n          onClick: handleClose,\n          dangerouslySetInnerHTML: {\n            __html: closeIcon\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '93%',\n          marginTop: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          rows: 3,\n          placeholder: translate(\"Please describe your image...\"),\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          style: {\n            width: '93%',\n            height: '93px',\n            padding: '16px',\n            // Extra right padding for button space\n            fontSize: '12px',\n            borderRadius: '6px',\n            border: '1px solid #ccc',\n            resize: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleEnhanceDescription,\n          disabled: isEnhancing || description.trim() === \"\",\n          style: {\n            position: 'absolute',\n            top: '75%',\n            left: '10px',\n            transform: 'translateY(-50%)',\n            backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\n            color: 'black',\n            border: 'none',\n            padding: '4px 10px',\n            borderRadius: '100px',\n            fontSize: '10px',\n            cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\n            whiteSpace: 'nowrap',\n            alignItems: 'center',\n            display: 'flex',\n            gap: '6px',\n            opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5'\n          },\n          children: [isEnhancing ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '10px',\n              height: '10px',\n              border: '1px solid #333',\n              borderTop: '1px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 7\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: blackMagicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 7\n          }, this), isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Style\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: styles.map(style => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedStyle(style),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${style}`)\n          }, style, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Colors\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: colors.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedColor(color),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${color}`)\n          }, color, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: translate(\"Ratio\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: ratios.map(ratio => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedRatio(ratio),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: ratio\n          }, ratio, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 3\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (isGenerating) {\n            // Cancel if generating\n            abortController === null || abortController === void 0 ? void 0 : abortController.abort();\n            setIsGenerating(false);\n            setAbortController(null);\n          } else if (description.trim() !== '') {\n            // Don't call setReplaceImage(true) here as it's already set when popup opens\n            // and we need to maintain the correct imageAnchorEl context\n            GenerateImage();\n          }\n        },\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        disabled: isGenerating && !abortController,\n        style: {\n          marginTop: '20px',\n          width: '100%',\n          padding: '10px 16px',\n          borderRadius: '8px',\n          backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\n          color: '#fff',\n          border: 'none',\n          fontSize: '14px',\n          cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '8px',\n          transition: 'background-color 0.3s',\n          opacity: description.trim() === '' ? 0.7 : 1\n        },\n        children: isGenerating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '14px',\n              height: '14px',\n              border: '2px solid #fff',\n              borderTop: '2px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 7\n          }, this), isHovered ? translate('Cancel Generation') : translate('Generating...')]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: magicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 7\n          }, this), translate('Generate Image')]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ImageGenerationPopup, \"613vvd6p1iqYCmRZi13U6YdMAiI=\", false, function () {\n  return [useTranslation, useSnackbar, useDrawerStore];\n});\n_c = ImageGenerationPopup;\nexport default ImageGenerationPopup;\nvar _c;\n$RefreshReg$(_c, \"ImageGenerationPopup\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "closeIcon", "magicPen", "Box", "Typography", "GenerateImageWithUserPrompt", "EnhanceUserPrompt", "AccountContext", "useTranslation", "useSnackbar", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageGenerationPopup", "openGenAiImagePopup", "setOpenGenAiImagePopup", "handleImageUploadFormApp", "setReplaceImage", "_s", "t", "translate", "openSnackbar", "imageAnchorEl", "state", "isHovered", "setIsHovered", "abortController", "setAbortController", "popupPosition", "setPopupPosition", "position", "top", "left", "right", "selected<PERSON><PERSON><PERSON>", "setSelectedStyle", "selectedColor", "setSelectedColor", "selectedRatio", "setSelectedRatio", "accountId", "styles", "colors", "ratios", "description", "setDescription", "isGenerating", "setIsGenerating", "isEnhancing", "setIsEnhancing", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "width", "height", "calculatePosition", "guidePopupPos", "viewportWidth", "window", "innerWidth", "popup<PERSON><PERSON><PERSON>", "gap", "requiredSpaceForExternal", "hasSpaceForExternal", "externalLeft", "Math", "min", "internalLeft", "max", "handleResize", "handleScroll", "addEventListener", "guidePopupElement", "resizeObserver", "mutationObserver", "ResizeObserver", "setTimeout", "observe", "MutationObserver", "attributes", "attributeFilter", "removeEventListener", "disconnect", "handleClose", "onImageGenerated", "imageUrl", "file", "Url", "FileName", "Date", "now", "IsAiGenerated", "handleEnhanceDescription", "trim", "enhancedPrompt", "error", "console", "GenerateImage", "containerId", "controller", "AbortController", "userPromptWithSelectedOptions", "signal", "blackMagicPen", "replace", "children", "style", "backgroundColor", "boxShadow", "transition", "zIndex", "padding", "alignItems", "borderRadius", "maxHeight", "sx", "display", "justifyContent", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cursor", "onClick", "dangerouslySetInnerHTML", "__html", "marginTop", "rows", "placeholder", "value", "onChange", "e", "target", "border", "resize", "disabled", "transform", "color", "whiteSpace", "opacity", "borderTop", "animation", "flexWrap", "map", "ratio", "abort", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/ImageGenerationPopup.tsx"], "sourcesContent": ["import React, { useContext, useState, useEffect } from 'react';\r\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\r\nimport { Box,Typography } from '@mui/material';\r\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { FileUpload } from '../../../models/FileUpload';\r\nimport { timeStamp } from 'console';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useSnackbar } from '../guideList/SnackbarContext';\r\nimport useDrawerStore  from '../../../store/drawerStore';\r\n\r\n\r\n\r\n\r\n\r\nconst ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {\r\n  //const [isOpen, setIsOpen] = useState(false);\r\n\r\n  //const togglePopup = () => setIsOpen(!isOpen);\r\n  const { t: translate } = useTranslation();\r\n  const { openSnackbar } = useSnackbar();\r\n\r\n  // Get imageAnchorEl from store to know which image to replace\r\n  const { imageAnchorEl } = useDrawerStore((state) => state);\r\n\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [abortController, setAbortController] = useState<AbortController | null>(null);\r\n  const [popupPosition, setPopupPosition] = useState<{\r\n    position: 'external' | 'internal';\r\n    top: number;\r\n    left: number;\r\n    right?: number;\r\n  }>({\r\n    position: 'external',\r\n    top: 55,\r\n    left: 0,\r\n    right: 0\r\n  });\r\n\r\n\r\nconst [selectedStyle, setSelectedStyle] = useState('Professional');\r\n  const [selectedColor, setSelectedColor] = useState('Black & White');\r\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\r\n\r\n  const { accountId } = useContext(AccountContext);\r\n\r\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\r\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\r\n  const ratios = ['1:1', '16:9','9:16'];\r\n  const [description, setDescription] = useState('');\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n\r\n  // Function to get guidepopup position and dimensions\r\n  const getGuidePopupPosition = () => {\r\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n                    document.getElementById('guide-popup');\r\n    if (element) {\r\n      const rect = element.getBoundingClientRect();\r\n      return {\r\n        top: rect.top,\r\n        left: rect.left,\r\n        width: rect.width,\r\n        height: rect.height\r\n      };\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Function to calculate adaptive positioning\r\n  const calculatePosition = () => {\r\n    const guidePopupPos = getGuidePopupPosition();\r\n    if (!guidePopupPos) return;\r\n\r\n    const viewportWidth = window.innerWidth;\r\n    const popupWidth = 280; // ImageGenerationPopup width\r\n    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup\r\n\r\n    // Calculate if there's enough space for external positioning\r\n    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;\r\n    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20; // 20px margin from viewport edge\r\n\r\n    if (hasSpaceForExternal) {\r\n      // External positioning: to the right of guidepopup with gap\r\n      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;\r\n      setPopupPosition({\r\n        position: 'external',\r\n        top: 55, // Keep consistent with current top positioning\r\n        left: Math.min(externalLeft, viewportWidth - popupWidth - 10), // Ensure it doesn't overflow viewport\r\n      });\r\n    } else {\r\n      // Internal positioning: align to the right side within guidepopup\r\n      const internalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15; // 15px padding from right edge\r\n      setPopupPosition({\r\n        position: 'internal',\r\n        top: Math.max(55, guidePopupPos.top), // Align with guidepopup top or maintain minimum top\r\n        left: Math.max(guidePopupPos.left + 15, internalLeft), // Ensure it doesn't go beyond left edge with 15px padding\r\n      });\r\n    }\r\n  };\r\n\r\n  // Update positioning when popup opens or guidepopup changes\r\n  useEffect(() => {\r\n    if (openGenAiImagePopup) {\r\n      calculatePosition();\r\n\r\n      // Set up listeners for responsive behavior\r\n      const handleResize = () => calculatePosition();\r\n      const handleScroll = () => calculatePosition();\r\n\r\n      window.addEventListener('resize', handleResize);\r\n      window.addEventListener('scroll', handleScroll);\r\n\r\n      // Observer for guidepopup changes\r\n      const guidePopupElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');\r\n      let resizeObserver: ResizeObserver | null = null;\r\n      let mutationObserver: MutationObserver | null = null;\r\n\r\n      if (guidePopupElement && window.ResizeObserver) {\r\n        resizeObserver = new ResizeObserver(() => {\r\n          setTimeout(calculatePosition, 50);\r\n        });\r\n        resizeObserver.observe(guidePopupElement);\r\n      }\r\n\r\n      if (guidePopupElement && window.MutationObserver) {\r\n        mutationObserver = new MutationObserver(() => {\r\n          setTimeout(calculatePosition, 50);\r\n        });\r\n        mutationObserver.observe(guidePopupElement, {\r\n          attributes: true,\r\n          attributeFilter: ['style', 'class']\r\n        });\r\n      }\r\n\r\n      // Cleanup function\r\n      return () => {\r\n        window.removeEventListener('resize', handleResize);\r\n        window.removeEventListener('scroll', handleScroll);\r\n        if (resizeObserver) resizeObserver.disconnect();\r\n        if (mutationObserver) mutationObserver.disconnect();\r\n      };\r\n    }\r\n  }, [openGenAiImagePopup]);\r\n\r\n  const handleClose = () => {\r\n  setOpenGenAiImagePopup(false);\r\n  };\r\n\r\n  const onImageGenerated = (imageUrl: any) => {\r\n    \r\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\r\n\r\n    let file: any = {\r\n      Url: imageUrl,\r\n      FileName: `Generated Image ${Date.now()}`,\r\n      IsAiGenerated: true,\r\n    };\r\n    \r\n\r\n    handleImageUploadFormApp(file);\r\n    setOpenGenAiImagePopup(false);\r\n\r\n  }\r\n\r\nconst handleEnhanceDescription = async () => {\r\n  if (description.trim() === \"\") {\r\n    openSnackbar(\"Please enter a description first.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsEnhancing(true);\r\n  try {\r\n    const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\r\n    if (enhancedPrompt) {\r\n      setDescription(enhancedPrompt);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error enhancing description:\", error);\r\n  } finally {\r\n    setIsEnhancing(false);\r\n  }\r\n};\r\n\r\nconst GenerateImage = () => {\r\n  if (description === \"\") return;\r\n\r\n  // Validate that we have the correct image context\r\n  // containerId is required, but buttonId can be empty for new image generation\r\n  if (!imageAnchorEl.containerId) {\r\n    openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsGenerating(true); // Start loader\r\n    const controller = new AbortController();\r\n  setAbortController(controller);\r\n\r\nconst userPromptWithSelectedOptions = `User Asked: ${description} ${\r\n  selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"\r\n} ${\r\n  selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"\r\n} ${\r\n  selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"\r\n}`;\r\n\r\n\r\n  GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {\r\n    onImageGenerated(imageUrl);\r\n    setIsGenerating(false); // Stop loader\r\n    setAbortController(null);\r\n  }, openSnackbar, controller.signal);\r\n};\r\n\r\n    const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n  \r\n\r\n  return (\r\n    <>\r\n      <div\r\n        style={{\r\n          position: 'fixed',\r\n          top: `${popupPosition.top}px`,\r\n          height: 'calc(100vh - 55px)',\r\n          width: '280px',\r\n          left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',\r\n          backgroundColor: '#fff',\r\n          boxShadow: popupPosition.position === 'external'\r\n            ? '-2px 0 8px rgba(0, 0, 0, 0.2)'\r\n            : '0 4px 12px rgba(0, 0, 0, 0.15)',\r\n          transition: 'left 0.3s ease-in-out, box-shadow 0.3s ease-in-out',\r\n          zIndex: popupPosition.position === 'external' ? 1000 : 1001,\r\n          padding: '12px',\r\n          alignItems: \"end\",\r\n          borderRadius: popupPosition.position === 'internal' ? '8px' : '0',\r\n          maxHeight: popupPosition.position === 'internal' ? '500px' : 'calc(100vh - 55px)',\r\n        }}\r\n          >\r\n              <Box sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"space-between\",\r\n                  \r\n              }}\r\n              >\r\n                  <Typography sx={{fontSize: \"18px !important\"}}>\r\n            \r\n            {translate(\"Generate Images\")}\r\n                  </Typography>\r\n                \r\n                \r\n                    <span style={{ cursor: 'pointer' }} onClick={handleClose} dangerouslySetInnerHTML={{ __html: closeIcon }}/>\r\n              </Box>\r\n              \r\n\r\n        <div style={{ position: 'relative', width: '93%', marginTop: '10px' }}>\r\n  <textarea\r\n    rows={3}\r\n    placeholder={translate(\"Please describe your image...\")}\r\n    value={description}\r\n    onChange={(e) => setDescription(e.target.value)}\r\n    style={{\r\n      width: '93%',\r\n      height: '93px',\r\n      padding: '16px', // Extra right padding for button space\r\n      fontSize: '12px',\r\n      borderRadius: '6px',\r\n      border: '1px solid #ccc',\r\n      resize: 'none',\r\n    }}\r\n  />\r\n  <button\r\n    onClick={handleEnhanceDescription}\r\n    disabled={isEnhancing || description.trim() === \"\"}\r\n    style={{\r\n      position: 'absolute',\r\n      top: '75%',\r\n      left: '10px',\r\n      transform: 'translateY(-50%)',\r\n      backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\r\n      color: 'black',\r\n      border: 'none',\r\n      padding: '4px 10px',\r\n      borderRadius: '100px',\r\n      fontSize: '10px',\r\n      cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\r\n      whiteSpace: 'nowrap',\r\n      alignItems: 'center',\r\n      display: 'flex',\r\n      gap: '6px',\r\n      opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5',\r\n    }}\r\n          >\r\n    {isEnhancing ? (\r\n      <div\r\n        style={{\r\n          width: '10px',\r\n          height: '10px',\r\n          border: '1px solid #333',\r\n          borderTop: '1px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n    ) : (\r\n      <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} />\r\n    )}\r\n\r\n    {isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")}\r\n  </button>\r\n</div>\r\n\r\n\r\n              {/* You can add form, preview, upload, etc. inside here */}\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Style\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }} >\r\n          {styles.map((style) => (\r\n            <button\r\n              key={style}\r\n              onClick={() => setSelectedStyle(style)}\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n            >\r\n               {translate(`${style}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n              </div>\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Colors\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n          {colors.map((color) => (\r\n            <button\r\n              key={color}\r\n              onClick={() => setSelectedColor(color)}\r\n\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n              \r\n            >\r\n               {translate(`${color}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n        </div>\r\n        <div>\r\n  <h2>{translate(\"Ratio\")}</h2>\r\n  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n{ratios.map((ratio) => (\r\n  <button\r\n    key={ratio}\r\n    onClick={() => setSelectedRatio(ratio)}\r\n    style={{\r\n      padding: '5px 12px',\r\n      borderRadius: '8px',\r\n      border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n      backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\r\n      color: '#333',\r\n      cursor: 'pointer',\r\n      fontSize: '11px',\r\n    }}\r\n  >\r\n    {ratio}\r\n  </button>\r\n))}\r\n\r\n  </div>\r\n</div>\r\n\r\n<button\r\n  onClick={() => {\r\n    if (isGenerating) {\r\n      // Cancel if generating\r\n      abortController?.abort();\r\n      setIsGenerating(false);\r\n      setAbortController(null);\r\n    } else if (description.trim() !== '') {\r\n      // Don't call setReplaceImage(true) here as it's already set when popup opens\r\n      // and we need to maintain the correct imageAnchorEl context\r\n      GenerateImage();\r\n    }\r\n  }}\r\n  onMouseEnter={() => setIsHovered(true)}\r\n  onMouseLeave={() => setIsHovered(false)}\r\n  disabled={isGenerating && !abortController}\r\n  style={{\r\n    marginTop: '20px',\r\n    width: '100%',\r\n    padding: '10px 16px',\r\n    borderRadius: '8px',\r\n    backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\r\n    color: '#fff',\r\n    border: 'none',\r\n    fontSize: '14px',\r\n    cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '8px',\r\n    transition: 'background-color 0.3s',\r\n    opacity: description.trim() === '' ? 0.7 : 1,\r\n  }}\r\n>\r\n  {isGenerating ? (\r\n    <>\r\n      <div\r\n        style={{\r\n          width: '14px',\r\n          height: '14px',\r\n          border: '2px solid #fff',\r\n          borderTop: '2px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n      {isHovered ? translate('Cancel Generation') : translate('Generating...')}\r\n    </>\r\n  ) : (\r\n    <>\r\n      <span dangerouslySetInnerHTML={{ __html: magicPen }} />\r\n      {translate('Generate Image')}\r\n    </>\r\n  )}\r\n</button>\r\n\r\n\r\n          </div>\r\n          \r\n    </>\r\n  );\r\n};\r\n\r\nexport default ImageGenerationPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,SAAS,EAAEC,QAAQ,QAAQ,6BAA6B;AACjE,SAASC,GAAG,EAACC,UAAU,QAAQ,eAAe;AAC9C,SAASC,2BAA2B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC5F,SAASC,cAAc,QAAQ,4BAA4B;AAG3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAOC,cAAc,MAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMzD,MAAMC,oBAAoB,GAAGA,CAAC;EAACC,mBAAmB;EAACC,sBAAsB;EAACC,wBAAwB;EAACC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC1H;;EAEA;EACA,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGd,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEe;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAM;IAAEe;EAAc,CAAC,GAAGd,cAAc,CAAEe,KAAK,IAAKA,KAAK,CAAC;EAE1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAK/C;IACDiC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;EAGJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,cAAc,CAAC;EAChE,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,eAAe,CAAC;EACnE,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,MAAM,CAAC;EAE1D,MAAM;IAAE2C;EAAU,CAAC,GAAG5C,UAAU,CAACS,cAAc,CAAC;EAEhD,MAAMoC,MAAM,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;EACzF,MAAMC,MAAM,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;EACxF,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAC,MAAM,CAAC;EACrC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;;EAGrD;EACA,MAAMqD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IAC7DD,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACLzB,GAAG,EAAEwB,IAAI,CAACxB,GAAG;QACbC,IAAI,EAAEuB,IAAI,CAACvB,IAAI;QACfyB,KAAK,EAAEF,IAAI,CAACE,KAAK;QACjBC,MAAM,EAAEH,IAAI,CAACG;MACf,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,aAAa,GAAGV,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACU,aAAa,EAAE;IAEpB,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,UAAU,GAAG,GAAG,CAAC,CAAC;IACxB,MAAMC,GAAG,GAAG,EAAE,CAAC,CAAC;;IAEhB;IACA,MAAMC,wBAAwB,GAAGN,aAAa,CAAC5B,IAAI,GAAG4B,aAAa,CAACH,KAAK,GAAGQ,GAAG,GAAGD,UAAU;IAC5F,MAAMG,mBAAmB,GAAGD,wBAAwB,IAAIL,aAAa,GAAG,EAAE,CAAC,CAAC;;IAE5E,IAAIM,mBAAmB,EAAE;MACvB;MACA,MAAMC,YAAY,GAAGR,aAAa,CAAC5B,IAAI,GAAG4B,aAAa,CAACH,KAAK,GAAGQ,GAAG;MACnEpC,gBAAgB,CAAC;QACfC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,EAAE;QAAE;QACTC,IAAI,EAAEqC,IAAI,CAACC,GAAG,CAACF,YAAY,EAAEP,aAAa,GAAGG,UAAU,GAAG,EAAE,CAAC,CAAE;MACjE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMO,YAAY,GAAGX,aAAa,CAAC5B,IAAI,GAAG4B,aAAa,CAACH,KAAK,GAAGO,UAAU,GAAG,EAAE,CAAC,CAAC;MACjFnC,gBAAgB,CAAC;QACfC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAEsC,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEZ,aAAa,CAAC7B,GAAG,CAAC;QAAE;QACtCC,IAAI,EAAEqC,IAAI,CAACG,GAAG,CAACZ,aAAa,CAAC5B,IAAI,GAAG,EAAE,EAAEuC,YAAY,CAAC,CAAE;MACzD,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAzE,SAAS,CAAC,MAAM;IACd,IAAIgB,mBAAmB,EAAE;MACvB6C,iBAAiB,CAAC,CAAC;;MAEnB;MACA,MAAMc,YAAY,GAAGA,CAAA,KAAMd,iBAAiB,CAAC,CAAC;MAC9C,MAAMe,YAAY,GAAGA,CAAA,KAAMf,iBAAiB,CAAC,CAAC;MAE9CG,MAAM,CAACa,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MAC/CX,MAAM,CAACa,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;;MAE/C;MACA,MAAME,iBAAiB,GAAGxB,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC;MACvF,IAAIwB,cAAqC,GAAG,IAAI;MAChD,IAAIC,gBAAyC,GAAG,IAAI;MAEpD,IAAIF,iBAAiB,IAAId,MAAM,CAACiB,cAAc,EAAE;QAC9CF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;UACxCC,UAAU,CAACrB,iBAAiB,EAAE,EAAE,CAAC;QACnC,CAAC,CAAC;QACFkB,cAAc,CAACI,OAAO,CAACL,iBAAiB,CAAC;MAC3C;MAEA,IAAIA,iBAAiB,IAAId,MAAM,CAACoB,gBAAgB,EAAE;QAChDJ,gBAAgB,GAAG,IAAII,gBAAgB,CAAC,MAAM;UAC5CF,UAAU,CAACrB,iBAAiB,EAAE,EAAE,CAAC;QACnC,CAAC,CAAC;QACFmB,gBAAgB,CAACG,OAAO,CAACL,iBAAiB,EAAE;UAC1CO,UAAU,EAAE,IAAI;UAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;QACpC,CAAC,CAAC;MACJ;;MAEA;MACA,OAAO,MAAM;QACXtB,MAAM,CAACuB,mBAAmB,CAAC,QAAQ,EAAEZ,YAAY,CAAC;QAClDX,MAAM,CAACuB,mBAAmB,CAAC,QAAQ,EAAEX,YAAY,CAAC;QAClD,IAAIG,cAAc,EAAEA,cAAc,CAACS,UAAU,CAAC,CAAC;QAC/C,IAAIR,gBAAgB,EAAEA,gBAAgB,CAACQ,UAAU,CAAC,CAAC;MACrD,CAAC;IACH;EACF,CAAC,EAAE,CAACxE,mBAAmB,CAAC,CAAC;EAEzB,MAAMyE,WAAW,GAAGA,CAAA,KAAM;IAC1BxE,sBAAsB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMyE,gBAAgB,GAAIC,QAAa,IAAK;IAE1C;;IAEA,IAAIC,IAAS,GAAG;MACdC,GAAG,EAAEF,QAAQ;MACbG,QAAQ,EAAE,mBAAmBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACzCC,aAAa,EAAE;IACjB,CAAC;IAGD/E,wBAAwB,CAAC0E,IAAI,CAAC;IAC9B3E,sBAAsB,CAAC,KAAK,CAAC;EAE/B,CAAC;EAEH,MAAMiF,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAIpD,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B5E,YAAY,CAAC,mCAAmC,EAAE,OAAO,CAAC;MAC1D;IACF;IAEA4B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMiD,cAAc,GAAG,MAAM9F,iBAAiB,CAACwC,WAAW,EAAEJ,SAAS,EAAEnB,YAAY,CAAC;MACpF,IAAI6E,cAAc,EAAE;QAClBrD,cAAc,CAACqD,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRlD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMoD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzD,WAAW,KAAK,EAAE,EAAE;;IAExB;IACA;IACA,IAAI,CAACtB,aAAa,CAACgF,WAAW,EAAE;MAC9BjF,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;MAClG;IACF;IAEA0B,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACrB,MAAMwD,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC1C7E,kBAAkB,CAAC4E,UAAU,CAAC;IAEhC,MAAME,6BAA6B,GAAG,eAAe7D,WAAW,IAC9DV,aAAa,KAAK,EAAE,GAAG,oCAAoC,GAAGA,aAAa,GAAG,EAAE,IAEhFE,aAAa,KAAK,EAAE,GAAG,wCAAwC,GAAGA,aAAa,GAAG,EAAE,IAEpFE,aAAa,KAAK,EAAE,GAAG,uCAAuC,GAAGA,aAAa,GAAG,EAAE,EACnF;IAGAnC,2BAA2B,CAACsG,6BAA6B,EAAEjE,SAAS,EAAGiD,QAAa,IAAK;MACvFD,gBAAgB,CAACC,QAAQ,CAAC;MAC1B1C,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MACxBpB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,EAAEN,YAAY,EAAEkF,UAAU,CAACG,MAAM,CAAC;EACrC,CAAC;EAEG,MAAMC,aAAa,GAAG3G,QAAQ,CAAC4G,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAG7E,oBACElG,OAAA,CAAAE,SAAA;IAAAiG,QAAA,eACEnG,OAAA;MACEoG,KAAK,EAAE;QACLhF,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,GAAGH,aAAa,CAACG,GAAG,IAAI;QAC7B2B,MAAM,EAAE,oBAAoB;QAC5BD,KAAK,EAAE,OAAO;QACdzB,IAAI,EAAElB,mBAAmB,GAAG,GAAGc,aAAa,CAACI,IAAI,IAAI,GAAG,QAAQ;QAChE+E,eAAe,EAAE,MAAM;QACvBC,SAAS,EAAEpF,aAAa,CAACE,QAAQ,KAAK,UAAU,GAC5C,+BAA+B,GAC/B,gCAAgC;QACpCmF,UAAU,EAAE,oDAAoD;QAChEC,MAAM,EAAEtF,aAAa,CAACE,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAG,IAAI;QAC3DqF,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAEzF,aAAa,CAACE,QAAQ,KAAK,UAAU,GAAG,KAAK,GAAG,GAAG;QACjEwF,SAAS,EAAE1F,aAAa,CAACE,QAAQ,KAAK,UAAU,GAAG,OAAO,GAAG;MAC/D,CAAE;MAAA+E,QAAA,gBAEInG,OAAA,CAACT,GAAG;QAACsH,EAAE,EAAE;UACLC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE;QAEpB,CAAE;QAAAZ,QAAA,gBAEEnG,OAAA,CAACR,UAAU;UAACqH,EAAE,EAAE;YAACG,QAAQ,EAAE;UAAiB,CAAE;UAAAb,QAAA,EAEnDzF,SAAS,CAAC,iBAAiB;QAAC;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAGXpH,OAAA;UAAMoG,KAAK,EAAE;YAAEiB,MAAM,EAAE;UAAU,CAAE;UAACC,OAAO,EAAEzC,WAAY;UAAC0C,uBAAuB,EAAE;YAAEC,MAAM,EAAEnI;UAAU;QAAE;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC,eAGZpH,OAAA;QAAKoG,KAAK,EAAE;UAAEhF,QAAQ,EAAE,UAAU;UAAE2B,KAAK,EAAE,KAAK;UAAE0E,SAAS,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBAC5EnG,OAAA;UACE0H,IAAI,EAAE,CAAE;UACRC,WAAW,EAAEjH,SAAS,CAAC,+BAA+B,CAAE;UACxDkH,KAAK,EAAE1F,WAAY;UACnB2F,QAAQ,EAAGC,CAAC,IAAK3F,cAAc,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDxB,KAAK,EAAE;YACLrD,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,MAAM;YACdyD,OAAO,EAAE,MAAM;YAAE;YACjBO,QAAQ,EAAE,MAAM;YAChBL,YAAY,EAAE,KAAK;YACnBqB,MAAM,EAAE,gBAAgB;YACxBC,MAAM,EAAE;UACV;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFpH,OAAA;UACEsH,OAAO,EAAEhC,wBAAyB;UAClC4C,QAAQ,EAAE5F,WAAW,IAAIJ,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAG;UACnDa,KAAK,EAAE;YACLhF,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,MAAM;YACZ6G,SAAS,EAAE,kBAAkB;YAC7B9B,eAAe,EAAE/D,WAAW,IAAIJ,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,WAAW;YACrF6C,KAAK,EAAE,OAAO;YACdJ,MAAM,EAAE,MAAM;YACdvB,OAAO,EAAE,UAAU;YACnBE,YAAY,EAAE,OAAO;YACrBK,QAAQ,EAAE,MAAM;YAChBK,MAAM,EAAE/E,WAAW,IAAIJ,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,aAAa,GAAG,SAAS;YAC5E8C,UAAU,EAAE,QAAQ;YACpB3B,UAAU,EAAE,QAAQ;YACpBI,OAAO,EAAE,MAAM;YACfvD,GAAG,EAAE,KAAK;YACV+E,OAAO,EAAEhG,WAAW,IAAIJ,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG;UAC9D,CAAE;UAAAY,QAAA,GAED7D,WAAW,gBACVtC,OAAA;YACEoG,KAAK,EAAE;cACLrD,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdgF,MAAM,EAAE,gBAAgB;cACxBO,SAAS,EAAE,uBAAuB;cAClC5B,YAAY,EAAE,KAAK;cACnB6B,SAAS,EAAE;YACb;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFpH,OAAA;YAAMuH,uBAAuB,EAAE;cAAEC,MAAM,EAAEvB;YAAc;UAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5D,EAEA9E,WAAW,GAAG5B,SAAS,CAAC,cAAc,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAC;QAAA;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAKQpH,OAAA;QAAAmG,QAAA,gBACNnG,OAAA;UAAAmG,QAAA,GAAK,GAAC,EAACzF,SAAS,CAAC,OAAO,CAAC;QAAA;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/BpH,OAAA;UAAKoG,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAE2B,QAAQ,EAAE,MAAM;YAAElF,GAAG,EAAE,KAAK;YAAEkE,SAAS,EAAE;UAAO,CAAE;UAAAtB,QAAA,EAC9EpE,MAAM,CAAC2G,GAAG,CAAEtC,KAAK,iBAChBpG,OAAA;YAEEsH,OAAO,EAAEA,CAAA,KAAM7F,gBAAgB,CAAC2E,KAAK,CAAE;YACjCA,KAAK,EAAE;cACLK,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBqB,MAAM,EAAExG,aAAa,KAAK4E,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxEC,eAAe,EAAE7E,aAAa,KAAK4E,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEgC,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAb,QAAA,EAENzF,SAAS,CAAC,GAAG0F,KAAK,EAAE;UAAC,GAZlBA,KAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAENpH,OAAA;QAAAmG,QAAA,gBACNnG,OAAA;UAAAmG,QAAA,GAAK,GAAC,EAACzF,SAAS,CAAC,QAAQ,CAAC;QAAA;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChCpH,OAAA;UAAKoG,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAE2B,QAAQ,EAAE,MAAM;YAAElF,GAAG,EAAE,KAAK;YAAEkE,SAAS,EAAE;UAAO,CAAE;UAAAtB,QAAA,EAC9EnE,MAAM,CAAC0G,GAAG,CAAEN,KAAK,iBAChBpI,OAAA;YAEEsH,OAAO,EAAEA,CAAA,KAAM3F,gBAAgB,CAACyG,KAAK,CAAE;YAEjChC,KAAK,EAAE;cACLK,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBqB,MAAM,EAAEtG,aAAa,KAAK0G,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxE/B,eAAe,EAAE3E,aAAa,KAAK0G,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEA,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAb,QAAA,EAGNzF,SAAS,CAAC,GAAG0H,KAAK,EAAE;UAAC,GAdlBA,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNpH,OAAA;QAAAmG,QAAA,gBACNnG,OAAA;UAAAmG,QAAA,EAAKzF,SAAS,CAAC,OAAO;QAAC;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BpH,OAAA;UAAKoG,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAE2B,QAAQ,EAAE,MAAM;YAAElF,GAAG,EAAE,KAAK;YAAEkE,SAAS,EAAE;UAAO,CAAE;UAAAtB,QAAA,EAClFlE,MAAM,CAACyG,GAAG,CAAEC,KAAK,iBAChB3I,OAAA;YAEEsH,OAAO,EAAEA,CAAA,KAAMzF,gBAAgB,CAAC8G,KAAK,CAAE;YACvCvC,KAAK,EAAE;cACLK,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBqB,MAAM,EAAEpG,aAAa,KAAK+G,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxEtC,eAAe,EAAEzE,aAAa,KAAK+G,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEP,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAb,QAAA,EAEDwC;UAAK,GAZDA,KAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpH,OAAA;QACEsH,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIlF,YAAY,EAAE;YAChB;YACApB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4H,KAAK,CAAC,CAAC;YACxBvG,eAAe,CAAC,KAAK,CAAC;YACtBpB,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM,IAAIiB,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC;YACA;YACAI,aAAa,CAAC,CAAC;UACjB;QACF,CAAE;QACFkD,YAAY,EAAEA,CAAA,KAAM9H,YAAY,CAAC,IAAI,CAAE;QACvC+H,YAAY,EAAEA,CAAA,KAAM/H,YAAY,CAAC,KAAK,CAAE;QACxCmH,QAAQ,EAAE9F,YAAY,IAAI,CAACpB,eAAgB;QAC3CoF,KAAK,EAAE;UACLqB,SAAS,EAAE,MAAM;UACjB1E,KAAK,EAAE,MAAM;UACb0D,OAAO,EAAE,WAAW;UACpBE,YAAY,EAAE,KAAK;UACnBN,eAAe,EAAEjE,YAAY,GAAG,SAAS,GAAGF,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UAC7F6C,KAAK,EAAE,MAAM;UACbJ,MAAM,EAAE,MAAM;UACdhB,QAAQ,EAAE,MAAM;UAChBK,MAAM,EAAEjF,YAAY,IAAIF,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UACzEuB,OAAO,EAAE,MAAM;UACfJ,UAAU,EAAE,QAAQ;UACpBK,cAAc,EAAE,QAAQ;UACxBxD,GAAG,EAAE,KAAK;UACVgD,UAAU,EAAE,uBAAuB;UACnC+B,OAAO,EAAEpG,WAAW,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;QAC7C,CAAE;QAAAY,QAAA,EAED/D,YAAY,gBACXpC,OAAA,CAAAE,SAAA;UAAAiG,QAAA,gBACEnG,OAAA;YACEoG,KAAK,EAAE;cACLrD,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdgF,MAAM,EAAE,gBAAgB;cACxBO,SAAS,EAAE,uBAAuB;cAClC5B,YAAY,EAAE,KAAK;cACnB6B,SAAS,EAAE;YACb;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDtG,SAAS,GAAGJ,SAAS,CAAC,mBAAmB,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAC;QAAA,eACxE,CAAC,gBAEHV,OAAA,CAAAE,SAAA;UAAAiG,QAAA,gBACEnG,OAAA;YAAMuH,uBAAuB,EAAE;cAAEC,MAAM,EAAElI;YAAS;UAAE;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtD1G,SAAS,CAAC,gBAAgB,CAAC;QAAA,eAC5B;MACH;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGM;EAAC,gBAEV,CAAC;AAEP,CAAC;AAAC5G,EAAA,CAhbIL,oBAAoB;EAAA,QAICP,cAAc,EACdC,WAAW,EAGVC,cAAc;AAAA;AAAAiJ,EAAA,GARpC5I,oBAAoB;AAkb1B,eAAeA,oBAAoB;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}