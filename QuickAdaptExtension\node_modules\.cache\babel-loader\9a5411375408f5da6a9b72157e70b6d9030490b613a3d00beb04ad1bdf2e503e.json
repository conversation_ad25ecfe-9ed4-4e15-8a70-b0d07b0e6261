{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\ImageGenerationPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState, useEffect, useRef } from 'react';\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\nimport { Box, Typography } from '@mui/material';\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\nimport { AccountContext } from '../../login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { useSnackbar } from '../guideList/SnackbarContext';\nimport useDrawerStore from '../../../store/drawerStore';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageGenerationPopup = ({\n  openGenAiImagePopup,\n  setOpenGenAiImagePopup,\n  handleImageUploadFormApp,\n  setReplaceImage\n}) => {\n  _s();\n  //const [isOpen, setIsOpen] = useState(false);\n\n  //const togglePopup = () => setIsOpen(!isOpen);\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    openSnackbar\n  } = useSnackbar();\n\n  // Get imageAnchorEl from store to know which image to replace\n  const {\n    imageAnchorEl\n  } = useDrawerStore(state => state);\n  const [isHovered, setIsHovered] = useState(false);\n  const [abortController, setAbortController] = useState(null);\n  const [popupPosition, setPopupPosition] = useState({});\n  const [isInternalPosition, setIsInternalPosition] = useState(false);\n  const popupRef = useRef(null);\n  const [selectedStyle, setSelectedStyle] = useState('Professional');\n  const [selectedColor, setSelectedColor] = useState('Black & White');\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\n  const ratios = ['1:1', '16:9', '9:16'];\n  const [description, setDescription] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [isEnhancing, setIsEnhancing] = useState(false);\n\n  // Function to get guidepopup position\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n\n  // Function to calculate adaptive positioning\n  const calculatePopupPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {\n      position: 'fixed',\n      right: '0px',\n      top: '55px'\n    };\n    const viewportWidth = window.innerWidth;\n    const popupWidth = 280; // ImageGenerationPopup width\n    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup\n\n    // Calculate external position (right side of guidepopup)\n    const externalLeftPosition = guidePopupPos.left + guidePopupPos.width + gap;\n    const hasSpaceForExternal = externalLeftPosition + popupWidth <= viewportWidth - 10;\n    if (hasSpaceForExternal) {\n      // External positioning - to the right side of guidepopup\n      setIsInternalPosition(false);\n      return {\n        position: 'fixed',\n        left: `${externalLeftPosition}px`,\n        top: `${guidePopupPos.top}px`,\n        right: 'auto',\n        transition: 'all 0.3s ease-in-out'\n      };\n    } else {\n      // Internal positioning - aligned to right side within guidepopup\n      setIsInternalPosition(true);\n      return {\n        position: 'fixed',\n        left: `${guidePopupPos.left + guidePopupPos.width - popupWidth - 15}px`,\n        // 15px from right edge\n        top: `${guidePopupPos.top}px`,\n        right: 'auto',\n        transition: 'all 0.3s ease-in-out'\n      };\n    }\n  };\n\n  // Update position when popup opens or guidepopup changes\n  useEffect(() => {\n    if (openGenAiImagePopup) {\n      const updatePosition = () => {\n        const newPosition = calculatePopupPosition();\n        setPopupPosition(newPosition);\n      };\n\n      // Initial position calculation\n      updatePosition();\n\n      // Set up observers for guidepopup changes\n      let resizeObserver = null;\n      let mutationObserver = null;\n      const elementsToObserve = [document.querySelector('.qadpt-guide-popup .MuiDialog-paper'), document.getElementById('guide-popup')].filter(Boolean);\n      if (window.ResizeObserver && elementsToObserve.length > 0) {\n        resizeObserver = new ResizeObserver(() => {\n          setTimeout(updatePosition, 10);\n        });\n        elementsToObserve.forEach(el => resizeObserver.observe(el));\n      }\n      if (window.MutationObserver && elementsToObserve.length > 0) {\n        mutationObserver = new MutationObserver(() => {\n          setTimeout(updatePosition, 10);\n        });\n        elementsToObserve.forEach(el => {\n          mutationObserver.observe(el, {\n            attributes: true,\n            attributeFilter: ['style', 'class'],\n            childList: true,\n            subtree: true\n          });\n        });\n      }\n\n      // Listen for window resize\n      const handleResize = () => setTimeout(updatePosition, 10);\n      window.addEventListener('resize', handleResize);\n      return () => {\n        if (resizeObserver) resizeObserver.disconnect();\n        if (mutationObserver) mutationObserver.disconnect();\n        window.removeEventListener('resize', handleResize);\n      };\n    }\n  }, [openGenAiImagePopup]);\n  const handleClose = () => {\n    setOpenGenAiImagePopup(false);\n  };\n  const onImageGenerated = imageUrl => {\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\n\n    let file = {\n      Url: imageUrl,\n      FileName: `Generated Image ${Date.now()}`,\n      IsAiGenerated: true\n    };\n    handleImageUploadFormApp(file);\n    setOpenGenAiImagePopup(false);\n  };\n  const handleEnhanceDescription = async () => {\n    if (description.trim() === \"\") {\n      openSnackbar(\"Please enter a description first.\", \"error\");\n      return;\n    }\n    setIsEnhancing(true);\n    try {\n      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\n      if (enhancedPrompt) {\n        setDescription(enhancedPrompt);\n      }\n    } catch (error) {\n      console.error(\"Error enhancing description:\", error);\n    } finally {\n      setIsEnhancing(false);\n    }\n  };\n  const GenerateImage = () => {\n    if (description === \"\") return;\n\n    // Validate that we have the correct image context\n    // containerId is required, but buttonId can be empty for new image generation\n    if (!imageAnchorEl.containerId) {\n      openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\n      return;\n    }\n    setIsGenerating(true); // Start loader\n    const controller = new AbortController();\n    setAbortController(controller);\n    const userPromptWithSelectedOptions = `User Asked: ${description} ${selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"} ${selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"} ${selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"}`;\n    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, imageUrl => {\n      onImageGenerated(imageUrl);\n      setIsGenerating(false); // Stop loader\n      setAbortController(null);\n    }, openSnackbar, controller.signal);\n  };\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: popupRef,\n      style: {\n        ...popupPosition,\n        height: 'calc(100vh - 55px)',\n        width: '280px',\n        backgroundColor: '#fff',\n        boxShadow: isInternalPosition ? '0 4px 8px rgba(0, 0, 0, 0.2)' : '-2px 0 8px rgba(0, 0, 0, 0.2)',\n        zIndex: 1000,\n        padding: '12px',\n        alignItems: \"end\",\n        opacity: openGenAiImagePopup ? 1 : 0,\n        visibility: openGenAiImagePopup ? 'visible' : 'hidden',\n        transform: openGenAiImagePopup ? 'translateX(0)' : 'translateX(20px)',\n        transition: 'all 0.3s ease-in-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"18px !important\"\n          },\n          children: translate(\"Generate Images\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer'\n          },\n          onClick: handleClose,\n          dangerouslySetInnerHTML: {\n            __html: closeIcon\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '93%',\n          marginTop: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          rows: 3,\n          placeholder: translate(\"Please describe your image...\"),\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          style: {\n            width: '93%',\n            height: '93px',\n            padding: '16px',\n            // Extra right padding for button space\n            fontSize: '12px',\n            borderRadius: '6px',\n            border: '1px solid #ccc',\n            resize: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleEnhanceDescription,\n          disabled: isEnhancing || description.trim() === \"\",\n          style: {\n            position: 'absolute',\n            top: '75%',\n            left: '10px',\n            transform: 'translateY(-50%)',\n            backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\n            color: 'black',\n            border: 'none',\n            padding: '4px 10px',\n            borderRadius: '100px',\n            fontSize: '10px',\n            cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\n            whiteSpace: 'nowrap',\n            alignItems: 'center',\n            display: 'flex',\n            gap: '6px',\n            opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5'\n          },\n          children: [isEnhancing ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '10px',\n              height: '10px',\n              border: '1px solid #333',\n              borderTop: '1px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 7\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: blackMagicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 7\n          }, this), isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Style\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: styles.map(style => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedStyle(style),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${style}`)\n          }, style, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Colors\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: colors.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedColor(color),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${color}`)\n          }, color, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: translate(\"Ratio\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: ratios.map(ratio => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedRatio(ratio),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: ratio\n          }, ratio, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 3\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (isGenerating) {\n            // Cancel if generating\n            abortController === null || abortController === void 0 ? void 0 : abortController.abort();\n            setIsGenerating(false);\n            setAbortController(null);\n          } else if (description.trim() !== '') {\n            // Don't call setReplaceImage(true) here as it's already set when popup opens\n            // and we need to maintain the correct imageAnchorEl context\n            GenerateImage();\n          }\n        },\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        disabled: isGenerating && !abortController,\n        style: {\n          marginTop: '20px',\n          width: '100%',\n          padding: '10px 16px',\n          borderRadius: '8px',\n          backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\n          color: '#fff',\n          border: 'none',\n          fontSize: '14px',\n          cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '8px',\n          transition: 'background-color 0.3s',\n          opacity: description.trim() === '' ? 0.7 : 1\n        },\n        children: isGenerating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '14px',\n              height: '14px',\n              border: '2px solid #fff',\n              borderTop: '2px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 7\n          }, this), isHovered ? translate('Cancel Generation') : translate('Generating...')]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: magicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 7\n          }, this), translate('Generate Image')]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ImageGenerationPopup, \"zBR9hIWshiSXaKkC6+AnfyywoeM=\", false, function () {\n  return [useTranslation, useSnackbar, useDrawerStore];\n});\n_c = ImageGenerationPopup;\nexport default ImageGenerationPopup;\nvar _c;\n$RefreshReg$(_c, \"ImageGenerationPopup\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "useRef", "closeIcon", "magicPen", "Box", "Typography", "GenerateImageWithUserPrompt", "EnhanceUserPrompt", "AccountContext", "useTranslation", "useSnackbar", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageGenerationPopup", "openGenAiImagePopup", "setOpenGenAiImagePopup", "handleImageUploadFormApp", "setReplaceImage", "_s", "t", "translate", "openSnackbar", "imageAnchorEl", "state", "isHovered", "setIsHovered", "abortController", "setAbortController", "popupPosition", "setPopupPosition", "isInternalPosition", "setIsInternalPosition", "popupRef", "selected<PERSON><PERSON><PERSON>", "setSelectedStyle", "selectedColor", "setSelectedColor", "selectedRatio", "setSelectedRatio", "accountId", "styles", "colors", "ratios", "description", "setDescription", "isGenerating", "setIsGenerating", "isEnhancing", "setIsEnhancing", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "top", "left", "width", "height", "calculatePopupPosition", "guidePopupPos", "position", "right", "viewportWidth", "window", "innerWidth", "popup<PERSON><PERSON><PERSON>", "gap", "externalLeftPosition", "hasSpaceForExternal", "transition", "updatePosition", "newPosition", "resizeObserver", "mutationObserver", "elementsToObserve", "filter", "Boolean", "ResizeObserver", "length", "setTimeout", "for<PERSON>ach", "el", "observe", "MutationObserver", "attributes", "attributeFilter", "childList", "subtree", "handleResize", "addEventListener", "disconnect", "removeEventListener", "handleClose", "onImageGenerated", "imageUrl", "file", "Url", "FileName", "Date", "now", "IsAiGenerated", "handleEnhanceDescription", "trim", "enhancedPrompt", "error", "console", "GenerateImage", "containerId", "controller", "AbortController", "userPromptWithSelectedOptions", "signal", "blackMagicPen", "replace", "children", "ref", "style", "backgroundColor", "boxShadow", "zIndex", "padding", "alignItems", "opacity", "visibility", "transform", "sx", "display", "justifyContent", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cursor", "onClick", "dangerouslySetInnerHTML", "__html", "marginTop", "rows", "placeholder", "value", "onChange", "e", "target", "borderRadius", "border", "resize", "disabled", "color", "whiteSpace", "borderTop", "animation", "flexWrap", "map", "ratio", "abort", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/ImageGenerationPopup.tsx"], "sourcesContent": ["import React, { useContext, useState, useEffect, useRef } from 'react';\r\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\r\nimport { Box,Typography } from '@mui/material';\r\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { FileUpload } from '../../../models/FileUpload';\r\nimport { timeStamp } from 'console';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useSnackbar } from '../guideList/SnackbarContext';\r\nimport useDrawerStore  from '../../../store/drawerStore';\r\n\r\n\r\n\r\n\r\n\r\nconst ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {\r\n  //const [isOpen, setIsOpen] = useState(false);\r\n\r\n  //const togglePopup = () => setIsOpen(!isOpen);\r\n  const { t: translate } = useTranslation();\r\n  const { openSnackbar } = useSnackbar();\r\n\r\n  // Get imageAnchorEl from store to know which image to replace\r\n  const { imageAnchorEl } = useDrawerStore((state) => state);\r\n\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [abortController, setAbortController] = useState<AbortController | null>(null);\r\n  const [popupPosition, setPopupPosition] = useState<any>({});\r\n  const [isInternalPosition, setIsInternalPosition] = useState(false);\r\n  const popupRef = useRef<HTMLDivElement>(null);\r\n\r\n\r\nconst [selectedStyle, setSelectedStyle] = useState('Professional');\r\n  const [selectedColor, setSelectedColor] = useState('Black & White');\r\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\r\n\r\n  const { accountId } = useContext(AccountContext);\r\n\r\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\r\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\r\n  const ratios = ['1:1', '16:9','9:16'];\r\n  const [description, setDescription] = useState('');\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n  // Function to get guidepopup position\r\n  const getGuidePopupPosition = () => {\r\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n                    document.getElementById('guide-popup');\r\n    if (element) {\r\n      const rect = element.getBoundingClientRect();\r\n      return {\r\n        top: rect.top,\r\n        left: rect.left,\r\n        width: rect.width,\r\n        height: rect.height\r\n      };\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Function to calculate adaptive positioning\r\n  const calculatePopupPosition = () => {\r\n    const guidePopupPos = getGuidePopupPosition();\r\n    if (!guidePopupPos) return { position: 'fixed', right: '0px', top: '55px' };\r\n\r\n    const viewportWidth = window.innerWidth;\r\n    const popupWidth = 280; // ImageGenerationPopup width\r\n    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup\r\n\r\n    // Calculate external position (right side of guidepopup)\r\n    const externalLeftPosition = guidePopupPos.left + guidePopupPos.width + gap;\r\n    const hasSpaceForExternal = externalLeftPosition + popupWidth <= viewportWidth - 10;\r\n\r\n    if (hasSpaceForExternal) {\r\n      // External positioning - to the right side of guidepopup\r\n      setIsInternalPosition(false);\r\n      return {\r\n        position: 'fixed' as const,\r\n        left: `${externalLeftPosition}px`,\r\n        top: `${guidePopupPos.top}px`,\r\n        right: 'auto',\r\n        transition: 'all 0.3s ease-in-out'\r\n      };\r\n    } else {\r\n      // Internal positioning - aligned to right side within guidepopup\r\n      setIsInternalPosition(true);\r\n      return {\r\n        position: 'fixed' as const,\r\n        left: `${guidePopupPos.left + guidePopupPos.width - popupWidth - 15}px`, // 15px from right edge\r\n        top: `${guidePopupPos.top}px`,\r\n        right: 'auto',\r\n        transition: 'all 0.3s ease-in-out'\r\n      };\r\n    }\r\n  };\r\n\r\n  // Update position when popup opens or guidepopup changes\r\n  useEffect(() => {\r\n    if (openGenAiImagePopup) {\r\n      const updatePosition = () => {\r\n        const newPosition = calculatePopupPosition();\r\n        setPopupPosition(newPosition);\r\n      };\r\n\r\n      // Initial position calculation\r\n      updatePosition();\r\n\r\n      // Set up observers for guidepopup changes\r\n      let resizeObserver: ResizeObserver | null = null;\r\n      let mutationObserver: MutationObserver | null = null;\r\n\r\n      const elementsToObserve = [\r\n        document.querySelector('.qadpt-guide-popup .MuiDialog-paper'),\r\n        document.getElementById('guide-popup')\r\n      ].filter(Boolean) as HTMLElement[];\r\n\r\n      if (window.ResizeObserver && elementsToObserve.length > 0) {\r\n        resizeObserver = new ResizeObserver(() => {\r\n          setTimeout(updatePosition, 10);\r\n        });\r\n        elementsToObserve.forEach(el => resizeObserver!.observe(el));\r\n      }\r\n\r\n      if (window.MutationObserver && elementsToObserve.length > 0) {\r\n        mutationObserver = new MutationObserver(() => {\r\n          setTimeout(updatePosition, 10);\r\n        });\r\n        elementsToObserve.forEach(el => {\r\n          mutationObserver!.observe(el, {\r\n            attributes: true,\r\n            attributeFilter: ['style', 'class'],\r\n            childList: true,\r\n            subtree: true\r\n          });\r\n        });\r\n      }\r\n\r\n      // Listen for window resize\r\n      const handleResize = () => setTimeout(updatePosition, 10);\r\n      window.addEventListener('resize', handleResize);\r\n\r\n      return () => {\r\n        if (resizeObserver) resizeObserver.disconnect();\r\n        if (mutationObserver) mutationObserver.disconnect();\r\n        window.removeEventListener('resize', handleResize);\r\n      };\r\n    }\r\n  }, [openGenAiImagePopup]);\r\n\r\n\r\n  const handleClose = () => {\r\n  setOpenGenAiImagePopup(false);\r\n  };\r\n\r\n  const onImageGenerated = (imageUrl: any) => {\r\n    \r\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\r\n\r\n    let file: any = {\r\n      Url: imageUrl,\r\n      FileName: `Generated Image ${Date.now()}`,\r\n      IsAiGenerated: true,\r\n    };\r\n    \r\n\r\n    handleImageUploadFormApp(file);\r\n    setOpenGenAiImagePopup(false);\r\n\r\n  }\r\n\r\nconst handleEnhanceDescription = async () => {\r\n  if (description.trim() === \"\") {\r\n    openSnackbar(\"Please enter a description first.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsEnhancing(true);\r\n  try {\r\n    const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\r\n    if (enhancedPrompt) {\r\n      setDescription(enhancedPrompt);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error enhancing description:\", error);\r\n  } finally {\r\n    setIsEnhancing(false);\r\n  }\r\n};\r\n\r\nconst GenerateImage = () => {\r\n  if (description === \"\") return;\r\n\r\n  // Validate that we have the correct image context\r\n  // containerId is required, but buttonId can be empty for new image generation\r\n  if (!imageAnchorEl.containerId) {\r\n    openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsGenerating(true); // Start loader\r\n    const controller = new AbortController();\r\n  setAbortController(controller);\r\n\r\nconst userPromptWithSelectedOptions = `User Asked: ${description} ${\r\n  selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"\r\n} ${\r\n  selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"\r\n} ${\r\n  selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"\r\n}`;\r\n\r\n\r\n  GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {\r\n    onImageGenerated(imageUrl);\r\n    setIsGenerating(false); // Stop loader\r\n    setAbortController(null);\r\n  }, openSnackbar, controller.signal);\r\n};\r\n\r\n    const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n  \r\n\r\n  return (\r\n    <>\r\n      <div\r\n        ref={popupRef}\r\n        style={{\r\n          ...popupPosition,\r\n          height: 'calc(100vh - 55px)',\r\n          width: '280px',\r\n          backgroundColor: '#fff',\r\n          boxShadow: isInternalPosition\r\n            ? '0 4px 8px rgba(0, 0, 0, 0.2)'\r\n            : '-2px 0 8px rgba(0, 0, 0, 0.2)',\r\n          zIndex: 1000,\r\n          padding: '12px',\r\n          alignItems: \"end\",\r\n          opacity: openGenAiImagePopup ? 1 : 0,\r\n          visibility: openGenAiImagePopup ? 'visible' : 'hidden',\r\n          transform: openGenAiImagePopup ? 'translateX(0)' : 'translateX(20px)',\r\n          transition: 'all 0.3s ease-in-out'\r\n        }}\r\n          >\r\n              <Box sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"space-between\",\r\n                  \r\n              }}\r\n              >\r\n                  <Typography sx={{fontSize: \"18px !important\"}}>\r\n            \r\n            {translate(\"Generate Images\")}\r\n                  </Typography>\r\n                \r\n                \r\n                    <span style={{ cursor: 'pointer' }} onClick={handleClose} dangerouslySetInnerHTML={{ __html: closeIcon }}/>\r\n              </Box>\r\n              \r\n\r\n        <div style={{ position: 'relative', width: '93%', marginTop: '10px' }}>\r\n  <textarea\r\n    rows={3}\r\n    placeholder={translate(\"Please describe your image...\")}\r\n    value={description}\r\n    onChange={(e) => setDescription(e.target.value)}\r\n    style={{\r\n      width: '93%',\r\n      height: '93px',\r\n      padding: '16px', // Extra right padding for button space\r\n      fontSize: '12px',\r\n      borderRadius: '6px',\r\n      border: '1px solid #ccc',\r\n      resize: 'none',\r\n    }}\r\n  />\r\n  <button\r\n    onClick={handleEnhanceDescription}\r\n    disabled={isEnhancing || description.trim() === \"\"}\r\n    style={{\r\n      position: 'absolute',\r\n      top: '75%',\r\n      left: '10px',\r\n      transform: 'translateY(-50%)',\r\n      backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\r\n      color: 'black',\r\n      border: 'none',\r\n      padding: '4px 10px',\r\n      borderRadius: '100px',\r\n      fontSize: '10px',\r\n      cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\r\n      whiteSpace: 'nowrap',\r\n      alignItems: 'center',\r\n      display: 'flex',\r\n      gap: '6px',\r\n      opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5',\r\n    }}\r\n          >\r\n    {isEnhancing ? (\r\n      <div\r\n        style={{\r\n          width: '10px',\r\n          height: '10px',\r\n          border: '1px solid #333',\r\n          borderTop: '1px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n    ) : (\r\n      <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} />\r\n    )}\r\n\r\n    {isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")}\r\n  </button>\r\n</div>\r\n\r\n\r\n              {/* You can add form, preview, upload, etc. inside here */}\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Style\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }} >\r\n          {styles.map((style) => (\r\n            <button\r\n              key={style}\r\n              onClick={() => setSelectedStyle(style)}\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n            >\r\n               {translate(`${style}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n              </div>\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Colors\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n          {colors.map((color) => (\r\n            <button\r\n              key={color}\r\n              onClick={() => setSelectedColor(color)}\r\n\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n              \r\n            >\r\n               {translate(`${color}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n        </div>\r\n        <div>\r\n  <h2>{translate(\"Ratio\")}</h2>\r\n  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n{ratios.map((ratio) => (\r\n  <button\r\n    key={ratio}\r\n    onClick={() => setSelectedRatio(ratio)}\r\n    style={{\r\n      padding: '5px 12px',\r\n      borderRadius: '8px',\r\n      border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n      backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\r\n      color: '#333',\r\n      cursor: 'pointer',\r\n      fontSize: '11px',\r\n    }}\r\n  >\r\n    {ratio}\r\n  </button>\r\n))}\r\n\r\n  </div>\r\n</div>\r\n\r\n<button\r\n  onClick={() => {\r\n    if (isGenerating) {\r\n      // Cancel if generating\r\n      abortController?.abort();\r\n      setIsGenerating(false);\r\n      setAbortController(null);\r\n    } else if (description.trim() !== '') {\r\n      // Don't call setReplaceImage(true) here as it's already set when popup opens\r\n      // and we need to maintain the correct imageAnchorEl context\r\n      GenerateImage();\r\n    }\r\n  }}\r\n  onMouseEnter={() => setIsHovered(true)}\r\n  onMouseLeave={() => setIsHovered(false)}\r\n  disabled={isGenerating && !abortController}\r\n  style={{\r\n    marginTop: '20px',\r\n    width: '100%',\r\n    padding: '10px 16px',\r\n    borderRadius: '8px',\r\n    backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\r\n    color: '#fff',\r\n    border: 'none',\r\n    fontSize: '14px',\r\n    cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '8px',\r\n    transition: 'background-color 0.3s',\r\n    opacity: description.trim() === '' ? 0.7 : 1,\r\n  }}\r\n>\r\n  {isGenerating ? (\r\n    <>\r\n      <div\r\n        style={{\r\n          width: '14px',\r\n          height: '14px',\r\n          border: '2px solid #fff',\r\n          borderTop: '2px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n      {isHovered ? translate('Cancel Generation') : translate('Generating...')}\r\n    </>\r\n  ) : (\r\n    <>\r\n      <span dangerouslySetInnerHTML={{ __html: magicPen }} />\r\n      {translate('Generate Image')}\r\n    </>\r\n  )}\r\n</button>\r\n\r\n\r\n          </div>\r\n          \r\n    </>\r\n  );\r\n};\r\n\r\nexport default ImageGenerationPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACtE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,6BAA6B;AACjE,SAASC,GAAG,EAACC,UAAU,QAAQ,eAAe;AAC9C,SAASC,2BAA2B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC5F,SAASC,cAAc,QAAQ,4BAA4B;AAG3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAOC,cAAc,MAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMzD,MAAMC,oBAAoB,GAAGA,CAAC;EAACC,mBAAmB;EAACC,sBAAsB;EAACC,wBAAwB;EAACC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC1H;;EAEA;EACA,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGd,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEe;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAM;IAAEe;EAAc,CAAC,GAAGd,cAAc,CAAEe,KAAK,IAAKA,KAAK,CAAC;EAE1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAM,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAMoC,QAAQ,GAAGlC,MAAM,CAAiB,IAAI,CAAC;EAG/C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,cAAc,CAAC;EAChE,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,eAAe,CAAC;EACnE,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,MAAM,CAAC;EAE1D,MAAM;IAAE2C;EAAU,CAAC,GAAG5C,UAAU,CAACU,cAAc,CAAC;EAEhD,MAAMmC,MAAM,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;EACzF,MAAMC,MAAM,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;EACxF,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAC,MAAM,CAAC;EACrC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMqD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IAC7DD,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACLC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfC,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,MAAM,EAAEL,IAAI,CAACK;MACf,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,aAAa,GAAGZ,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACY,aAAa,EAAE,OAAO;MAAEC,QAAQ,EAAE,OAAO;MAAEC,KAAK,EAAE,KAAK;MAAEP,GAAG,EAAE;IAAO,CAAC;IAE3E,MAAMQ,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,UAAU,GAAG,GAAG,CAAC,CAAC;IACxB,MAAMC,GAAG,GAAG,EAAE,CAAC,CAAC;;IAEhB;IACA,MAAMC,oBAAoB,GAAGR,aAAa,CAACJ,IAAI,GAAGI,aAAa,CAACH,KAAK,GAAGU,GAAG;IAC3E,MAAME,mBAAmB,GAAGD,oBAAoB,GAAGF,UAAU,IAAIH,aAAa,GAAG,EAAE;IAEnF,IAAIM,mBAAmB,EAAE;MACvB;MACAvC,qBAAqB,CAAC,KAAK,CAAC;MAC5B,OAAO;QACL+B,QAAQ,EAAE,OAAgB;QAC1BL,IAAI,EAAE,GAAGY,oBAAoB,IAAI;QACjCb,GAAG,EAAE,GAAGK,aAAa,CAACL,GAAG,IAAI;QAC7BO,KAAK,EAAE,MAAM;QACbQ,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM;MACL;MACAxC,qBAAqB,CAAC,IAAI,CAAC;MAC3B,OAAO;QACL+B,QAAQ,EAAE,OAAgB;QAC1BL,IAAI,EAAE,GAAGI,aAAa,CAACJ,IAAI,GAAGI,aAAa,CAACH,KAAK,GAAGS,UAAU,GAAG,EAAE,IAAI;QAAE;QACzEX,GAAG,EAAE,GAAGK,aAAa,CAACL,GAAG,IAAI;QAC7BO,KAAK,EAAE,MAAM;QACbQ,UAAU,EAAE;MACd,CAAC;IACH;EACF,CAAC;;EAED;EACA1E,SAAS,CAAC,MAAM;IACd,IAAIiB,mBAAmB,EAAE;MACvB,MAAM0D,cAAc,GAAGA,CAAA,KAAM;QAC3B,MAAMC,WAAW,GAAGb,sBAAsB,CAAC,CAAC;QAC5C/B,gBAAgB,CAAC4C,WAAW,CAAC;MAC/B,CAAC;;MAED;MACAD,cAAc,CAAC,CAAC;;MAEhB;MACA,IAAIE,cAAqC,GAAG,IAAI;MAChD,IAAIC,gBAAyC,GAAG,IAAI;MAEpD,MAAMC,iBAAiB,GAAG,CACxBzB,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,EAC7DD,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC,CACvC,CAACwB,MAAM,CAACC,OAAO,CAAkB;MAElC,IAAIb,MAAM,CAACc,cAAc,IAAIH,iBAAiB,CAACI,MAAM,GAAG,CAAC,EAAE;QACzDN,cAAc,GAAG,IAAIK,cAAc,CAAC,MAAM;UACxCE,UAAU,CAACT,cAAc,EAAE,EAAE,CAAC;QAChC,CAAC,CAAC;QACFI,iBAAiB,CAACM,OAAO,CAACC,EAAE,IAAIT,cAAc,CAAEU,OAAO,CAACD,EAAE,CAAC,CAAC;MAC9D;MAEA,IAAIlB,MAAM,CAACoB,gBAAgB,IAAIT,iBAAiB,CAACI,MAAM,GAAG,CAAC,EAAE;QAC3DL,gBAAgB,GAAG,IAAIU,gBAAgB,CAAC,MAAM;UAC5CJ,UAAU,CAACT,cAAc,EAAE,EAAE,CAAC;QAChC,CAAC,CAAC;QACFI,iBAAiB,CAACM,OAAO,CAACC,EAAE,IAAI;UAC9BR,gBAAgB,CAAES,OAAO,CAACD,EAAE,EAAE;YAC5BG,UAAU,EAAE,IAAI;YAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YACnCC,SAAS,EAAE,IAAI;YACfC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAMT,UAAU,CAACT,cAAc,EAAE,EAAE,CAAC;MACzDP,MAAM,CAAC0B,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;MAE/C,OAAO,MAAM;QACX,IAAIhB,cAAc,EAAEA,cAAc,CAACkB,UAAU,CAAC,CAAC;QAC/C,IAAIjB,gBAAgB,EAAEA,gBAAgB,CAACiB,UAAU,CAAC,CAAC;QACnD3B,MAAM,CAAC4B,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;MACpD,CAAC;IACH;EACF,CAAC,EAAE,CAAC5E,mBAAmB,CAAC,CAAC;EAGzB,MAAMgF,WAAW,GAAGA,CAAA,KAAM;IAC1B/E,sBAAsB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMgF,gBAAgB,GAAIC,QAAa,IAAK;IAE1C;;IAEA,IAAIC,IAAS,GAAG;MACdC,GAAG,EAAEF,QAAQ;MACbG,QAAQ,EAAE,mBAAmBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACzCC,aAAa,EAAE;IACjB,CAAC;IAGDtF,wBAAwB,CAACiF,IAAI,CAAC;IAC9BlF,sBAAsB,CAAC,KAAK,CAAC;EAE/B,CAAC;EAEH,MAAMwF,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI5D,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7BnF,YAAY,CAAC,mCAAmC,EAAE,OAAO,CAAC;MAC1D;IACF;IAEA2B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMyD,cAAc,GAAG,MAAMrG,iBAAiB,CAACuC,WAAW,EAAEJ,SAAS,EAAElB,YAAY,CAAC;MACpF,IAAIoF,cAAc,EAAE;QAClB7D,cAAc,CAAC6D,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACR1D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM4D,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIjE,WAAW,KAAK,EAAE,EAAE;;IAExB;IACA;IACA,IAAI,CAACrB,aAAa,CAACuF,WAAW,EAAE;MAC9BxF,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;MAClG;IACF;IAEAyB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACrB,MAAMgE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC1CpF,kBAAkB,CAACmF,UAAU,CAAC;IAEhC,MAAME,6BAA6B,GAAG,eAAerE,WAAW,IAC9DV,aAAa,KAAK,EAAE,GAAG,oCAAoC,GAAGA,aAAa,GAAG,EAAE,IAEhFE,aAAa,KAAK,EAAE,GAAG,wCAAwC,GAAGA,aAAa,GAAG,EAAE,IAEpFE,aAAa,KAAK,EAAE,GAAG,uCAAuC,GAAGA,aAAa,GAAG,EAAE,EACnF;IAGAlC,2BAA2B,CAAC6G,6BAA6B,EAAEzE,SAAS,EAAGyD,QAAa,IAAK;MACvFD,gBAAgB,CAACC,QAAQ,CAAC;MAC1BlD,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MACxBnB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,EAAEN,YAAY,EAAEyF,UAAU,CAACG,MAAM,CAAC;EACrC,CAAC;EAEG,MAAMC,aAAa,GAAGlH,QAAQ,CAACmH,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAG7E,oBACEzG,OAAA,CAAAE,SAAA;IAAAwG,QAAA,eACE1G,OAAA;MACE2G,GAAG,EAAErF,QAAS;MACdsF,KAAK,EAAE;QACL,GAAG1F,aAAa;QAChB+B,MAAM,EAAE,oBAAoB;QAC5BD,KAAK,EAAE,OAAO;QACd6D,eAAe,EAAE,MAAM;QACvBC,SAAS,EAAE1F,kBAAkB,GACzB,8BAA8B,GAC9B,+BAA+B;QACnC2F,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,KAAK;QACjBC,OAAO,EAAE9G,mBAAmB,GAAG,CAAC,GAAG,CAAC;QACpC+G,UAAU,EAAE/G,mBAAmB,GAAG,SAAS,GAAG,QAAQ;QACtDgH,SAAS,EAAEhH,mBAAmB,GAAG,eAAe,GAAG,kBAAkB;QACrEyD,UAAU,EAAE;MACd,CAAE;MAAA6C,QAAA,gBAEI1G,OAAA,CAACT,GAAG;QAAC8H,EAAE,EAAE;UACLC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE;QAEpB,CAAE;QAAAb,QAAA,gBAEE1G,OAAA,CAACR,UAAU;UAAC6H,EAAE,EAAE;YAACG,QAAQ,EAAE;UAAiB,CAAE;UAAAd,QAAA,EAEnDhG,SAAS,CAAC,iBAAiB;QAAC;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAGX5H,OAAA;UAAM4G,KAAK,EAAE;YAAEiB,MAAM,EAAE;UAAU,CAAE;UAACC,OAAO,EAAE1C,WAAY;UAAC2C,uBAAuB,EAAE;YAAEC,MAAM,EAAE3I;UAAU;QAAE;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC,eAGZ5H,OAAA;QAAK4G,KAAK,EAAE;UAAExD,QAAQ,EAAE,UAAU;UAAEJ,KAAK,EAAE,KAAK;UAAEiF,SAAS,EAAE;QAAO,CAAE;QAAAvB,QAAA,gBAC5E1G,OAAA;UACEkI,IAAI,EAAE,CAAE;UACRC,WAAW,EAAEzH,SAAS,CAAC,+BAA+B,CAAE;UACxD0H,KAAK,EAAEnG,WAAY;UACnBoG,QAAQ,EAAGC,CAAC,IAAKpG,cAAc,CAACoG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDxB,KAAK,EAAE;YACL5D,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,MAAM;YACd+D,OAAO,EAAE,MAAM;YAAE;YACjBQ,QAAQ,EAAE,MAAM;YAChBgB,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,gBAAgB;YACxBC,MAAM,EAAE;UACV;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF5H,OAAA;UACE8H,OAAO,EAAEjC,wBAAyB;UAClC8C,QAAQ,EAAEtG,WAAW,IAAIJ,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAG;UACnDc,KAAK,EAAE;YACLxD,QAAQ,EAAE,UAAU;YACpBN,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,MAAM;YACZqE,SAAS,EAAE,kBAAkB;YAC7BP,eAAe,EAAExE,WAAW,IAAIJ,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,WAAW;YACrF8C,KAAK,EAAE,OAAO;YACdH,MAAM,EAAE,MAAM;YACdzB,OAAO,EAAE,UAAU;YACnBwB,YAAY,EAAE,OAAO;YACrBhB,QAAQ,EAAE,MAAM;YAChBK,MAAM,EAAExF,WAAW,IAAIJ,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,aAAa,GAAG,SAAS;YAC5E+C,UAAU,EAAE,QAAQ;YACpB5B,UAAU,EAAE,QAAQ;YACpBK,OAAO,EAAE,MAAM;YACf5D,GAAG,EAAE,KAAK;YACVwD,OAAO,EAAE7E,WAAW,IAAIJ,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG;UAC9D,CAAE;UAAAY,QAAA,GAEDrE,WAAW,gBACVrC,OAAA;YACE4G,KAAK,EAAE;cACL5D,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdwF,MAAM,EAAE,gBAAgB;cACxBK,SAAS,EAAE,uBAAuB;cAClCN,YAAY,EAAE,KAAK;cACnBO,SAAS,EAAE;YACb;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF5H,OAAA;YAAM+H,uBAAuB,EAAE;cAAEC,MAAM,EAAExB;YAAc;UAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5D,EAEAvF,WAAW,GAAG3B,SAAS,CAAC,cAAc,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAC;QAAA;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAKQ5H,OAAA;QAAA0G,QAAA,gBACN1G,OAAA;UAAA0G,QAAA,GAAK,GAAC,EAAChG,SAAS,CAAC,OAAO,CAAC;QAAA;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/B5H,OAAA;UAAK4G,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAE0B,QAAQ,EAAE,MAAM;YAAEtF,GAAG,EAAE,KAAK;YAAEuE,SAAS,EAAE;UAAO,CAAE;UAAAvB,QAAA,EAC9E5E,MAAM,CAACmH,GAAG,CAAErC,KAAK,iBAChB5G,OAAA;YAEE8H,OAAO,EAAEA,CAAA,KAAMtG,gBAAgB,CAACoF,KAAK,CAAE;YACjCA,KAAK,EAAE;cACLI,OAAO,EAAE,UAAU;cACnBwB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAElH,aAAa,KAAKqF,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxEC,eAAe,EAAEtF,aAAa,KAAKqF,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEgC,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,EAENhG,SAAS,CAAC,GAAGkG,KAAK,EAAE;UAAC,GAZlBA,KAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEN5H,OAAA;QAAA0G,QAAA,gBACN1G,OAAA;UAAA0G,QAAA,GAAK,GAAC,EAAChG,SAAS,CAAC,QAAQ,CAAC;QAAA;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChC5H,OAAA;UAAK4G,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAE0B,QAAQ,EAAE,MAAM;YAAEtF,GAAG,EAAE,KAAK;YAAEuE,SAAS,EAAE;UAAO,CAAE;UAAAvB,QAAA,EAC9E3E,MAAM,CAACkH,GAAG,CAAEL,KAAK,iBAChB5I,OAAA;YAEE8H,OAAO,EAAEA,CAAA,KAAMpG,gBAAgB,CAACkH,KAAK,CAAE;YAEjChC,KAAK,EAAE;cACLI,OAAO,EAAE,UAAU;cACnBwB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAEhH,aAAa,KAAKmH,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxE/B,eAAe,EAAEpF,aAAa,KAAKmH,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEA,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,EAGNhG,SAAS,CAAC,GAAGkI,KAAK,EAAE;UAAC,GAdlBA,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN5H,OAAA;QAAA0G,QAAA,gBACN1G,OAAA;UAAA0G,QAAA,EAAKhG,SAAS,CAAC,OAAO;QAAC;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B5H,OAAA;UAAK4G,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAE0B,QAAQ,EAAE,MAAM;YAAEtF,GAAG,EAAE,KAAK;YAAEuE,SAAS,EAAE;UAAO,CAAE;UAAAvB,QAAA,EAClF1E,MAAM,CAACiH,GAAG,CAAEC,KAAK,iBAChBlJ,OAAA;YAEE8H,OAAO,EAAEA,CAAA,KAAMlG,gBAAgB,CAACsH,KAAK,CAAE;YACvCtC,KAAK,EAAE;cACLI,OAAO,EAAE,UAAU;cACnBwB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE9G,aAAa,KAAKuH,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxErC,eAAe,EAAElF,aAAa,KAAKuH,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEN,KAAK,EAAE,MAAM;cACbf,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAd,QAAA,EAEDwC;UAAK,GAZDA,KAAK;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5H,OAAA;QACE8H,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI3F,YAAY,EAAE;YAChB;YACAnB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmI,KAAK,CAAC,CAAC;YACxB/G,eAAe,CAAC,KAAK,CAAC;YACtBnB,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM,IAAIgB,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC;YACA;YACAI,aAAa,CAAC,CAAC;UACjB;QACF,CAAE;QACFkD,YAAY,EAAEA,CAAA,KAAMrI,YAAY,CAAC,IAAI,CAAE;QACvCsI,YAAY,EAAEA,CAAA,KAAMtI,YAAY,CAAC,KAAK,CAAE;QACxC4H,QAAQ,EAAExG,YAAY,IAAI,CAACnB,eAAgB;QAC3C4F,KAAK,EAAE;UACLqB,SAAS,EAAE,MAAM;UACjBjF,KAAK,EAAE,MAAM;UACbgE,OAAO,EAAE,WAAW;UACpBwB,YAAY,EAAE,KAAK;UACnB3B,eAAe,EAAE1E,YAAY,GAAG,SAAS,GAAGF,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UAC7F8C,KAAK,EAAE,MAAM;UACbH,MAAM,EAAE,MAAM;UACdjB,QAAQ,EAAE,MAAM;UAChBK,MAAM,EAAE1F,YAAY,IAAIF,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UACzEwB,OAAO,EAAE,MAAM;UACfL,UAAU,EAAE,QAAQ;UACpBM,cAAc,EAAE,QAAQ;UACxB7D,GAAG,EAAE,KAAK;UACVG,UAAU,EAAE,uBAAuB;UACnCqD,OAAO,EAAEjF,WAAW,CAAC6D,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;QAC7C,CAAE;QAAAY,QAAA,EAEDvE,YAAY,gBACXnC,OAAA,CAAAE,SAAA;UAAAwG,QAAA,gBACE1G,OAAA;YACE4G,KAAK,EAAE;cACL5D,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdwF,MAAM,EAAE,gBAAgB;cACxBK,SAAS,EAAE,uBAAuB;cAClCN,YAAY,EAAE,KAAK;cACnBO,SAAS,EAAE;YACb;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD9G,SAAS,GAAGJ,SAAS,CAAC,mBAAmB,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAC;QAAA,eACxE,CAAC,gBAEHV,OAAA,CAAAE,SAAA;UAAAwG,QAAA,gBACE1G,OAAA;YAAM+H,uBAAuB,EAAE;cAAEC,MAAM,EAAE1I;YAAS;UAAE;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtDlH,SAAS,CAAC,gBAAgB,CAAC;QAAA,eAC5B;MACH;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGM;EAAC,gBAEV,CAAC;AAEP,CAAC;AAACpH,EAAA,CArbIL,oBAAoB;EAAA,QAICP,cAAc,EACdC,WAAW,EAGVC,cAAc;AAAA;AAAAwJ,EAAA,GARpCnJ,oBAAoB;AAub1B,eAAeA,oBAAoB;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}