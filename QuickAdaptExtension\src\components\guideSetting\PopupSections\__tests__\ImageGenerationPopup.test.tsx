import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageGenerationPopup from '../ImageGenerationPopup';

// Mock the dependencies
jest.mock('../../../../store/drawerStore', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    imageAnchorEl: { containerId: 'test-container' }
  }))
}));

jest.mock('../../../login/AccountContext', () => ({
  AccountContext: {
    Consumer: ({ children }: any) => children({ accountId: 'test-account' })
  }
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

jest.mock('../../guideList/SnackbarContext', () => ({
  useSnackbar: () => ({
    openSnackbar: jest.fn()
  })
}));

// Mock DOM methods
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 1920,
});

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  configurable: true,
  value: jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
  })),
});

Object.defineProperty(window, 'MutationObserver', {
  writable: true,
  configurable: true,
  value: jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
  })),
});

// Mock querySelector to return a mock guidepopup element
const mockGuidePopupElement = {
  getBoundingClientRect: jest.fn(() => ({
    top: 100,
    left: 200,
    width: 500,
    height: 400
  }))
};

document.querySelector = jest.fn((selector) => {
  if (selector.includes('qadpt-guide-popup')) {
    return mockGuidePopupElement;
  }
  return null;
});

describe('ImageGenerationPopup Positioning', () => {
  const defaultProps = {
    openGenAiImagePopup: true,
    setOpenGenAiImagePopup: jest.fn(),
    handleImageUploadFormApp: jest.fn(),
    setReplaceImage: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should position externally when there is sufficient viewport space', async () => {
    // Set viewport width to allow external positioning
    Object.defineProperty(window, 'innerWidth', { value: 1920 });
    
    render(<ImageGenerationPopup {...defaultProps} />);
    
    await waitFor(() => {
      const popup = document.querySelector('.image-generation-popup');
      expect(popup).toBeInTheDocument();
    });
  });

  test('should position internally when viewport space is insufficient', async () => {
    // Set narrow viewport width to force internal positioning
    Object.defineProperty(window, 'innerWidth', { value: 800 });
    
    render(<ImageGenerationPopup {...defaultProps} />);
    
    await waitFor(() => {
      const popup = document.querySelector('.image-generation-popup');
      expect(popup).toBeInTheDocument();
    });
  });

  test('should handle guidepopup width changes', async () => {
    render(<ImageGenerationPopup {...defaultProps} />);
    
    // Simulate guidepopup width change
    mockGuidePopupElement.getBoundingClientRect.mockReturnValue({
      top: 100,
      left: 200,
      width: 800, // Increased width
      height: 400
    });
    
    // Trigger resize event
    fireEvent(window, new Event('resize'));
    
    await waitFor(() => {
      const popup = document.querySelector('.image-generation-popup');
      expect(popup).toBeInTheDocument();
    });
  });

  test('should close popup when close button is clicked', () => {
    const setOpenGenAiImagePopup = jest.fn();
    
    render(<ImageGenerationPopup 
      {...defaultProps} 
      setOpenGenAiImagePopup={setOpenGenAiImagePopup}
    />);
    
    const closeButton = screen.getByRole('button', { name: /close/i }) || 
                       document.querySelector('[dangerouslySetInnerHTML]');
    
    if (closeButton) {
      fireEvent.click(closeButton);
      expect(setOpenGenAiImagePopup).toHaveBeenCalledWith(false);
    }
  });
});
