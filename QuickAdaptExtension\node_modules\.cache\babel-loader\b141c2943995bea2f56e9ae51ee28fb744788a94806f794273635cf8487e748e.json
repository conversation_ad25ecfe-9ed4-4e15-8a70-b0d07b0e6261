{"ast": null, "code": "import { adminApiService, userApiService } from './APIService';\nexport const NewAgentTraining = async agent => {\n  try {\n    const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error uploading agent\", error);\n    return {\n      message: \"Upload failed\"\n    };\n  }\n};\n_c = NewAgentTraining;\nexport const CreateInteraction = async (userCommand, accountId, targetUrl) => {\n  try {\n    const requestBody = {\n      userCommand,\n      accountId,\n      targetUrl\n    };\n    const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error in creating integration\", error);\n    return [];\n  }\n};\n_c2 = CreateInteraction;\nexport const GenerateImageWithUserPrompt = async (userPrompt, accountId, onImageGenerated, openSnackbar, signal) => {\n  try {\n    const requestBody = {\n      userPrompt,\n      accountId\n    };\n    const response = await adminApiService.post(`Ai/GenerateImageOnUserPrompt`, requestBody, {\n      signal // 🆕 attach signal to cancel the request\n    });\n    if (response.data && response.data.Success) {\n      onImageGenerated(response.data.SuccessMessage);\n      return response.data;\n    } else {\n      openSnackbar(response.data.ErrorMessage, \"error\");\n      return null;\n    }\n  } catch (error) {\n    if (error.name === \"CanceledError\" || error.code === \"ERR_CANCELED\") {\n      console.warn(\"Image generation canceled by user.\");\n    } else {\n      console.error(\"Error generating image:\", error);\n      openSnackbar === null || openSnackbar === void 0 ? void 0 : openSnackbar(\"An error occurred while generating the image. Please try again.\", \"error\");\n    }\n    return null;\n  }\n};\n_c3 = GenerateImageWithUserPrompt;\nexport const EnhanceUserPrompt = async (userPrompt, accountId, openSnackbar) => {\n  try {\n    const requestBody = {\n      userPrompt,\n      accountId\n    };\n    const response = await adminApiService.post(`Ai/EnhanceUserPrompt`, requestBody);\n    if (response.data && response.data.Success) {\n      return response.data.SuccessMessage;\n    } else {\n      if (openSnackbar) {\n        openSnackbar(response.data.ErrorMessage || \"Failed to enhance prompt. Please try again.\", \"error\");\n      }\n      return null;\n    }\n  } catch (error) {\n    console.error(\"Error in enhancing prompt\", error);\n    if (openSnackbar) {\n      openSnackbar(\"An error occurred while enhancing the prompt. Please try again.\", \"error\");\n    }\n    return null;\n  }\n};\n_c4 = EnhanceUserPrompt;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"NewAgentTraining\");\n$RefreshReg$(_c2, \"CreateInteraction\");\n$RefreshReg$(_c3, \"GenerateImageWithUserPrompt\");\n$RefreshReg$(_c4, \"EnhanceUserPrompt\");", "map": {"version": 3, "names": ["adminApiService", "userApiService", "NewAgentTraining", "agent", "response", "post", "data", "error", "console", "message", "_c", "CreateInteraction", "userCommand", "accountId", "targetUrl", "requestBody", "_c2", "GenerateImageWithUserPrompt", "userPrompt", "onImageGenerated", "openSnackbar", "signal", "Success", "SuccessMessage", "ErrorMessage", "name", "code", "warn", "_c3", "EnhanceUserPrompt", "_c4", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/services/AIService.ts"], "sourcesContent": ["import { adminApiService, userApiService } from './APIService';\r\nimport { ScrapedElement } from './ScrapingService';\r\nexport interface Agent {\r\n  Name: string | undefined;\r\n  Description: string | undefined;\r\n  AccountId:string;\r\n  url: string;\r\n  TrainingFields: ScrapedElement[];\r\n  AdditionalContext?: string;\r\n}\r\n\r\n\r\nexport const NewAgentTraining = async (agent: Agent) => {\r\n    try {\r\n        const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error uploading agent\", error);\r\n        return { message: \"Upload failed\" };\r\n    }\r\n};\r\n\r\n\r\nexport const CreateInteraction = async (userCommand: string, accountId: string,targetUrl:string) => {\r\n    \r\n    try {\r\n        const requestBody = {\r\n\t\t\tuserCommand,\r\n\t\t\taccountId,\r\n\t\t\ttargetUrl,\r\n\t\t};\r\n        const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody)\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error in creating integration\", error);\r\n        return [];\r\n    }\r\n}\r\n\r\nexport const GenerateImageWithUserPrompt = async (userPrompt:string,accountId:string,onImageGenerated:any,openSnackbar?:any,signal?: AbortSignal) => {\r\n\ttry {\r\n        const requestBody = {\r\n\t\t\tuserPrompt,\r\n\t\t\taccountId\r\n\t\t};\r\n        const response = await adminApiService.post(`Ai/GenerateImageOnUserPrompt`, requestBody, {\r\n            signal, // 🆕 attach signal to cancel the request\r\n        });\r\n\r\n        if (response.data && response.data.Success) {\r\n            onImageGenerated(response.data.SuccessMessage);\r\n            return response.data;\r\n        } else {\r\n            \r\n            openSnackbar(response.data.ErrorMessage,\"error\");\r\n            \r\n            return null;\r\n        }\r\n\r\n        \r\n    } catch (error: any) {\r\n    if (error.name === \"CanceledError\" || error.code === \"ERR_CANCELED\") {\r\n      console.warn(\"Image generation canceled by user.\");\r\n    } else {\r\n      console.error(\"Error generating image:\", error);\r\n      openSnackbar?.(\"An error occurred while generating the image. Please try again.\", \"error\");\r\n    }\r\n    return null;\r\n  }\r\n}\r\n\r\n\r\nexport const EnhanceUserPrompt = async (userPrompt:string, accountId:string, openSnackbar?:any) => {\r\n    try {\r\n        const requestBody = {\r\n            userPrompt,\r\n            accountId\r\n        };\r\n        const response = await adminApiService.post(`Ai/EnhanceUserPrompt`, requestBody)\r\n        \r\n\r\n        if (response.data && response.data.Success) {\r\n            return response.data.SuccessMessage;\r\n        } else {\r\n            if (openSnackbar) {\r\n                openSnackbar(response.data.ErrorMessage || \"Failed to enhance prompt. Please try again.\", \"error\");\r\n            }\r\n            return null;\r\n        }\r\n    } catch (error) {\r\n        console.error(\"Error in enhancing prompt\", error);\r\n        if (openSnackbar) {\r\n            openSnackbar(\"An error occurred while enhancing the prompt. Please try again.\", \"error\");\r\n        }\r\n        return null;\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,cAAc;AAY9D,OAAO,MAAMC,gBAAgB,GAAG,MAAOC,KAAY,IAAK;EACpD,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMH,cAAc,CAACI,IAAI,CAAC,4BAA4B,EAAEF,KAAK,CAAC;IAC/E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO;MAAEE,OAAO,EAAE;IAAgB,CAAC;EACvC;AACJ,CAAC;AAACC,EAAA,GARWR,gBAAgB;AAW7B,OAAO,MAAMS,iBAAiB,GAAG,MAAAA,CAAOC,WAAmB,EAAEC,SAAiB,EAACC,SAAgB,KAAK;EAEhG,IAAI;IACA,MAAMC,WAAW,GAAG;MACzBH,WAAW;MACXC,SAAS;MACTC;IACD,CAAC;IACK,MAAMV,QAAQ,GAAG,MAAMJ,eAAe,CAACK,IAAI,CAAC,sBAAsB,EAAEU,WAAW,CAAC;IAChF,OAAOX,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO,EAAE;EACb;AACJ,CAAC;AAAAS,GAAA,GAdYL,iBAAiB;AAgB9B,OAAO,MAAMM,2BAA2B,GAAG,MAAAA,CAAOC,UAAiB,EAACL,SAAgB,EAACM,gBAAoB,EAACC,YAAiB,EAACC,MAAoB,KAAK;EACpJ,IAAI;IACG,MAAMN,WAAW,GAAG;MACzBG,UAAU;MACVL;IACD,CAAC;IACK,MAAMT,QAAQ,GAAG,MAAMJ,eAAe,CAACK,IAAI,CAAC,8BAA8B,EAAEU,WAAW,EAAE;MACrFM,MAAM,CAAE;IACZ,CAAC,CAAC;IAEF,IAAIjB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgB,OAAO,EAAE;MACxCH,gBAAgB,CAACf,QAAQ,CAACE,IAAI,CAACiB,cAAc,CAAC;MAC9C,OAAOnB,QAAQ,CAACE,IAAI;IACxB,CAAC,MAAM;MAEHc,YAAY,CAAChB,QAAQ,CAACE,IAAI,CAACkB,YAAY,EAAC,OAAO,CAAC;MAEhD,OAAO,IAAI;IACf;EAGJ,CAAC,CAAC,OAAOjB,KAAU,EAAE;IACrB,IAAIA,KAAK,CAACkB,IAAI,KAAK,eAAe,IAAIlB,KAAK,CAACmB,IAAI,KAAK,cAAc,EAAE;MACnElB,OAAO,CAACmB,IAAI,CAAC,oCAAoC,CAAC;IACpD,CAAC,MAAM;MACLnB,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/Ca,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG,iEAAiE,EAAE,OAAO,CAAC;IAC5F;IACA,OAAO,IAAI;EACb;AACF,CAAC;AAAAQ,GAAA,GA9BYX,2BAA2B;AAiCxC,OAAO,MAAMY,iBAAiB,GAAG,MAAAA,CAAOX,UAAiB,EAAEL,SAAgB,EAAEO,YAAiB,KAAK;EAC/F,IAAI;IACA,MAAML,WAAW,GAAG;MAChBG,UAAU;MACVL;IACJ,CAAC;IACD,MAAMT,QAAQ,GAAG,MAAMJ,eAAe,CAACK,IAAI,CAAC,sBAAsB,EAAEU,WAAW,CAAC;IAGhF,IAAIX,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgB,OAAO,EAAE;MACxC,OAAOlB,QAAQ,CAACE,IAAI,CAACiB,cAAc;IACvC,CAAC,MAAM;MACH,IAAIH,YAAY,EAAE;QACdA,YAAY,CAAChB,QAAQ,CAACE,IAAI,CAACkB,YAAY,IAAI,6CAA6C,EAAE,OAAO,CAAC;MACtG;MACA,OAAO,IAAI;IACf;EACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,IAAIa,YAAY,EAAE;MACdA,YAAY,CAAC,iEAAiE,EAAE,OAAO,CAAC;IAC5F;IACA,OAAO,IAAI;EACf;AACJ,CAAC;AAAAU,GAAA,GAxBYD,iBAAiB;AAAA,IAAAnB,EAAA,EAAAM,GAAA,EAAAY,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}