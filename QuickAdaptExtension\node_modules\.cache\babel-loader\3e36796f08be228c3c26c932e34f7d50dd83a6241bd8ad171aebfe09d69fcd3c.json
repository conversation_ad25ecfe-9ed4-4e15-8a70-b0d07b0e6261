{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\GuidePopUp.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState, useEffect, useRef } from \"react\";\nimport { Dialog, DialogContent, useMediaQuery, useTheme, Box, IconButton, Popover, Typography, Button, FormControl, Select, MenuItem, TextField, ToggleButton, ToggleButtonGroup, Tooltip, LinearProgress, MobileStepper, Breadcrumbs } from \"@mui/material\";\nimport ImageSection from \"./PopupSections/Imagesection\";\nimport RTEsection from \"./PopupSections/RTEsection\";\nimport ButtonSection from \"./PopupSections/Button\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport DragIndicatorIcon from \"@mui/icons-material/DragIndicator\";\nimport { Image, TextFormat, Code, VideoLibrary, Link } from \"@mui/icons-material\";\nimport HtmlSection from \"./PopupSections/HtmlSection\";\nimport VideoSection from \"./PopupSections/VideoSection\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport \"../guideDesign/Canvas.module.css\";\nimport PerfectScrollbar from \"react-perfect-scrollbar\";\nimport \"react-perfect-scrollbar/dist/css/styles.css\";\nimport AlertPopup from \"../drawer/AlertPopup\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const TOOLTIP_MN_WIDTH = 500;\n\n// Helper function to convert hex color to rgba with opacity\nconst hexToRgba = (hex, opacity) => {\n  // Remove # if present\n  hex = hex.replace('#', '');\n\n  // Parse hex values\n  const r = parseInt(hex.substring(0, 2), 16);\n  const g = parseInt(hex.substring(2, 4), 16);\n  const b = parseInt(hex.substring(4, 6), 16);\n  return `rgba(${r}, ${g}, ${b}, ${opacity})`;\n};\n// Only text sections have IDs\nconst GuidePopup = ({\n  selectedStepType,\n  guideStep,\n  setImageSrc,\n  imageSrc,\n  textBoxRef,\n  htmlContent,\n  setHtmlContent,\n  buttonColor,\n  setButtonColor,\n  setImageName,\n  imageName,\n  openStepDropdown,\n  openWarning,\n  setopenWarning,\n  isUnSavedChanges,\n  handleLeave\n}) => {\n  _s();\n  var _announcementJson$Gui, _style$Radius, _style$Radius2, _style$Radius3, _style$Radius4, _style$Radius5;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    addNewButton,\n    updateButton,\n    guideListByOrg,\n    getGuildeListByOrg,\n    updateButtonInteraction,\n    addNewImageContainer,\n    dismissData,\n    setSelectActions,\n    currentButtonName,\n    setCurrentButtonName,\n    targetURL,\n    setTargetURL,\n    selectedInteraction,\n    setSelectedInteraction,\n    openInteractionList,\n    setOpenInteractionList,\n    selectedTab,\n    setSelectedTab,\n    loading,\n    setLoading,\n    addNewRTEContainer,\n    dismiss,\n    currentStepIndex,\n    selectedOption,\n    progress,\n    steps,\n    setProgress,\n    selectedTemplate,\n    updateTooltipButtonAction,\n    updateTooltipButtonInteraction,\n    selectedTemplateTour,\n    setIsUnSavedChanges,\n    ProgressColor,\n    setProgressColor,\n    createWithAI\n  } = useDrawerStore(state => state);\n  const [open, setOpen] = useState(true);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [sections, setSections] = useState([{\n    type: \"image\"\n  }, {\n    type: \"text\"\n  }, {\n    type: \"button\"\n  }]);\n  const [draggingIndex, setDraggingIndex] = useState(null);\n  const [sectionCounts, setSectionCounts] = useState({\n    image: 1,\n    // Start with one of each as per initial sections state\n    text: 1,\n    button: 1,\n    video: 0,\n    gif: 0,\n    html: 0\n  });\n\n  // Maximum allowed sections of each type\n  const MAX_SECTIONS = {\n    image: 3,\n    text: 3,\n    // RTE sections\n    button: 3,\n    video: 3,\n    gif: 3,\n    html: 3\n  };\n\n  // Helper function to check if a section type has reached its limit\n  const hasReachedLimit = type => {\n    // Map \"text\" to \"text\" for the check\n    const checkType = type === \"text\" ? \"text\" : type;\n    return sectionCounts[checkType] >= MAX_SECTIONS[checkType];\n  };\n\n  //const [imageSrc, setImageSrc] = useState<string>(\"\");\n  //const [imageName, setImageName] = useState<string>(\"\");\n  //const [selectedActions, setSelectActions] = useState<string>(\"close\");\n  //const [selectedTab, setSelectedTab] = useState<string>(\"new-tab\");\n  //const [targetURL, setTargetURL] = useState<string>(\"\");\n  //const [selectedInteraction, setSelectedInteraction] = useState(null);\n  //const [currentButtonName, setCurrentButtonName] = useState(\"\");\n  //const [openInteractionList, setOpenInteractionList] = useState(false);\n  //const [loading, setLoading] = useState(false);\n  const [action, setAction] = useState(\"close\");\n  const userInfo = localStorage.getItem(\"userInfo\");\n  const userInfoObj = JSON.parse(userInfo || \"{}\");\n  const orgDetails = JSON.parse(userInfoObj.orgDetails || \"{}\");\n  const organizationId = orgDetails.OrganizationId;\n  const overlayEnabled = useDrawerStore(state => state.overlayEnabled);\n  const guidePopUpRef = useRef(null);\n\n  //Added Zustand here\n  const {\n    designPopup,\n    announcementJson,\n    currentStep,\n    setSettingAnchorEl,\n    settingAnchorEl,\n    updateButtonAction,\n    getCurrentButtonInfo,\n    buttonsContainer,\n    buttonId,\n    setButtonId,\n    cuntainerId,\n    setCuntainerId,\n    btnname,\n    setBtnName,\n    rtesContainer,\n    imagesContainer\n  } = useDrawerStore(state => state);\n  const theme = useTheme();\n  const isFullScreen = useMediaQuery(theme.breakpoints.down(\"sm\"));\n\n  // Synchronize local sectionCounts with actual store state\n  useEffect(() => {\n    setSectionCounts({\n      image: imagesContainer.length,\n      text: rtesContainer.length,\n      button: buttonsContainer.length,\n      video: 0,\n      gif: 0,\n      html: 0\n    });\n  }, [buttonsContainer.length, rtesContainer.length, imagesContainer.length]);\n  const handleCloseInteraction = () => {\n    setOpenInteractionList(false);\n  };\n  const handleOpenInteraction = () => {\n    setOpenInteractionList(true);\n    if (organizationId && !guideListByOrg.length) {\n      (async () => {\n        setLoading(true);\n        await getGuildeListByOrg(organizationId);\n        setLoading(false);\n      })();\n    }\n  };\n  const handleClose = (_event, reason) => {\n    if (reason === \"backdropClick\" || reason === \"escapeKeyDown\") {\n      return;\n    }\n    setOpen(false);\n  };\n  const handleAddIconClick = event => {\n    if (hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")) {\n      return;\n    }\n    setAnchorEl(event.currentTarget);\n  };\n  const handlePopoverClose = () => {\n    setAnchorEl(null);\n  };\n  const handleAddSection = type => {\n    // Check if we've reached the limit for this section type\n    if (hasReachedLimit(type)) {\n      // Don't add more sections if limit is reached\n      setAnchorEl(null);\n      return;\n    }\n    if (type === \"button\") {\n      // Create and add a new button with default values\n      addNewButton({\n        id: crypto.randomUUID(),\n        name: \"Button 1\",\n        position: \"center\",\n        type: \"primary\",\n        isEditing: false,\n        index: 0,\n        style: {\n          ...defaultButtonColors\n        },\n        actions: {\n          value: \"close\",\n          // Default action is \"close\"\n          targetURL: targetURL,\n          // Default empty target URL\n          tab: \"same-tab\" // Default tab behavior\n        }\n      }, \"\");\n\n      // Set the temporary colors (this might be for styling the new button)\n      setTempColors(defaultButtonColors);\n\n      // Optionally, set the selected actions (if needed outside of the button creation)\n      setSelectedActions({\n        value: \"close\",\n        // Default action is \"close\"\n        targetURL: targetURL,\n        // Default empty target URL\n        tab: \"same-tab\" // Default tab behavior\n      });\n    } else if (type === \"image\") {\n      addNewImageContainer();\n    } else if (type === \"text\") {\n      addNewRTEContainer();\n    } else {\n      // For other section types\n      setSections(prevSections => [...prevSections, {\n        type\n      }]);\n    }\n    setAnchorEl(null);\n  };\n  const handleDragStart = index => {\n    setDraggingIndex(index);\n  };\n  const handleDragEnter = index => {\n    if (draggingIndex !== null && draggingIndex !== index) {\n      const reorderedSections = [...sections];\n      const [removed] = reorderedSections.splice(draggingIndex, 1);\n      reorderedSections.splice(index, 0, removed);\n      setSections(reorderedSections);\n      setDraggingIndex(index);\n    }\n  };\n  const handleDragEnd = () => {\n    setDraggingIndex(null);\n  };\n  const handleDeleteRTESection = index => {\n    // RTE section deletion is handled by the store\n    // Section counts will be automatically updated via useEffect\n  };\n\n  // Function to handle deletion of image sections\n  const handleDeleteImageSection = () => {\n    // Image section deletion is handled by the store\n    // Section counts will be automatically updated via useEffect\n  };\n\n  // Function to handle deletion of button sections\n  const handleDeleteButtonSection = () => {\n    // Button section deletion is handled by the store\n    // Section counts will be automatically updated via useEffect\n  };\n\n  // Function to handle cloning of RTE sections\n  const handleCloneRTESection = () => {\n    // Check if we've reached the limit for RTE sections\n    if (hasReachedLimit(\"text\")) {\n      return; // Don't clone if limit is reached\n    }\n    // RTE section cloning is handled by the store\n    // Section counts will be automatically updated via useEffect\n  };\n\n  // Function to handle cloning of image sections\n  const handleCloneImageSection = () => {\n    // Check if we've reached the limit for image sections\n    if (hasReachedLimit(\"image\")) {\n      return; // Don't clone if limit is reached\n    }\n    // Image section cloning is handled by the store\n    // Section counts will be automatically updated via useEffect\n  };\n\n  // Function to handle cloning of button sections\n  const handleCloneButtonSection = () => {\n    // Check if we've reached the limit for button sections\n    if (hasReachedLimit(\"button\")) {\n      return; // Don't clone if limit is reached\n    }\n    // Button section cloning is handled by the store\n    // Section counts will be automatically updated via useEffect\n  };\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  const renderSection = (section, index) => {\n    switch (section.type) {\n      case \"image\":\n        return /*#__PURE__*/_jsxDEV(ImageSection, {\n          setImageSrc: setImageSrc,\n          imageSrc: imageSrc,\n          setImageName: setImageName,\n          imageName: imageName,\n          onDelete: handleDeleteImageSection,\n          onClone: handleCloneImageSection,\n          isCloneDisabled: hasReachedLimit(\"image\")\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 6\n        }, this);\n      case \"text\":\n        return /*#__PURE__*/_jsxDEV(RTEsection, {\n          // Use unique ID as the key for RTESection\n          textBoxRef: textBoxRef,\n          isBanner: false,\n          handleDeleteRTESection: () => handleDeleteRTESection(index),\n          index: index\n          // @ts-ignore\n          ,\n          ref: textBoxRef,\n          guidePopUpRef: guidePopUpRef,\n          onClone: handleCloneRTESection,\n          isCloneDisabled: hasReachedLimit(\"text\")\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 6\n        }, this);\n      case \"button\":\n        return /*#__PURE__*/_jsxDEV(ButtonSection, {\n          buttonColor: buttonColor,\n          setButtonColor: setButtonColor,\n          isBanner: false,\n          onDelete: handleDeleteButtonSection,\n          onClone: handleCloneButtonSection,\n          isCloneDisabled: hasReachedLimit(\"button\")\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 6\n        }, this);\n      case \"video\":\n        return /*#__PURE__*/_jsxDEV(VideoSection, {}, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 12\n        }, this);\n      case \"html\":\n        return /*#__PURE__*/_jsxDEV(HtmlSection, {\n          htmlContent: htmlContent,\n          setHtmlContent: setHtmlContent,\n          isBanner: false\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 6\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const style = (_announcementJson$Gui = announcementJson.GuideStep.find(step => step.stepName === currentStep)) === null || _announcementJson$Gui === void 0 ? void 0 : _announcementJson$Gui.Canvas;\n  const popupStyle = {\n    //maxWidth: \"533px\",\n    //\tminWidth: TOOLTIP_MN_WIDTH,\n    maxWidth: `${style === null || style === void 0 ? void 0 : style.Width} !important` || \"500px !important\",\n    // maxHeight: \"400px\",\n    width: `${(style === null || style === void 0 ? void 0 : style.Width) || 500}px`,\n    borderRadius: `${(_style$Radius = style === null || style === void 0 ? void 0 : style.Radius) !== null && _style$Radius !== void 0 ? _style$Radius : 8}px`,\n    borderWidth: `${(style === null || style === void 0 ? void 0 : style.BorderWidth) || 0}px`,\n    display: \"flex\",\n    flexDirection: \"column\",\n    borderColor: `${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"transparent\"}`,\n    backgroundColor: `${(style === null || style === void 0 ? void 0 : style.BackgroundColor) || \"#fff\"}`,\n    border: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || \"0\"}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"none\"}`,\n    overflow: \"visible\"\n  };\n  const sectionStyle = {\n    width: \"100%\",\n    display: \"flex\",\n    flexDirection: \"column\",\n    position: \"relative\",\n    \"&:hover .add-icon\": {\n      display: \"flex\"\n    },\n    \"&:hover .side-add-icon\": {\n      display: \"flex\"\n    },\n    \"&:hover .add-section\": {\n      opacity: \"1\"\n    }\n  };\n  const dragButtonStyle = {\n    position: \"absolute\",\n    left: \"-60px\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    cursor: \"move\",\n    zIndex: 1000\n  };\n  const sideAddButtonStyle = {\n    position: \"absolute\",\n    right: \"-38px\",\n    top: \"50%\",\n    transform: \"translateY(-50%)\",\n    width: \"18px\",\n    height: \"100%\",\n    borderRadius: \"6px\",\n    display: \"none\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    backgroundColor: \"#5F9EA0\",\n    cursor: \"pointer\",\n    zIndex: 1000,\n    \"&:hover\": {\n      backgroundColor: \"#70afaf\"\n    }\n  };\n  const handleChangeTabs = event => {\n    setSelectedTab(event.target.value);\n  };\n  const handleCloseSettingPopup = (containerId, buttonId) => {\n    updateButtonAction(containerId, buttonId, selectedActions);\n    updateButtonInteraction(containerId, buttonId, selectedInteraction);\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n  };\n  const [targetURLError, setTargetURLError] = useState(\"\");\n  const validateTargetURL = url => {\n    if (selectedActions.value === \"open-url\") {\n      if (!url) {\n        return \"URL is required\";\n      }\n      try {\n        new URL(url);\n        return \"\";\n      } catch (error) {\n        return \"Invalid URL\";\n      }\n    }\n    return \"\";\n  };\n  const handleApplyChanges = (containerId, buttonId) => {\n    const error = validateTargetURL(targetURL);\n    setTargetURLError(error); // Set the error message for display\n\n    if (error) {\n      return; // Prevent applying changes if there's a validation error\n    }\n    const buttonNameToUpdate = !currentButtonName || !currentButtonName.trim() ? curronButtonInfo.title // Retain the previously saved button name\n    : currentButtonName;\n    setCurrentButtonName(buttonNameToUpdate);\n    updateButton(containerId, buttonId, \"style\", tempColors);\n    updateButtonAction(containerId, buttonId, selectedActions); // Update the selected actions\n    updateButtonInteraction(containerId, buttonId, selectedInteraction);\n    updateButton(containerId, buttonId, \"name\", buttonNameToUpdate);\n    updateButton(containerId, buttonId, \"actions\", selectedActions); // Update button actions\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setIsUnSavedChanges(true);\n  };\n  const handleURLChange = e => {\n    const newURL = e.target.value;\n    setTargetURL(newURL);\n\n    // Validate the URL and update the error state\n    const error = validateTargetURL(newURL);\n    setTargetURLError(error);\n    setSelectedActions({\n      value: selectedActions.value,\n      targetURL: newURL,\n      tab: selectedTab\n    });\n  };\n  const curronButtonInfo = useMemo(() => {\n    const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n    setCurrentButtonName(result.title);\n    setBtnName(result.title);\n    setAction(result.value);\n    return result;\n  }, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\n  const defaultButtonColors = {\n    backgroundColor: \"#5F9EA0\",\n    borderColor: \"#70afaf\",\n    color: \"#ffffff\"\n  };\n  const [selectedActions, setSelectedActions] = useState({\n    value: \"close\",\n    // Default action\n    targetURL: \"\",\n    // Default empty target URL\n    tab: \"same-tab\" // Default tab (same-tab)\n  });\n  const [tempColors, setTempColors] = useState(defaultButtonColors);\n  const selectedButton = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n  useEffect(() => {\n    const handleSelectButton = (containerId, buttonId) => {\n      const selectedButton = getCurrentButtonInfo(containerId, buttonId);\n      if (selectedButton) {\n        setTargetURL(selectedButton.targetURL || \"\");\n        setTempColors({\n          backgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,\n          borderColor: selectedButton.borderColor || defaultButtonColors.borderColor,\n          color: selectedButton.textColor || defaultButtonColors.color\n        });\n        setSelectedActions({\n          value: selectedButton.selectedActions || \"close\",\n          // Default to \"close\" if no action is set\n          targetURL: selectedButton.targetURL || targetURL,\n          // Can be updated later if needed\n          tab: \"same-tab\" // Default tab behavior\n        });\n      }\n    };\n    handleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n  }, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\n\n  // Function to handle color changes in the color picker\n  const handleColorChange = (e, targetName) => {\n    const value = e.target.value;\n    setTempColors(prev => ({\n      ...prev,\n      [targetName]: value\n    }));\n  };\n  useEffect(() => {\n    setSelectedActions({\n      value: selectedActions.value,\n      // Default action\n      targetURL: targetURL,\n      // Default empty target URL\n      tab: \"same-tab\" // Default tab (same-tab)\n    });\n  }, []);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (guidePopUpRef.current) {\n        // Force a reflow to get accurate measurements\n        guidePopUpRef.current.style.height = 'auto';\n        const contentHeight = guidePopUpRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (guidePopUpRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(guidePopUpRef.current);\n    }\n    if (guidePopUpRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(guidePopUpRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  const handleChangeActions = e => {\n    const v = e.target.value; // Casting to TInteractionValue\n    setSelectedActions({\n      value: v,\n      // Ensure that selectedActions.value is of type TInteractionValue\n      targetURL: targetURL,\n      tab: selectedTab // Ensure tab is a valid value\n    });\n  };\n  // useEffect(() => {\n  // \tif (selectedButton) {\n  // \t  selectedButton.targetURL = targetURL;  // Update selectedButton's targetURL whenever targetURL state changes\n  // \t}\n  //   }, [targetURL]);  // Dependency on `targetURL`\n  // Dependency on `targetURL`\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-annpopop\",\n    children: [overlayEnabled && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        width: \"100vw\",\n        height: \"100vh\",\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        zIndex: 9\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 5\n    }, this), isUnSavedChanges && openWarning && /*#__PURE__*/_jsxDEV(AlertPopup, {\n      openWarning: openWarning,\n      setopenWarning: setopenWarning,\n      handleLeave: handleLeave\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 705,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      className: \"qadpt-guide-popup\",\n      open: open,\n      onClose: handleClose,\n      fullScreen: isFullScreen,\n      PaperProps: {\n        style: popupStyle\n      },\n      maxWidth: false,\n      disableEnforceFocus: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          placeContent: \"end\",\n          display: \"flex\"\n        },\n        children: dismiss && /*#__PURE__*/_jsxDEV(IconButton, {\n          className: \"qadpt-dismiss\"\n          //onClick={handleCloseBanner}\n          ,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            sx: {\n              zoom: 1,\n              color: \"#000\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 1\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n        ref: scrollbarRef,\n        style: {\n          maxHeight: \"500px\",\n          ...(progress ? {\n            borderTopLeftRadius: `${(_style$Radius2 = style === null || style === void 0 ? void 0 : style.Radius) !== null && _style$Radius2 !== void 0 ? _style$Radius2 : 8}px`,\n            borderTopRightRadius: `${(_style$Radius3 = style === null || style === void 0 ? void 0 : style.Radius) !== null && _style$Radius3 !== void 0 ? _style$Radius3 : 8}px`,\n            borderBottomLeftRadius: \"0px\",\n            borderBottomRightRadius: \"0px\"\n          } : {\n            borderRadius: `${(_style$Radius4 = style === null || style === void 0 ? void 0 : style.Radius) !== null && _style$Radius4 !== void 0 ? _style$Radius4 : 8}px`\n          })\n        },\n        options: {\n          suppressScrollY: false,\n          suppressScrollX: true,\n          wheelPropagation: false,\n          swipeEasing: true,\n          minScrollbarLength: 20,\n          scrollingThreshold: 1000,\n          scrollYMarginOffset: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            padding: 0,\n            paddingTop: \"10px\",\n            // Add 10px padding to top of GuidePopUp\n            overflow: \"hidden\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\"\n          },\n          ref: guidePopUpRef,\n          id: \"guide-popup\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              padding: `${(style === null || style === void 0 ? void 0 : style.Padding) || 12}px`\n            },\n            children: [sections.map((section, index) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                ...sectionStyle,\n                height: \"auto\",\n                \"&:hover\": {\n                  borderTopWidth: index !== 0 ? \"1px\" : \"0px\",\n                  borderTopColor: index !== 0 ? \"var(--primarycolor)\" : \"transparent\",\n                  borderTopStyle: index !== 0 ? \"dotted\" : \"none\"\n                }\n              },\n              draggable: true,\n              onDragStart: () => handleDragStart(index),\n              onDragEnter: () => handleDragEnter(index),\n              onDragEnd: handleDragEnd,\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                className: \"drag-icon\",\n                sx: dragButtonStyle,\n                children: /*#__PURE__*/_jsxDEV(DragIndicatorIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    color: \"#5F9EA0\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 9\n              }, this), renderSection(section, index), index !== 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\") ? translate(\"Maximum limit reached for all section types\") : translate(\"Add Section\"),\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  className: \"add-section\",\n                  onClick: handleAddIconClick,\n                  sx: {\n                    backgroundColor: \"#5F9EA0\",\n                    \"&:hover\": {\n                      backgroundColor: \"#70afaf\"\n                    },\n                    borderRadius: \"4px\",\n                    padding: \"5px !important\",\n                    position: \"absolute\",\n                    top: \"auto\",\n                    left: \"50%\",\n                    transform: \"translate(-50%, -50%)\",\n                    opacity: \"0\",\n                    cursor: hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\"\n                  },\n                  disabled: hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\"),\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\",\n                    sx: {\n                      color: \"#fff\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 834,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 10\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 8\n            }, this)), Boolean(settingAnchorEl.value) ? /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-designpopup qadpt-btnprop\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"qadpt-design-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"qadpt-title\",\n                    children: translate(\"Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 12\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    \"aria-label\": \"close\",\n                    onClick: () => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId),\n                    children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 878,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"qadpt-canblock qadpt-btnpro\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"qadpt-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(FormControl, {\n                      fullWidth: true,\n                      sx: {\n                        marginBottom: \"16px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: \"14px\",\n                          fontWeight: \"bold\",\n                          my: \"5px\"\n                        },\n                        children: translate(\"Button Name\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                        value: currentButtonName,\n                        size: \"small\",\n                        sx: {\n                          mb: \"5px\",\n                          border: \"1px solid #ccc\",\n                          borderRadius: \"4px\",\n                          \"& .MuiOutlinedInput-root\": {\n                            height: \"35px\",\n                            \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                              border: \"none !important\"\n                            },\n                            \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                              border: \"none !important\"\n                            }\n                          },\n                          \"& .MuiOutlinedInput-notchedOutline\": {\n                            border: \"none !important\"\n                          }\n                        },\n                        placeholder: translate(\"Button Name\"),\n                        onChange: e => setCurrentButtonName(e.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 888,\n                        columnNumber: 12\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: \"14px\",\n                          fontWeight: \"bold\",\n                          mb: \"5px\"\n                        },\n                        children: translate(\"Button Action\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 913,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(Select, {\n                        value: selectedActions.value,\n                        defaultValue: \"close\",\n                        onChange: handleChangeActions,\n                        sx: {\n                          mb: \"5px\",\n                          border: \"1px solid #ccc\",\n                          borderRadius: \"4px\",\n                          textAlign: \"left\",\n                          \"& .MuiSelect-select\": {\n                            padding: \"8px\"\n                          },\n                          \"& .MuiOutlinedInput-root\": {\n                            height: \"35px\",\n                            \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                              border: \"none !important\"\n                            },\n                            \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                              border: \"none !important\"\n                            }\n                          },\n                          \"& .MuiOutlinedInput-notchedOutline\": {\n                            border: \"none !important\"\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"close\",\n                          children: translate(\"Close\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 942,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"open-url\",\n                          children: translate(\"Open URL\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 943,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"Previous\",\n                          children: translate(\"Previous\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 944,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"Next\",\n                          children: translate(\"Next\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 945,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                          value: \"Restart\",\n                          children: translate(\"Restart\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 946,\n                          columnNumber: 15\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 12\n                      }, this), selectedActions.value === \"open-url\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          sx: {\n                            fontSize: \"14px\",\n                            fontWeight: \"bold\",\n                            my: \"5px\"\n                          },\n                          children: translate(\"Enter URL\")\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 950,\n                          columnNumber: 16\n                        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                          value: targetURL,\n                          size: \"small\",\n                          placeholder: \"https://quixy.com\",\n                          onChange: e => {\n                            const newURL = e.target.value;\n                            setTargetURL(newURL); // Update the `targetURL` state with the new value\n                            handleURLChange(e); // Update the selectedButton.targetURL with the new value\n                          },\n                          error: !!targetURLError,\n                          helperText: targetURLError\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 951,\n                          columnNumber: 14\n                        }, this), /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n                          value: selectedTab,\n                          onChange: handleChangeTabs,\n                          exclusive: true,\n                          \"aria-label\": translate(\"open in tab\"),\n                          sx: {\n                            gap: \"5px\",\n                            marginY: \"5px\",\n                            height: \"35px\"\n                          },\n                          children: [\"new-tab\", \"same-tab\"].map(tab => {\n                            return /*#__PURE__*/_jsxDEV(ToggleButton, {\n                              value: tab,\n                              \"aria-label\": \"new tab\",\n                              sx: {\n                                border: \"1px solid #7EA8A5\",\n                                textTransform: \"capitalize\",\n                                color: \"#000\",\n                                borderRadius: \"4px\",\n                                flex: 1,\n                                padding: \"0 !important\",\n                                \"&.Mui-selected\": {\n                                  backgroundColor: \"var(--border-color)\",\n                                  color: \"#000\",\n                                  border: \"2px solid #7EA8A5\"\n                                },\n                                \"&:hover\": {\n                                  backgroundColor: \"#f5f5f5\"\n                                },\n                                \"&:last-child\": {\n                                  borderLeft: \"1px solid var(--primarycolor) !important\" // Remove left border for the last button\n                                }\n                              },\n                              children: tab\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 977,\n                              columnNumber: 17\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 964,\n                          columnNumber: 14\n                        }, this)]\n                      }, void 0, true) : null]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 883,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      className: \"qadpt-control-box\",\n                      sx: {\n                        borderRadius: \"5px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        className: \"qadpt-control-label\",\n                        children: translate(\"Background\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1055,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"color\",\n                        value: tempColors.backgroundColor,\n                        onChange: e => handleColorChange(e, \"backgroundColor\"),\n                        className: \"qadpt-color-input\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1056,\n                        columnNumber: 12\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1051,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      className: \"qadpt-control-box\",\n                      sx: {\n                        borderRadius: \"5px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        className: \"qadpt-control-label\",\n                        children: translate(\"Border\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1068,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"color\",\n                        value: tempColors.borderColor,\n                        onChange: e => handleColorChange(e, \"borderColor\"),\n                        className: \"qadpt-color-input\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1069,\n                        columnNumber: 12\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1064,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      className: \"qadpt-control-box\",\n                      sx: {\n                        borderRadius: \"5px\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        className: \"qadpt-control-label\",\n                        children: translate(\"Text\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"color\",\n                        value: tempColors.color,\n                        onChange: e => handleColorChange(e, \"color\"),\n                        className: \"qadpt-color-input\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1082,\n                        columnNumber: 12\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1077,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"qadpt-drawerFooter\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    onClick: () => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId),\n                    className: \"qadpt-btn\",\n                    children: translate(\"Apply\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 11\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1092,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 8\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 6\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 5\n        }, this)\n      }, `scrollbar-${needsScrolling}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderRadius: `${(_style$Radius5 = style === null || style === void 0 ? void 0 : style.Radius) !== null && _style$Radius5 !== void 0 ? _style$Radius5 : 8}px`\n        },\n        children: progress && (selectedOption === 1 || selectedOption === \"\" ? /*#__PURE__*/_jsxDEV(DotsStepper, {\n          activeStep: currentStep,\n          steps: steps.length,\n          ProgressColor: ProgressColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1113,\n          columnNumber: 7\n        }, this) : selectedOption === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              backgroundColor: hexToRgba(ProgressColor, 0.45),\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            },\n            value: currentStep / steps.length * 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1120,\n          columnNumber: 7\n        }, this) : selectedOption === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: \"8px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(BreadCrumpStepper, {\n            activeStep: currentStep,\n            steps: steps.length,\n            ProgressColor: ProgressColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1136,\n          columnNumber: 7\n        }, this) : selectedOption === 4 ? /*#__PURE__*/_jsxDEV(Breadcrumbs, {\n          \"aria-label\": \"breadcrumb\",\n          sx: {\n            padding: \"8px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Step \", currentStep, \" of \", steps.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1149,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1145,\n          columnNumber: 7\n        }, this) : null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1109,\n        columnNumber: 5\n      }, this), anchorEl && /*#__PURE__*/_jsxDEV(Popover, {\n        className: \"qadpt-secprop\",\n        open: Boolean(anchorEl),\n        anchorEl: anchorEl,\n        onClose: handlePopoverClose,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"center\"\n        },\n        transformOrigin: {\n          vertical: \"top\",\n          horizontal: \"center\"\n        },\n        slotProps: {\n          paper: {\n            sx: {\n              padding: \"12px\",\n              display: \"flex\",\n              gap: \"16px\",\n              width: \"auto\",\n              zIndex: 1302\n            }\n          }\n        },\n        sx: {\n          position: \"absolute\",\n          // top: `${anchorEl.getBoundingClientRect().bottom + 8}px`,\n          // left: `${anchorEl.getBoundingClientRect().left - 150}px`,\n          transform: \"none\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          flexDirection: \"row\",\n          gap: \"16px\",\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasReachedLimit(\"text\") ? translate(\"Maximum limit of 3 Rich Text sections reached\") : \"\",\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              sx: {\n                cursor: hasReachedLimit(\"text\") ? \"not-allowed\" : \"pointer\",\n                opacity: hasReachedLimit(\"text\") ? 0.5 : 1,\n                svg: {\n                  fontSize: \"24px !important\"\n                }\n              },\n              onClick: () => !hasReachedLimit(\"text\") && handleAddSection(\"text\"),\n              children: [/*#__PURE__*/_jsxDEV(TextFormat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1215,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontSize: \"11px !important\"\n                },\n                children: translate(\"Rich Text\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1216,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1202,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1194,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasReachedLimit(\"button\") ? translate(\"Maximum limit of 3 Button sections reached\") : \"\",\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              sx: {\n                cursor: hasReachedLimit(\"button\") ? \"not-allowed\" : \"pointer\",\n                opacity: hasReachedLimit(\"button\") ? 0.5 : 1,\n                svg: {\n                  fontSize: \"24px !important\"\n                }\n              },\n              onClick: () => !hasReachedLimit(\"button\") && handleAddSection(\"button\"),\n              children: [/*#__PURE__*/_jsxDEV(Link, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontSize: \"11px !important\"\n                },\n                children: translate(\"Button\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: hasReachedLimit(\"image\") ? translate(\"Maximum limit of 3 Image sections reached\") : \"\",\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems: \"center\",\n              sx: {\n                cursor: hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\",\n                opacity: hasReachedLimit(\"image\") ? 0.5 : 1,\n                svg: {\n                  fontSize: \"24px !important\"\n                }\n              },\n              onClick: () => !hasReachedLimit(\"image\") && handleAddSection(\"image\"),\n              children: [/*#__PURE__*/_jsxDEV(Image, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1277,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontSize: \"11px !important\"\n                },\n                children: translate(\"Image\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1278,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1256,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Coming Soon\"),\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                sx: {\n                  cursor: \"pointer\",\n                  opacity: 0.5,\n                  svg: {\n                    fontSize: \"24px !important\"\n                  }\n                }\n                // onClick={() => handleAddSection(\"video\")}\n                ,\n                children: [/*#__PURE__*/_jsxDEV(VideoLibrary, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontSize: \"11px !important\"\n                  },\n                  children: translate(\"Video\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1308,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1294,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1293,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1285,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Coming Soon\"),\n            PopperProps: {\n              sx: {\n                zIndex: 9999\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                sx: {\n                  cursor: \"pointer\",\n                  opacity: 0.5,\n                  svg: {\n                    fontSize: \"24px !important\"\n                  }\n                },\n                onClick: () => handleAddSection(\"html\"),\n                children: [/*#__PURE__*/_jsxDEV(Code, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontSize: \"11px !important\"\n                  },\n                  children: translate(\"HTML\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1326,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1188,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1157,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 690,\n    columnNumber: 3\n  }, this);\n};\n_s(GuidePopup, \"ildquiXj3dXVHQpyVVEAMLKwj8U=\", false, function () {\n  return [useTranslation, useDrawerStore, useDrawerStore, useDrawerStore, useTheme, useMediaQuery];\n});\n_c = GuidePopup;\nexport default GuidePopup;\nconst DotsStepper = ({\n  steps,\n  activeStep,\n  ProgressColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(MobileStepper, {\n    variant: \"dots\",\n    steps: steps,\n    position: \"static\",\n    activeStep: activeStep - 1,\n    sx: {\n      flexGrow: 1,\n      display: \"flex\",\n      justifyContent: \"center\",\n      background: \"inherit\",\n      \"& .MuiMobileStepper-dotActive\": {\n        backgroundColor: ProgressColor // active dot color\n      }\n    },\n    nextButton: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false),\n    backButton: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1368,\n    columnNumber: 3\n  }, this);\n};\n_c2 = DotsStepper;\nconst BreadCrumpStepper = ({\n  steps,\n  activeStep,\n  ProgressColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: \"4px\" // Adjust space between steps\n        //   paddingTop: '15px',\n      },\n      children: Array.from({\n        length: steps\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '14px',\n          height: '4px',\n          backgroundColor: index === activeStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45),\n          // Active color and inactive color\n          borderRadius: '100px'\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1395,\n        columnNumber: 6\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1386,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1384,\n    columnNumber: 3\n  }, this);\n};\n_c3 = BreadCrumpStepper;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"GuidePopup\");\n$RefreshReg$(_c2, \"DotsStepper\");\n$RefreshReg$(_c3, \"BreadCrumpStepper\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useEffect", "useRef", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMediaQuery", "useTheme", "Box", "IconButton", "Popover", "Typography", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "TextField", "ToggleButton", "ToggleButtonGroup", "<PERSON><PERSON><PERSON>", "LinearProgress", "MobileStepper", "Breadcrumbs", "ImageSection", "RTEsection", "ButtonSection", "AddIcon", "DragIndicatorIcon", "Image", "TextFormat", "Code", "VideoLibrary", "Link", "HtmlSection", "VideoSection", "useDrawerStore", "CloseIcon", "PerfectScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TOOLTIP_MN_WIDTH", "hexToRgba", "hex", "opacity", "replace", "r", "parseInt", "substring", "g", "b", "GuidePopup", "selectedStepType", "guideStep", "setImageSrc", "imageSrc", "textBoxRef", "htmlContent", "setHtmlContent", "buttonColor", "setButtonColor", "setImageName", "imageName", "openStepDropdown", "openWarning", "setopenWarning", "isUnSavedChanges", "handleLeave", "_s", "_announcementJson$Gui", "_style$Radius", "_style$Radius2", "_style$Radius3", "_style$Radius4", "_style$Radius5", "t", "translate", "addNewButton", "updateButton", "guideListByOrg", "getGuildeListByOrg", "updateButtonInteraction", "addNewImageContainer", "dismissData", "setSelectActions", "currentButtonName", "setCurrentButtonName", "targetURL", "setTargetURL", "selectedInteraction", "setSelectedInteraction", "openInteractionList", "setOpenInteractionList", "selectedTab", "setSelectedTab", "loading", "setLoading", "addNewRTEContainer", "dismiss", "currentStepIndex", "selectedOption", "progress", "steps", "setProgress", "selectedTemplate", "updateTooltipButtonAction", "updateTooltipButtonInteraction", "selectedTemplateTour", "setIsUnSavedChanges", "ProgressColor", "setProgressColor", "createWithAI", "state", "open", "<PERSON><PERSON><PERSON>", "anchorEl", "setAnchorEl", "sections", "setSections", "type", "draggingIndex", "setDraggingIndex", "sectionCounts", "setSectionCounts", "image", "text", "button", "video", "gif", "html", "MAX_SECTIONS", "hasReachedLimit", "checkType", "action", "setAction", "userInfo", "localStorage", "getItem", "userInfoObj", "JSON", "parse", "orgDetails", "organizationId", "OrganizationId", "overlayEnabled", "guidePopUpRef", "designPopup", "announcement<PERSON><PERSON>", "currentStep", "setSettingAnchorEl", "settingAnchorEl", "updateButtonAction", "getCurrentButtonInfo", "buttonsContainer", "buttonId", "setButtonId", "cuntainerId", "setCuntainerId", "btnname", "setBtnName", "rtesContainer", "imagesContainer", "theme", "isFullScreen", "breakpoints", "down", "length", "handleCloseInteraction", "handleOpenInteraction", "handleClose", "_event", "reason", "handleAddIconClick", "event", "currentTarget", "handlePopoverClose", "handleAddSection", "id", "crypto", "randomUUID", "name", "position", "isEditing", "index", "style", "defaultButtonColors", "actions", "value", "tab", "setTempColors", "setSelectedActions", "prevSections", "handleDragStart", "handleDragEnter", "reorderedSections", "removed", "splice", "handleDragEnd", "handleDeleteRTESection", "handleDeleteImageSection", "handleDeleteButtonSection", "handleCloneRTESection", "handleCloneImageSection", "handleCloneButtonSection", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "renderSection", "section", "onDelete", "onClone", "isCloneDisabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isBanner", "ref", "GuideStep", "find", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "popupStyle", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "width", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderWidth", "display", "flexDirection", "borderColor", "BorderColor", "backgroundColor", "BackgroundColor", "border", "BorderSize", "overflow", "sectionStyle", "dragButtonStyle", "left", "top", "transform", "cursor", "zIndex", "sideAddButtonStyle", "right", "height", "alignItems", "justifyContent", "handleChangeTabs", "target", "handleCloseSettingPopup", "containerId", "selectedActions", "targetURLError", "setTargetURLError", "validateTargetURL", "url", "URL", "error", "handleApplyChanges", "buttonNameToUpdate", "trim", "curronButtonInfo", "title", "tempColors", "handleURLChange", "e", "newURL", "result", "color", "<PERSON><PERSON><PERSON><PERSON>", "handleSelectButton", "bgColor", "textColor", "handleColorChange", "targetName", "prev", "checkScrollNeeded", "current", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "window", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "handleChangeActions", "v", "className", "children", "sx", "onClose", "fullScreen", "PaperProps", "disableEnforceFocus", "place<PERSON><PERSON>nt", "zoom", "maxHeight", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "padding", "paddingTop", "Padding", "map", "borderTopWidth", "borderTopColor", "borderTopStyle", "draggable", "onDragStart", "onDragEnter", "onDragEnd", "fontSize", "onClick", "disabled", "Boolean", "size", "fullWidth", "marginBottom", "fontWeight", "my", "mb", "placeholder", "onChange", "defaultValue", "textAlign", "helperText", "exclusive", "gap", "marginY", "textTransform", "flex", "borderLeft", "variant", "DotsStepper", "activeStep", "margin", "BreadCrumpStepper", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "paper", "PopperProps", "svg", "_c", "flexGrow", "background", "nextButton", "backButton", "_c2", "Array", "from", "_", "_c3", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/GuidePopUp.tsx"], "sourcesContent": ["import React, { useMemo, useState, useEffect, useRef } from \"react\";\r\nimport { TB<PERSON>onAction, TInteractionValue, TSectionType } from \"../../store/drawerStore\";\r\nimport {\r\n\tDialog,\r\n\tDialogContent,\r\n\tuseMediaQuery,\r\n\tuseTheme,\r\n\tBox,\r\n\tIconButton,\r\n\tPopover,\r\n\tTypography,\r\n\tButton,\r\n\tFormControl,\r\n\tSelect,\r\n\tMenuItem,\r\n\tTextField,\r\n\tSelectChangeEvent,\r\n\tRadioGroup,\r\n\tRadio,\r\n\tFormControlLabel,\r\n\tInput,\r\n\tToggleButton,\r\n\tToggleButtonGroup,\r\n\tAutocomplete,\r\n\tCircularProgress,\r\n\tDialogTitle,\r\n\tTooltip,\r\n\tLinearProgress,\r\n\tMobileStepper,\r\n\tBreadcrumbs,\r\n\tDialogActions\r\n} from \"@mui/material\";\r\nimport ImageSection from \"./PopupSections/Imagesection\";\r\nimport RTEsection from \"./PopupSections/RTEsection\";\r\nimport ButtonSection from \"./PopupSections/Button\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DragIndicatorIcon from \"@mui/icons-material/DragIndicator\";\r\nimport { Image, TextFormat, Code, VideoLibrary, GifBox, Link, Opacity } from \"@mui/icons-material\";\r\nimport HtmlSection from \"./PopupSections/HtmlSection\";\r\nimport VideoSection from \"./PopupSections/VideoSection\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport \"../guideDesign/Canvas.module.css\";\r\nimport PerfectScrollbar from \"react-perfect-scrollbar\";\r\nimport \"react-perfect-scrollbar/dist/css/styles.css\";\r\nimport WarningIcon from \"@mui/icons-material/Warning\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport { color } from \"jodit/esm/plugins/color/color\";\r\nimport { useTranslation } from 'react-i18next';\r\nexport const TOOLTIP_MN_WIDTH = 500;\r\n\r\n// Helper function to convert hex color to rgba with opacity\r\nconst hexToRgba = (hex: string, opacity: number): string => {\r\n\t// Remove # if present\r\n\thex = hex.replace('#', '');\r\n\r\n\t// Parse hex values\r\n\tconst r = parseInt(hex.substring(0, 2), 16);\r\n\tconst g = parseInt(hex.substring(2, 4), 16);\r\n\tconst b = parseInt(hex.substring(4, 6), 16);\r\n\r\n\treturn `rgba(${r}, ${g}, ${b}, ${opacity})`;\r\n};\r\n\r\ntype SectionType = { type: \"image\" | \"button\" | \"video\" | \"gif\" | \"html\" } | { type: \"text\" }; // Only text sections have IDs\r\nconst GuidePopup = ({\r\n\tselectedStepType,\r\n\tguideStep,\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\ttextBoxRef,\r\n\thtmlContent,\r\n\tsetHtmlContent,\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tsetImageName,\r\n\timageName,\r\n\topenStepDropdown,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\tisUnSavedChanges,\r\n\thandleLeave,\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\taddNewButton,\r\n\t\tupdateButton,\r\n\t\tguideListByOrg,\r\n\t\tgetGuildeListByOrg,\r\n\t\tupdateButtonInteraction,\r\n\t\taddNewImageContainer,\r\n\t\tdismissData,\r\n\t\tsetSelectActions,\r\n\t\tcurrentButtonName,\r\n\t\tsetCurrentButtonName,\r\n\t\ttargetURL,\r\n\t\tsetTargetURL,\r\n\t\tselectedInteraction,\r\n\t\tsetSelectedInteraction,\r\n\t\topenInteractionList,\r\n\t\tsetOpenInteractionList,\r\n\t\tselectedTab,\r\n\t\tsetSelectedTab,\r\n\t\tloading,\r\n\t\tsetLoading,\r\n\t\taddNewRTEContainer,\r\n\t\tdismiss,\r\n\t\tcurrentStepIndex,\r\n\t\tselectedOption,\r\n\t\tprogress,\r\n\t\tsteps,\r\n\t\tsetProgress,\r\n\t\tselectedTemplate,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tupdateTooltipButtonInteraction,\r\n\t\tselectedTemplateTour,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tcreateWithAI\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [open, setOpen] = useState(true);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [sections, setSections] = useState<SectionType[]>([{ type: \"image\" }, { type: \"text\" }, { type: \"button\" }]);\r\n\tconst [draggingIndex, setDraggingIndex] = useState<number | null>(null);\r\n\r\n\tconst [sectionCounts, setSectionCounts] = useState({\r\n\t\timage: 1, // Start with one of each as per initial sections state\r\n\t\ttext: 1,\r\n\t\tbutton: 1,\r\n\t\tvideo: 0,\r\n\t\tgif: 0,\r\n\t\thtml: 0\r\n\t});\r\n\r\n\t// Maximum allowed sections of each type\r\n\tconst MAX_SECTIONS = {\r\n\t\timage: 3,\r\n\t\ttext: 3, // RTE sections\r\n\t\tbutton: 3,\r\n\t\tvideo: 3,\r\n\t\tgif: 3,\r\n\t\thtml: 3\r\n\t};\r\n\r\n\t// Helper function to check if a section type has reached its limit\r\n\tconst hasReachedLimit = (type: SectionType[\"type\"]): boolean => {\r\n\t\t// Map \"text\" to \"text\" for the check\r\n\t\tconst checkType = type === \"text\" ? \"text\" : type;\r\n\t\treturn sectionCounts[checkType] >= MAX_SECTIONS[checkType];\r\n\t};\r\n\r\n\t//const [imageSrc, setImageSrc] = useState<string>(\"\");\r\n\t//const [imageName, setImageName] = useState<string>(\"\");\r\n\t//const [selectedActions, setSelectActions] = useState<string>(\"close\");\r\n\t//const [selectedTab, setSelectedTab] = useState<string>(\"new-tab\");\r\n\t//const [targetURL, setTargetURL] = useState<string>(\"\");\r\n\t//const [selectedInteraction, setSelectedInteraction] = useState(null);\r\n\t//const [currentButtonName, setCurrentButtonName] = useState(\"\");\r\n\t//const [openInteractionList, setOpenInteractionList] = useState(false);\r\n\t//const [loading, setLoading] = useState(false);\r\n\tconst [action, setAction] = useState(\"close\");\r\n\tconst userInfo = localStorage.getItem(\"userInfo\");\r\n\tconst userInfoObj = JSON.parse(userInfo || \"{}\");\r\n\tconst orgDetails = JSON.parse(userInfoObj.orgDetails || \"{}\");\r\n\tconst organizationId = orgDetails.OrganizationId;\r\n\tconst overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\tconst guidePopUpRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t//Added Zustand here\r\n\tconst {\r\n\t\tdesignPopup,\r\n\t\tannouncementJson,\r\n\t\tcurrentStep,\r\n\t\tsetSettingAnchorEl,\r\n\t\tsettingAnchorEl,\r\n\t\tupdateButtonAction,\r\n\t\tgetCurrentButtonInfo,\r\n\t\tbuttonsContainer,\r\n\t\tbuttonId,\r\n\t\tsetButtonId,\r\n\t\tcuntainerId,\r\n\t\tsetCuntainerId,\r\n\t\tbtnname,\r\n\t\tsetBtnName,\r\n\t\trtesContainer,\r\n\t\timagesContainer,\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst theme = useTheme();\r\n\tconst isFullScreen = useMediaQuery(theme.breakpoints.down(\"sm\"));\r\n\r\n\t// Synchronize local sectionCounts with actual store state\r\n\tuseEffect(() => {\r\n\t\tsetSectionCounts({\r\n\t\t\timage: imagesContainer.length,\r\n\t\t\ttext: rtesContainer.length,\r\n\t\t\tbutton: buttonsContainer.length,\r\n\t\t\tvideo: 0,\r\n\t\t\tgif: 0,\r\n\t\t\thtml: 0\r\n\t\t});\r\n\t}, [buttonsContainer.length, rtesContainer.length, imagesContainer.length]);\r\n\r\n\tconst handleCloseInteraction = () => {\r\n\t\tsetOpenInteractionList(false);\r\n\t};\r\n\r\n\tconst handleOpenInteraction = () => {\r\n\t\tsetOpenInteractionList(true);\r\n\t\tif (organizationId && !guideListByOrg.length) {\r\n\t\t\t(async () => {\r\n\t\t\t\tsetLoading(true);\r\n\t\t\t\tawait getGuildeListByOrg(organizationId);\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t})();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClose = (_event?: object, reason?: string) => {\r\n\t\tif (reason === \"backdropClick\" || reason === \"escapeKeyDown\") {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetOpen(false);\r\n\t};\r\n\r\n\tconst handleAddIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tif (hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tsetAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handlePopoverClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\tconst handleAddSection = (type: SectionType[\"type\"]) => {\r\n\t\t// Check if we've reached the limit for this section type\r\n\t\tif (hasReachedLimit(type)) {\r\n\t\t\t// Don't add more sections if limit is reached\r\n\t\t\tsetAnchorEl(null);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tif (type === \"button\") {\r\n\t\t\t// Create and add a new button with default values\r\n\t\t\taddNewButton(\r\n\t\t\t\t{\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\tname: \"Button 1\",\r\n\t\t\t\t\tposition: \"center\",\r\n\t\t\t\t\ttype: \"primary\",\r\n\t\t\t\t\tisEditing: false,\r\n\t\t\t\t\tindex: 0,\r\n\t\t\t\t\tstyle: { ...defaultButtonColors },\r\n\t\t\t\t\tactions: {\r\n\t\t\t\t\t\tvalue: \"close\", // Default action is \"close\"\r\n\t\t\t\t\t\ttargetURL: targetURL, // Default empty target URL\r\n\t\t\t\t\t\ttab: \"same-tab\", // Default tab behavior\r\n\t\t\t\t\t},\r\n\t\t\t\t},\r\n\t\t\t\t\"\"\r\n\t\t\t);\r\n\r\n\t\t\t// Set the temporary colors (this might be for styling the new button)\r\n\t\t\tsetTempColors(defaultButtonColors);\r\n\r\n\t\t\t// Optionally, set the selected actions (if needed outside of the button creation)\r\n\t\t\tsetSelectedActions({\r\n\t\t\t\tvalue: \"close\", // Default action is \"close\"\r\n\t\t\t\ttargetURL: targetURL, // Default empty target URL\r\n\t\t\t\ttab: \"same-tab\", // Default tab behavior\r\n\t\t\t});\r\n\t\t} else if (type === \"image\") {\r\n\t\t\taddNewImageContainer();\r\n\t\t} else if (type === \"text\") {\r\n\t\t\taddNewRTEContainer();\r\n\t\t} else {\r\n\t\t\t// For other section types\r\n\t\t\tsetSections((prevSections) => [...prevSections, { type } as SectionType]);\r\n\t\t}\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleDragStart = (index: number) => {\r\n\t\tsetDraggingIndex(index);\r\n\t};\r\n\r\n\tconst handleDragEnter = (index: number) => {\r\n\t\tif (draggingIndex !== null && draggingIndex !== index) {\r\n\t\t\tconst reorderedSections = [...sections];\r\n\t\t\tconst [removed] = reorderedSections.splice(draggingIndex, 1);\r\n\t\t\treorderedSections.splice(index, 0, removed);\r\n\t\t\tsetSections(reorderedSections);\r\n\t\t\tsetDraggingIndex(index);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleDragEnd = () => {\r\n\t\tsetDraggingIndex(null);\r\n\t};\r\n\r\n\tconst handleDeleteRTESection = (index: number) => {\r\n\t\t// RTE section deletion is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle deletion of image sections\r\n\tconst handleDeleteImageSection = () => {\r\n\t\t// Image section deletion is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle deletion of button sections\r\n\tconst handleDeleteButtonSection = () => {\r\n\t\t// Button section deletion is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle cloning of RTE sections\r\n\tconst handleCloneRTESection = () => {\r\n\t\t// Check if we've reached the limit for RTE sections\r\n\t\tif (hasReachedLimit(\"text\")) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\t// RTE section cloning is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle cloning of image sections\r\n\tconst handleCloneImageSection = () => {\r\n\t\t// Check if we've reached the limit for image sections\r\n\t\tif (hasReachedLimit(\"image\")) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\t// Image section cloning is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\r\n\t// Function to handle cloning of button sections\r\n\tconst handleCloneButtonSection = () => {\r\n\t\t// Check if we've reached the limit for button sections\r\n\t\tif (hasReachedLimit(\"button\")) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\t// Button section cloning is handled by the store\r\n\t\t// Section counts will be automatically updated via useEffect\r\n\t};\r\n\t// State to track if scrolling is needed\r\n\tconst [needsScrolling, setNeedsScrolling] = useState(false);\r\n\tconst scrollbarRef = useRef<any>(null);\r\n\tconst renderSection = (section: SectionType, index: number) => {\r\n\t\tswitch (section.type) {\r\n\t\t\tcase \"image\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<ImageSection\r\n\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\tsetImageSrc={setImageSrc}\r\n\t\t\t\t\t\timageSrc={imageSrc}\r\n\t\t\t\t\t\tsetImageName={setImageName}\r\n\t\t\t\t\t\timageName={imageName}\r\n\t\t\t\t\t\tonDelete={handleDeleteImageSection}\r\n\t\t\t\t\t\tonClone={handleCloneImageSection}\r\n\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"image\")}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tcase \"text\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\tkey={index} // Use unique ID as the key for RTESection\r\n\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\tisBanner={false}\r\n\t\t\t\t\t\thandleDeleteRTESection={() => handleDeleteRTESection(index)}\r\n\t\t\t\t\t\tindex={index}\r\n\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t\tonClone={handleCloneRTESection}\r\n\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"text\")}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tcase \"button\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\tisBanner={false}\r\n\t\t\t\t\t\tonDelete={handleDeleteButtonSection}\r\n\t\t\t\t\t\tonClone={handleCloneButtonSection}\r\n\t\t\t\t\t\tisCloneDisabled={hasReachedLimit(\"button\")}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tcase \"video\":\r\n\t\t\t\treturn <VideoSection key={index} />;\r\n\t\t\tcase \"html\":\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<HtmlSection\r\n\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\thtmlContent={htmlContent}\r\n\t\t\t\t\t\tsetHtmlContent={setHtmlContent}\r\n\t\t\t\t\t\tisBanner={false}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\tdefault:\r\n\t\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\r\n\tconst style = announcementJson.GuideStep.find((step) => step.stepName === currentStep)?.Canvas as\r\n\t\t| Record<string, unknown>\r\n\t\t| undefined;\r\n\r\n\tconst popupStyle: React.CSSProperties = {\r\n\t\t//maxWidth: \"533px\",\r\n\t\t//\tminWidth: TOOLTIP_MN_WIDTH,\r\n\t\tmaxWidth: `${style?.Width} !important` || \"500px !important\",\r\n\t\t// maxHeight: \"400px\",\r\n\t\twidth: `${style?.Width || 500}px`,\r\n\t\tborderRadius: `${style?.Radius ?? 8}px`,\r\n\t\tborderWidth: `${style?.BorderWidth || 0}px`,\r\n\t\tdisplay: \"flex\" as const,\r\n\t\tflexDirection: \"column\" as const,\r\n\t\tborderColor: `${style?.BorderColor || \"transparent\"}`,\r\n\t\tbackgroundColor: `${style?.BackgroundColor || \"#fff\"}`,\r\n\t\tborder: `${style?.BorderSize || \"0\"}px solid ${style?.BorderColor || \"none\"}`,\r\n\t\toverflow: \"visible\",\r\n\t};\r\n\r\n\tconst sectionStyle = {\r\n\t\twidth: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\tposition: \"relative\" as const,\r\n\t\t\"&:hover .add-icon\": {\r\n\t\t\tdisplay: \"flex\",\r\n\t\t},\r\n\t\t\"&:hover .side-add-icon\": {\r\n\t\t\tdisplay: \"flex\",\r\n\t\t},\r\n\t\t\"&:hover .add-section\": {\r\n\t\t\topacity: \"1\",\r\n\t\t},\r\n\t};\r\n\r\n\tconst dragButtonStyle = {\r\n\t\tposition: \"absolute\" as const,\r\n\t\tleft: \"-60px\",\r\n\t\ttop: \"50%\",\r\n\t\ttransform: \"translateY(-50%)\",\r\n\t\tcursor: \"move\",\r\n\t\tzIndex: 1000,\r\n\t};\r\n\r\n\tconst sideAddButtonStyle = {\r\n\t\tposition: \"absolute\" as const,\r\n\t\tright: \"-38px\",\r\n\t\ttop: \"50%\",\r\n\t\ttransform: \"translateY(-50%)\",\r\n\t\twidth: \"18px\",\r\n\t\theight: \"100%\",\r\n\t\tborderRadius: \"6px\",\r\n\t\tdisplay: \"none\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tcursor: \"pointer\",\r\n\t\tzIndex: 1000,\r\n\t\t\"&:hover\": {\r\n\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t},\r\n\t};\r\n\r\n\tconst handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSelectedTab((event.target as HTMLInputElement).value);\r\n\t};\r\n\r\n\tconst handleCloseSettingPopup = (containerId: string, buttonId: string) => {\r\n\t\tupdateButtonAction(containerId, buttonId, selectedActions);\r\n\t\tupdateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId: \"\",\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\tconst [targetURLError, setTargetURLError] = useState(\"\");\r\n\r\n\tconst validateTargetURL = (url: string) => {\r\n\t\tif (selectedActions.value === \"open-url\") {\r\n\t\t\tif (!url) {\r\n\t\t\t\treturn \"URL is required\";\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tnew URL(url);\r\n\t\t\t\treturn \"\";\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn \"Invalid URL\";\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn \"\";\r\n\t};\r\n\r\n\tconst handleApplyChanges = (containerId: string, buttonId: string) => {\r\n\t\tconst error = validateTargetURL(targetURL);\r\n\t\tsetTargetURLError(error); // Set the error message for display\r\n\r\n\t\tif (error) {\r\n\t\t\treturn; // Prevent applying changes if there's a validation error\r\n\t\t}\r\n\r\n\t\tconst buttonNameToUpdate = !currentButtonName || !currentButtonName.trim()\r\n\t\t\t? curronButtonInfo.title // Retain the previously saved button name\r\n\t\t\t: currentButtonName;\r\n\t\tsetCurrentButtonName(buttonNameToUpdate);\r\n\r\n\t\tupdateButton(containerId, buttonId, \"style\", tempColors);\r\n\t\tupdateButtonAction(containerId, buttonId, selectedActions); // Update the selected actions\r\n\t\tupdateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tupdateButton(containerId, buttonId, \"name\", buttonNameToUpdate);\r\n\t\tupdateButton(containerId, buttonId, \"actions\", selectedActions); // Update button actions\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\tconst handleURLChange = (e: any) => {\r\n\t\tconst newURL = e.target.value;\r\n\t\tsetTargetURL(newURL);\r\n\r\n\t\t// Validate the URL and update the error state\r\n\t\tconst error = validateTargetURL(newURL);\r\n\t\tsetTargetURLError(error);\r\n\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: selectedActions.value,\r\n\t\t\ttargetURL: newURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\",\r\n\t\t});\r\n\t};\r\n\r\n\tconst curronButtonInfo = useMemo(() => {\r\n\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\tsetCurrentButtonName(result.title);\r\n\t\tsetBtnName(result.title);\r\n\t\tsetAction(result.value);\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\tconst defaultButtonColors = {\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tborderColor: \"#70afaf\",\r\n\t\tcolor: \"#ffffff\",\r\n\t};\r\n\r\n\tconst [selectedActions, setSelectedActions] = useState<TButtonAction>({\r\n\t\tvalue: \"close\", // Default action\r\n\t\ttargetURL: \"\", // Default empty target URL\r\n\t\ttab: \"same-tab\", // Default tab (same-tab)\r\n\t});\r\n\tconst [tempColors, setTempColors] = useState(defaultButtonColors);\r\n\tconst selectedButton = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst handleSelectButton = (containerId: any, buttonId: any) => {\r\n\t\t\tconst selectedButton = getCurrentButtonInfo(containerId, buttonId);\r\n\t\t\tif (selectedButton) {\r\n\t\t\t\tsetTargetURL(selectedButton.targetURL || \"\");\r\n\t\t\t\tsetTempColors({\r\n\t\t\t\t\tbackgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,\r\n\t\t\t\t\tborderColor: selectedButton.borderColor || defaultButtonColors.borderColor,\r\n\t\t\t\t\tcolor: selectedButton.textColor || defaultButtonColors.color,\r\n\t\t\t\t});\r\n\t\t\t\tsetSelectedActions({\r\n\t\t\t\t\tvalue: selectedButton.selectedActions || \"close\", // Default to \"close\" if no action is set\r\n\t\t\t\t\ttargetURL: selectedButton.targetURL || targetURL, // Can be updated later if needed\r\n\t\t\t\t\ttab: \"same-tab\", // Default tab behavior\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t};\r\n\t\thandleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\t// Function to handle color changes in the color picker\r\n\tconst handleColorChange = (e: any, targetName: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tsetTempColors((prev) => ({\r\n\t\t\t...prev,\r\n\t\t\t[targetName]: value,\r\n\t\t}));\r\n\t};\r\nuseEffect(() => {\r\n\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: selectedActions.value, // Default action\r\n\t\t\ttargetURL: targetURL, // Default empty target URL\r\n\t\t\ttab: \"same-tab\", // Default tab (same-tab)\r\n\t\t});\r\n}, []);\r\n// Check if content needs scrolling with improved detection\r\nuseEffect(() => {\r\n\tconst checkScrollNeeded = () => {\r\n\t\tif (guidePopUpRef.current) {\r\n\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\tguidePopUpRef.current.style.height = 'auto';\r\n\t\t\tconst contentHeight = guidePopUpRef.current.scrollHeight;\r\n\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t// Force update scrollbar\r\n\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t}\r\n\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 10);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t\r\n\tcheckScrollNeeded();\r\n\r\n\t\r\n\tconst timeouts = [\r\n\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t];\r\n\r\n\t\r\n\tlet resizeObserver: ResizeObserver | null = null;\r\n\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\tif (guidePopUpRef.current && window.ResizeObserver) {\r\n\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t});\r\n\t\tresizeObserver.observe(guidePopUpRef.current);\r\n\t}\r\n\r\n\t\r\n\tif (guidePopUpRef.current && window.MutationObserver) {\r\n\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t});\r\n\t\tmutationObserver.observe(guidePopUpRef.current, {\r\n\t\t\tchildList: true,\r\n\t\t\tsubtree: true,\r\n\t\t\tattributes: true,\r\n\t\t\tattributeFilter: ['style', 'class']\r\n\t\t});\r\n\t}\r\n\r\n\treturn () => {\r\n\t\ttimeouts.forEach(clearTimeout);\r\n\t\tif (resizeObserver) {\r\n\t\t\tresizeObserver.disconnect();\r\n\t\t}\r\n\t\tif (mutationObserver) {\r\n\t\t\tmutationObserver.disconnect();\r\n\t\t}\r\n\t};\r\n}, [ currentStep]);\r\n\tconst handleChangeActions = (e: SelectChangeEvent) => {\r\n\t\tconst v: TInteractionValue = e.target.value as TInteractionValue; // Casting to TInteractionValue\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: v, // Ensure that selectedActions.value is of type TInteractionValue\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\", // Ensure tab is a valid value\r\n\t\t});\r\n\t};\r\n\t// useEffect(() => {\r\n\t// \tif (selectedButton) {\r\n\t// \t  selectedButton.targetURL = targetURL;  // Update selectedButton's targetURL whenever targetURL state changes\r\n\t// \t}\r\n\t//   }, [targetURL]);  // Dependency on `targetURL`\r\n\t// Dependency on `targetURL`\r\n\r\n\r\n\treturn (\r\n\t\t<div className=\"qadpt-annpopop\">\r\n\t\t\t{overlayEnabled && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\twidth: \"100vw\",\r\n\t\t\t\t\t\theight: \"100vh\",\r\n\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\tzIndex: 9,\r\n\t\t\t\t\t}}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t{isUnSavedChanges && openWarning &&(\r\n\t\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\r\n\t\t\t<Dialog\r\n\t\t\t\tclassName=\"qadpt-guide-popup\"\r\n\t\t\t\topen={open}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tfullScreen={isFullScreen}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: popupStyle,\r\n\t\t\t\t}}\r\n\t\t\t\tmaxWidth={false}\r\n\t\t\t\tdisableEnforceFocus={true}\r\n\t\t\t>\r\n\t\t\t\t<div style={{placeContent:\"end\",display:\"flex\"}}>\r\n\t\t\t\t\t{dismiss && (\r\n\t\t\t\t\t\t<IconButton className=\"qadpt-dismiss\"\r\n\t\t\t\t\t\t\t//onClick={handleCloseBanner}\r\n\t\t\t\t\t\t>\r\n<CloseIcon sx={{ zoom: 1,color:\"#000\"}} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\tstyle={{\r\n  maxHeight: \"500px\",\r\n  ...(progress\r\n    ? {\r\n        borderTopLeftRadius: `${style?.Radius ?? 8}px`,\r\n        borderTopRightRadius: `${style?.Radius ?? 8}px`,\r\n        borderBottomLeftRadius: \"0px\",\r\n        borderBottomRightRadius: \"0px\",\r\n      }\r\n    : {\r\n        borderRadius: `${style?.Radius ?? 8}px`,\r\n      }),\r\n}}\r\n\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: false,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<DialogContent\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tpadding: 0,\r\n\t\t\t\t\t\tpaddingTop: \"10px\", // Add 10px padding to top of GuidePopUp\r\n\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\t\tref={guidePopUpRef}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* {dismissData?.dismisssel ? (\r\n\t\t\t\t\t\t<DialogTitle\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"end\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t// sx={{ position: \"absolute\", right: 8, top: 8, color: `${dissmissIconColor}` }}\r\n\t\t\t\t\t\t\t\t//onClick={handleClose}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<CloseIcon\r\n\t\t\t\t\t\t\t\t\t// color={dismissData.Color}\r\n\t\t\t\t\t\t\t\t\thtmlColor={dismissData.Color}\r\n\t\t\t\t\t\t\t\t\t// fontSize=\"medium\"\r\n\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"1.5rem !important\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</DialogTitle>\r\n\t\t\t\t\t) : null} */}\r\n\r\n\r\n\t\t\t\t\t{/* <PerfectScrollbar> */}\r\n\t\t\t\t\t<Box sx={{padding: `${style?.Padding || 12}px`,\r\n}}>\r\n\t\t\t\t\t\t{sections.map((section, index) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t...sectionStyle,\r\n\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tborderTopWidth: index !== 0 ? \"1px\" : \"0px\",\r\n\t\t\t\t\t\t\t\t\t\tborderTopColor: index !== 0 ? \"var(--primarycolor)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\tborderTopStyle: index !== 0 ? \"dotted\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdraggable\r\n\t\t\t\t\t\t\t\tonDragStart={() => handleDragStart(index)}\r\n\t\t\t\t\t\t\t\tonDragEnter={() => handleDragEnter(index)}\r\n\t\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tclassName=\"drag-icon\"\r\n\t\t\t\t\t\t\t\t\tsx={dragButtonStyle}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<DragIndicatorIcon\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ color: \"#5F9EA0\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t\t{renderSection(section, index)}\r\n\t\t\t\t\t\t\t\t{index !== 0 && (\r\n\t\t\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\t\t\ttitle={\r\n\t\t\t\t\t\t\t\t\t\t\thasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t? translate(\"Maximum limit reached for all section types\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: translate(\"Add Section\")\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"add-section\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleAddIconClick}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttop: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? \"not-allowed\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled={hasReachedLimit(\"text\") && hasReachedLimit(\"button\") && hasReachedLimit(\"image\")}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t{Boolean(settingAnchorEl.value) ? (\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-designpopup qadpt-btnprop\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Properties\")}</div>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-canblock qadpt-btnpro\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"16px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Button Name\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={currentButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Button Name\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setCurrentButtonName(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>{translate(\"Button Action\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedActions.value}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue=\"close\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChangeActions}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": {\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"close\">{translate(\"Close\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"open-url\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Previous\">{translate(\"Previous\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Next\">{translate(\"Next\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Restart\">{translate(\"Restart\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t\t\t{selectedActions.value === \"open-url\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Enter URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={targetURL}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"https://quixy.com\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst newURL = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetTargetURL(newURL); // Update the `targetURL` state with the new value\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thandleURLChange(e); // Update the selectedButton.targetURL with the new value\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\terror={!!targetURLError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thelperText={targetURLError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<ToggleButtonGroup\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedTab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleChangeTabs}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\texclusive\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={translate(\"open in tab\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginY: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"35px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{[\"new-tab\", \"same-tab\"].map((tab) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ToggleButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"new tab\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-selected\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"2px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:last-child\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderLeft: \"1px solid var(--primarycolor) !important\", // Remove left border for the last button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</ToggleButton>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</ToggleButtonGroup>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t\t\t\t\t\t{/* {selectedActions === \"start-interaction\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tChoose Interaction\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Autocomplete\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t// sx={{ width: 300 }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\topen={openInteractionList}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedInteraction}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(event, newValue) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedInteraction(newValue);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonOpen={handleOpenInteraction}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClose={handleCloseInteraction}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tisOptionEqualToValue={(option, value) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn option.guideId === value.guideId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgetOptionLabel={(option) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn option.title;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfreeSolo\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toptions={guideListByOrg}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tloading={loading}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trenderInput={(params) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...params}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Select Interaction\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tinputLabel: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tshrink: false,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null} */}\r\n\t\t\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t\t\t\t{/* <Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>Button Color</Typography> */}\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={tempColors.backgroundColor}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"backgroundColor\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={tempColors.borderColor}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"borderColor\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Text\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={tempColors.color}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"color\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t\r\n\t\t\t\t\t{/* </PerfectScrollbar> */}\r\n\t\t\t\t\t</DialogContent>\r\n\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t<Box sx={{\t\tborderRadius: `${style?.Radius ?? 8}px`,\r\n}}>\r\n\t\t\t\t{progress  &&\r\n\t\t\t\t\t(selectedOption === 1 || selectedOption === \"\" ? (\r\n\t\t\t\t\t\t<DotsStepper\r\n\t\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\t\tProgressColor = {ProgressColor}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)\r\n\t\t\t\t\t: selectedOption === 2 ? (\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: hexToRgba(ProgressColor, 0.45),\r\n\t\t\t\t\t\t\t\t\t\t'& .MuiLinearProgress-bar': {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t  },}}\r\n\t\t\t\t\t\t\t\tvalue={(currentStep / steps.length) * 100}\r\n\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t) : selectedOption === 3 ? (\r\n\t\t\t\t\t\t<div style={{ padding: \"8px\" }}>\r\n\t\t\t\t\t\t\t<BreadCrumpStepper\r\n\t\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\t\tProgressColor={ProgressColor}\r\n\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t): selectedOption === 4 ? (\r\n\t\t\t\t\t\t<Breadcrumbs\r\n\t\t\t\t\t\t\taria-label=\"breadcrumb\"\r\n\t\t\t\t\t\t\tsx={{ padding: \"8px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tStep {currentStep} of {steps.length}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</Breadcrumbs>\r\n\t\t\t\t\t\t\t) : null)}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t{anchorEl && (\r\n\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-secprop\"\r\n\t\t\t\t\t\topen={Boolean(anchorEl)}\r\n\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\tonClose={handlePopoverClose}\r\n\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"auto\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 1302,\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t// top: `${anchorEl.getBoundingClientRect().bottom + 8}px`,\r\n\t\t\t\t\t\t\t// left: `${anchorEl.getBoundingClientRect().left - 150}px`,\r\n\t\t\t\t\t\t\ttransform: \"none\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\tflexDirection=\"row\"\r\n\t\t\t\t\t\t\tgap=\"16px\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{/* Rich Text Section */}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={hasReachedLimit(\"text\") ? translate(\"Maximum limit of 3 Rich Text sections reached\") : \"\"}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"text\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"text\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"text\") && handleAddSection(\"text\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<TextFormat />\r\n\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>{translate(\"Rich Text\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t\t{/* Button Section */}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={hasReachedLimit(\"button\") ? translate(\"Maximum limit of 3 Button sections reached\") : \"\"}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"button\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"button\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"button\") && handleAddSection(\"button\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Link />\r\n\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>{translate(\"Button\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t\t{/* Image Section */}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={hasReachedLimit(\"image\") ? translate(\"Maximum limit of 3 Image sections reached\") : \"\"}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tcursor: hasReachedLimit(\"image\") ? \"not-allowed\" : \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\topacity: hasReachedLimit(\"image\") ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={() => !hasReachedLimit(\"image\") && handleAddSection(\"image\")}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Image />\r\n\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>{translate(\"Image\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Coming Soon\")}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0.5,\r\n\t\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t// onClick={() => handleAddSection(\"video\")}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<VideoLibrary />\r\n\t\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>{translate(\"Video\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t{/* {<Box\r\n\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t\tonClick={() => handleAddSection(\"gif\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<GifBox />\r\n\t\t\t\t\t\t\t\t<Typography variant=\"caption\">Gif</Typography>\r\n\t\t\t\t\t\t\t</Box>*/}\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle={translate(\"Coming Soon\")}\r\n\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0.5,\r\n\t\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"24px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleAddSection(\"html\")}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Code />\r\n\t\t\t\t\t\t\t\t\t\t<Typography variant=\"caption\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"11px !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>{translate(\"HTML\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Popover>\r\n\t\t\t\t)}\r\n\r\n\t\t\t</Dialog>\r\n\t\t</div>\r\n\t);\r\n};\r\nexport default GuidePopup;\r\nconst DotsStepper = ({ steps, activeStep, ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {\r\n\treturn (\r\n\t\t<MobileStepper\r\n\t\t\tvariant=\"dots\"\r\n\t\t\tsteps={steps}\r\n\t\t\tposition=\"static\"\r\n\t\t\tactiveStep={activeStep - 1}\r\n\t\t\tsx={{ flexGrow: 1, display: \"flex\", justifyContent: \"center\",background:\"inherit\",\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\tbackgroundColor: ProgressColor, // active dot color\r\n\t\t\t  } }}\r\n\t\t\tnextButton={<></>}\r\n\t\t\tbackButton={<></>}\r\n\r\n\t\t/>\r\n\t);\r\n};\r\nconst BreadCrumpStepper = ({ steps, activeStep,ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {\r\n\treturn (\r\n\t\t<Box sx={{ flexGrow: 1 }}>\r\n\t\t  {/* Custom Step Indicators */}\r\n\t\t  <Box\r\n\t\t\tsx={{\r\n\t\t\t  display: 'flex',\r\n\t\t\t  justifyContent: 'center',\r\n\t\t\t  gap: \"4px\", // Adjust space between steps\r\n\t\t\t//   paddingTop: '15px',\r\n\t\t\t}}\r\n\t\t  >\r\n\t\t\t{Array.from({ length: steps }).map((_, index) => (\r\n\t\t\t  <div\r\n\t\t\t\tkey={index}\r\n\t\t\t\tstyle={{\r\n\t\t\t\t  width: '14px',\r\n\t\t\t\t  height: '4px',\r\n\t\t\t\t  backgroundColor: index === activeStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45), // Active color and inactive color\r\n\t\t\t\t  borderRadius: '100px',\r\n\t\t\t\t}}\r\n\t\t\t  />\r\n\t\t\t))}\r\n\t\t  </Box>\r\n\t\t</Box>\r\n\t  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEnE,SACCC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,SAAS,EAMTC,YAAY,EACZC,iBAAiB,EAIjBC,OAAO,EACPC,cAAc,EACdC,aAAa,EACbC,WAAW,QAEL,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,SAASC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAEC,YAAY,EAAUC,IAAI,QAAiB,qBAAqB;AAClG,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAO,kCAAkC;AACzC,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAEpD,OAAOC,UAAU,MAAM,sBAAsB;AAE7C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC/C,OAAO,MAAMC,gBAAgB,GAAG,GAAG;;AAEnC;AACA,MAAMC,SAAS,GAAGA,CAACC,GAAW,EAAEC,OAAe,KAAa;EAC3D;EACAD,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;;EAE1B;EACA,MAAMC,CAAC,GAAGC,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3C,MAAMC,CAAC,GAAGF,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3C,MAAME,CAAC,GAAGH,QAAQ,CAACJ,GAAG,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAE3C,OAAO,QAAQF,CAAC,KAAKG,CAAC,KAAKC,CAAC,KAAKN,OAAO,GAAG;AAC5C,CAAC;AAE8F;AAC/F,MAAMO,UAAU,GAAGA,CAAC;EACnBC,gBAAgB;EAChBC,SAAS;EACTC,WAAW;EACXC,QAAQ;EACRC,UAAU;EACVC,WAAW;EACXC,cAAc;EACdC,WAAW;EACXC,cAAc;EACdC,YAAY;EACZC,SAAS;EACTC,gBAAgB;EAChBC,WAAW;EACXC,cAAc;EACdC,gBAAgB;EAChBC;AACI,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;EACV,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGxC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLyC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,WAAW;IACXC,gBAAgB;IAChBC,iBAAiB;IACjBC,oBAAoB;IACpBC,SAAS;IACTC,YAAY;IACZC,mBAAmB;IACnBC,sBAAsB;IACtBC,mBAAmB;IACnBC,sBAAsB;IACtBC,WAAW;IACXC,cAAc;IACdC,OAAO;IACPC,UAAU;IACVC,kBAAkB;IAClBC,OAAO;IACPC,gBAAgB;IAChBC,cAAc;IACdC,QAAQ;IACRC,KAAK;IACLC,WAAW;IACXC,gBAAgB;IAChBC,yBAAyB;IACzBC,8BAA8B;IAC9BC,oBAAoB;IACpBC,mBAAmB;IACnBC,aAAa;IACbC,gBAAgB;IAChBC;EACD,CAAC,GAAG/E,cAAc,CAAEgF,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGpH,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACqH,QAAQ,EAAEC,WAAW,CAAC,GAAGtH,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACuH,QAAQ,EAAEC,WAAW,CAAC,GAAGxH,QAAQ,CAAgB,CAAC;IAAEyH,IAAI,EAAE;EAAQ,CAAC,EAAE;IAAEA,IAAI,EAAE;EAAO,CAAC,EAAE;IAAEA,IAAI,EAAE;EAAS,CAAC,CAAC,CAAC;EAClH,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3H,QAAQ,CAAgB,IAAI,CAAC;EAEvE,MAAM,CAAC4H,aAAa,EAAEC,gBAAgB,CAAC,GAAG7H,QAAQ,CAAC;IAClD8H,KAAK,EAAE,CAAC;IAAE;IACVC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG;IACpBN,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IAAE;IACTC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE;EACP,CAAC;;EAED;EACA,MAAME,eAAe,GAAIZ,IAAyB,IAAc;IAC/D;IACA,MAAMa,SAAS,GAAGb,IAAI,KAAK,MAAM,GAAG,MAAM,GAAGA,IAAI;IACjD,OAAOG,aAAa,CAACU,SAAS,CAAC,IAAIF,YAAY,CAACE,SAAS,CAAC;EAC3D,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxI,QAAQ,CAAC,OAAO,CAAC;EAC7C,MAAMyI,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EACjD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,IAAI,IAAI,CAAC;EAChD,MAAMM,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,CAACG,UAAU,IAAI,IAAI,CAAC;EAC7D,MAAMC,cAAc,GAAGD,UAAU,CAACE,cAAc;EAChD,MAAMC,cAAc,GAAGhH,cAAc,CAAEgF,KAAK,IAAKA,KAAK,CAACgC,cAAc,CAAC;EACtE,MAAMC,aAAa,GAAGjJ,MAAM,CAAwB,IAAI,CAAC;;EAEzD;EACA,MAAM;IACLkJ,WAAW;IACXC,gBAAgB;IAChBC,WAAW;IACXC,kBAAkB;IAClBC,eAAe;IACfC,kBAAkB;IAClBC,oBAAoB;IACpBC,gBAAgB;IAChBC,QAAQ;IACRC,WAAW;IACXC,WAAW;IACXC,cAAc;IACdC,OAAO;IACPC,UAAU;IACVC,aAAa;IACbC;EACD,CAAC,GAAGjI,cAAc,CAAEgF,KAAK,IAAKA,KAAK,CAAC;EAEpC,MAAMkD,KAAK,GAAG9J,QAAQ,CAAC,CAAC;EACxB,MAAM+J,YAAY,GAAGhK,aAAa,CAAC+J,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAEhE;EACAtK,SAAS,CAAC,MAAM;IACf4H,gBAAgB,CAAC;MAChBC,KAAK,EAAEqC,eAAe,CAACK,MAAM;MAC7BzC,IAAI,EAAEmC,aAAa,CAACM,MAAM;MAC1BxC,MAAM,EAAE2B,gBAAgB,CAACa,MAAM;MAC/BvC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE;IACP,CAAC,CAAC;EACH,CAAC,EAAE,CAACwB,gBAAgB,CAACa,MAAM,EAAEN,aAAa,CAACM,MAAM,EAAEL,eAAe,CAACK,MAAM,CAAC,CAAC;EAE3E,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACpC3E,sBAAsB,CAAC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAM4E,qBAAqB,GAAGA,CAAA,KAAM;IACnC5E,sBAAsB,CAAC,IAAI,CAAC;IAC5B,IAAIkD,cAAc,IAAI,CAAC/D,cAAc,CAACuF,MAAM,EAAE;MAC7C,CAAC,YAAY;QACZtE,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMhB,kBAAkB,CAAC8D,cAAc,CAAC;QACxC9C,UAAU,CAAC,KAAK,CAAC;MAClB,CAAC,EAAE,CAAC;IACL;EACD,CAAC;EAED,MAAMyE,WAAW,GAAGA,CAACC,MAAe,EAAEC,MAAe,KAAK;IACzD,IAAIA,MAAM,KAAK,eAAe,IAAIA,MAAM,KAAK,eAAe,EAAE;MAC7D;IACD;IACAzD,OAAO,CAAC,KAAK,CAAC;EACf,CAAC;EAED,MAAM0D,kBAAkB,GAAIC,KAAoC,IAAK;IACpE,IAAI1C,eAAe,CAAC,MAAM,CAAC,IAAIA,eAAe,CAAC,QAAQ,CAAC,IAAIA,eAAe,CAAC,OAAO,CAAC,EAAE;MACrF;IACD;IACAf,WAAW,CAACyD,KAAK,CAACC,aAAa,CAAC;EACjC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAChC3D,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EACD,MAAM4D,gBAAgB,GAAIzD,IAAyB,IAAK;IACvD;IACA,IAAIY,eAAe,CAACZ,IAAI,CAAC,EAAE;MAC1B;MACAH,WAAW,CAAC,IAAI,CAAC;MACjB;IACD;IAEA,IAAIG,IAAI,KAAK,QAAQ,EAAE;MACtB;MACA1C,YAAY,CACX;QACCoG,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAE,QAAQ;QAClB9D,IAAI,EAAE,SAAS;QACf+D,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;UAAE,GAAGC;QAAoB,CAAC;QACjCC,OAAO,EAAE;UACRC,KAAK,EAAE,OAAO;UAAE;UAChBpG,SAAS,EAAEA,SAAS;UAAE;UACtBqG,GAAG,EAAE,UAAU,CAAE;QAClB;MACD,CAAC,EACD,EACD,CAAC;;MAED;MACAC,aAAa,CAACJ,mBAAmB,CAAC;;MAElC;MACAK,kBAAkB,CAAC;QAClBH,KAAK,EAAE,OAAO;QAAE;QAChBpG,SAAS,EAAEA,SAAS;QAAE;QACtBqG,GAAG,EAAE,UAAU,CAAE;MAClB,CAAC,CAAC;IACH,CAAC,MAAM,IAAIrE,IAAI,KAAK,OAAO,EAAE;MAC5BrC,oBAAoB,CAAC,CAAC;IACvB,CAAC,MAAM,IAAIqC,IAAI,KAAK,MAAM,EAAE;MAC3BtB,kBAAkB,CAAC,CAAC;IACrB,CAAC,MAAM;MACN;MACAqB,WAAW,CAAEyE,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAE;QAAExE;MAAK,CAAC,CAAgB,CAAC;IAC1E;IACAH,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAM4E,eAAe,GAAIT,KAAa,IAAK;IAC1C9D,gBAAgB,CAAC8D,KAAK,CAAC;EACxB,CAAC;EAED,MAAMU,eAAe,GAAIV,KAAa,IAAK;IAC1C,IAAI/D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK+D,KAAK,EAAE;MACtD,MAAMW,iBAAiB,GAAG,CAAC,GAAG7E,QAAQ,CAAC;MACvC,MAAM,CAAC8E,OAAO,CAAC,GAAGD,iBAAiB,CAACE,MAAM,CAAC5E,aAAa,EAAE,CAAC,CAAC;MAC5D0E,iBAAiB,CAACE,MAAM,CAACb,KAAK,EAAE,CAAC,EAAEY,OAAO,CAAC;MAC3C7E,WAAW,CAAC4E,iBAAiB,CAAC;MAC9BzE,gBAAgB,CAAC8D,KAAK,CAAC;IACxB;EACD,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC3B5E,gBAAgB,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM6E,sBAAsB,GAAIf,KAAa,IAAK;IACjD;IACA;EAAA,CACA;;EAED;EACA,MAAMgB,wBAAwB,GAAGA,CAAA,KAAM;IACtC;IACA;EAAA,CACA;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACvC;IACA;EAAA,CACA;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IACnC;IACA,IAAItE,eAAe,CAAC,MAAM,CAAC,EAAE;MAC5B,OAAO,CAAC;IACT;IACA;IACA;EACD,CAAC;;EAED;EACA,MAAMuE,uBAAuB,GAAGA,CAAA,KAAM;IACrC;IACA,IAAIvE,eAAe,CAAC,OAAO,CAAC,EAAE;MAC7B,OAAO,CAAC;IACT;IACA;IACA;EACD,CAAC;;EAED;EACA,MAAMwE,wBAAwB,GAAGA,CAAA,KAAM;IACtC;IACA,IAAIxE,eAAe,CAAC,QAAQ,CAAC,EAAE;MAC9B,OAAO,CAAC;IACT;IACA;IACA;EACD,CAAC;EACD;EACA,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG/M,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMgN,YAAY,GAAG9M,MAAM,CAAM,IAAI,CAAC;EACtC,MAAM+M,aAAa,GAAGA,CAACC,OAAoB,EAAEzB,KAAa,KAAK;IAC9D,QAAQyB,OAAO,CAACzF,IAAI;MACnB,KAAK,OAAO;QACX,oBACCjF,OAAA,CAAClB,YAAY;UAEZkC,WAAW,EAAEA,WAAY;UACzBC,QAAQ,EAAEA,QAAS;UACnBM,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA,SAAU;UACrBmJ,QAAQ,EAAEV,wBAAyB;UACnCW,OAAO,EAAER,uBAAwB;UACjCS,eAAe,EAAEhF,eAAe,CAAC,OAAO;QAAE,GAPrCoD,KAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CAAC;MAEJ,KAAK,MAAM;QACV,oBACCjL,OAAA,CAACjB,UAAU;UACE;UACZmC,UAAU,EAAEA,UAAW;UACvBgK,QAAQ,EAAE,KAAM;UAChBlB,sBAAsB,EAAEA,CAAA,KAAMA,sBAAsB,CAACf,KAAK,CAAE;UAC5DA,KAAK,EAAEA;UACP;UAAA;UACAkC,GAAG,EAAEjK,UAAW;UAChByF,aAAa,EAAEA,aAAc;UAC7BiE,OAAO,EAAET,qBAAsB;UAC/BU,eAAe,EAAEhF,eAAe,CAAC,MAAM;QAAE,GATpCoD,KAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CAAC;MAEJ,KAAK,QAAQ;QACZ,oBACCjL,OAAA,CAAChB,aAAa;UAEbqC,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/B4J,QAAQ,EAAE,KAAM;UAChBP,QAAQ,EAAET,yBAA0B;UACpCU,OAAO,EAAEP,wBAAyB;UAClCQ,eAAe,EAAEhF,eAAe,CAAC,QAAQ;QAAE,GANtCoD,KAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CAAC;MAEJ,KAAK,OAAO;QACX,oBAAOjL,OAAA,CAACP,YAAY,MAAMwJ,KAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MACpC,KAAK,MAAM;QACV,oBACCjL,OAAA,CAACR,WAAW;UAEX2B,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/B8J,QAAQ,EAAE;QAAM,GAHXjC,KAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIV,CAAC;MAEJ;QACC,OAAO,IAAI;IACb;EACD,CAAC;EAED,MAAM/B,KAAK,IAAAnH,qBAAA,GAAG8E,gBAAgB,CAACuE,SAAS,CAACC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,KAAKzE,WAAW,CAAC,cAAA/E,qBAAA,uBAAxEA,qBAAA,CAA0EyJ,MAE5E;EAEZ,MAAMC,UAA+B,GAAG;IACvC;IACA;IACAC,QAAQ,EAAE,GAAGxC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyC,KAAK,aAAa,IAAI,kBAAkB;IAC5D;IACAC,KAAK,EAAE,GAAG,CAAA1C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyC,KAAK,KAAI,GAAG,IAAI;IACjCE,YAAY,EAAE,IAAA7J,aAAA,GAAGkH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4C,MAAM,cAAA9J,aAAA,cAAAA,aAAA,GAAI,CAAC,IAAI;IACvC+J,WAAW,EAAE,GAAG,CAAA7C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8C,WAAW,KAAI,CAAC,IAAI;IAC3CC,OAAO,EAAE,MAAe;IACxBC,aAAa,EAAE,QAAiB;IAChCC,WAAW,EAAE,GAAG,CAAAjD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkD,WAAW,KAAI,aAAa,EAAE;IACrDC,eAAe,EAAE,GAAG,CAAAnD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoD,eAAe,KAAI,MAAM,EAAE;IACtDC,MAAM,EAAE,GAAG,CAAArD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEsD,UAAU,KAAI,GAAG,YAAY,CAAAtD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkD,WAAW,KAAI,MAAM,EAAE;IAC7EK,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,YAAY,GAAG;IACpBd,KAAK,EAAE,MAAM;IACbK,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBnD,QAAQ,EAAE,UAAmB;IAC7B,mBAAmB,EAAE;MACpBkD,OAAO,EAAE;IACV,CAAC;IACD,wBAAwB,EAAE;MACzBA,OAAO,EAAE;IACV,CAAC;IACD,sBAAsB,EAAE;MACvB3L,OAAO,EAAE;IACV;EACD,CAAC;EAED,MAAMqM,eAAe,GAAG;IACvB5D,QAAQ,EAAE,UAAmB;IAC7B6D,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACT,CAAC;EAED,MAAMC,kBAAkB,GAAG;IAC1BlE,QAAQ,EAAE,UAAmB;IAC7BmE,KAAK,EAAE,OAAO;IACdL,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,kBAAkB;IAC7BlB,KAAK,EAAE,MAAM;IACbuB,MAAM,EAAE,MAAM;IACdtB,YAAY,EAAE,KAAK;IACnBI,OAAO,EAAE,MAAM;IACfmB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBhB,eAAe,EAAE,SAAS;IAC1BU,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,IAAI;IACZ,SAAS,EAAE;MACVX,eAAe,EAAE;IAClB;EACD,CAAC;EAED,MAAMiB,gBAAgB,GAAI/E,KAAoC,IAAK;IAClE/E,cAAc,CAAE+E,KAAK,CAACgF,MAAM,CAAsBlE,KAAK,CAAC;EACzD,CAAC;EAED,MAAMmE,uBAAuB,GAAGA,CAACC,WAAmB,EAAErG,QAAgB,KAAK;IAC1EH,kBAAkB,CAACwG,WAAW,EAAErG,QAAQ,EAAEsG,eAAe,CAAC;IAC1D/K,uBAAuB,CAAC8K,WAAW,EAAErG,QAAQ,EAAEjE,mBAAmB,CAAC;IAEnE4D,kBAAkB,CAAC;MAClB0G,WAAW,EAAE,EAAE;MACfrG,QAAQ,EAAE,EAAE;MACZiC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EACD,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpQ,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAMqQ,iBAAiB,GAAIC,GAAW,IAAK;IAC1C,IAAIJ,eAAe,CAACrE,KAAK,KAAK,UAAU,EAAE;MACzC,IAAI,CAACyE,GAAG,EAAE;QACT,OAAO,iBAAiB;MACzB;MACA,IAAI;QACH,IAAIC,GAAG,CAACD,GAAG,CAAC;QACZ,OAAO,EAAE;MACV,CAAC,CAAC,OAAOE,KAAK,EAAE;QACf,OAAO,aAAa;MACrB;IACD;IACA,OAAO,EAAE;EACV,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACR,WAAmB,EAAErG,QAAgB,KAAK;IACrE,MAAM4G,KAAK,GAAGH,iBAAiB,CAAC5K,SAAS,CAAC;IAC1C2K,iBAAiB,CAACI,KAAK,CAAC,CAAC,CAAC;;IAE1B,IAAIA,KAAK,EAAE;MACV,OAAO,CAAC;IACT;IAEA,MAAME,kBAAkB,GAAG,CAACnL,iBAAiB,IAAI,CAACA,iBAAiB,CAACoL,IAAI,CAAC,CAAC,GACvEC,gBAAgB,CAACC,KAAK,CAAC;IAAA,EACvBtL,iBAAiB;IACpBC,oBAAoB,CAACkL,kBAAkB,CAAC;IAExC1L,YAAY,CAACiL,WAAW,EAAErG,QAAQ,EAAE,OAAO,EAAEkH,UAAU,CAAC;IACxDrH,kBAAkB,CAACwG,WAAW,EAAErG,QAAQ,EAAEsG,eAAe,CAAC,CAAC,CAAC;IAC5D/K,uBAAuB,CAAC8K,WAAW,EAAErG,QAAQ,EAAEjE,mBAAmB,CAAC;IACnEX,YAAY,CAACiL,WAAW,EAAErG,QAAQ,EAAE,MAAM,EAAE8G,kBAAkB,CAAC;IAC/D1L,YAAY,CAACiL,WAAW,EAAErG,QAAQ,EAAE,SAAS,EAAEsG,eAAe,CAAC,CAAC,CAAC;IACjE3G,kBAAkB,CAAC;MAAE0G,WAAW,EAAE,EAAE;MAAErG,QAAQ,EAAE,EAAE;MAAEiC,KAAK,EAAE;IAAK,CAAC,CAAC;IAClE/E,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMiK,eAAe,GAAIC,CAAM,IAAK;IACnC,MAAMC,MAAM,GAAGD,CAAC,CAACjB,MAAM,CAAClE,KAAK;IAC7BnG,YAAY,CAACuL,MAAM,CAAC;;IAEpB;IACA,MAAMT,KAAK,GAAGH,iBAAiB,CAACY,MAAM,CAAC;IACvCb,iBAAiB,CAACI,KAAK,CAAC;IAExBxE,kBAAkB,CAAC;MAClBH,KAAK,EAAEqE,eAAe,CAACrE,KAAK;MAC5BpG,SAAS,EAAEwL,MAAM;MACjBnF,GAAG,EAAE/F;IACN,CAAC,CAAC;EACH,CAAC;EAED,MAAM6K,gBAAgB,GAAG7Q,OAAO,CAAC,MAAM;IACtC,MAAMmR,MAAM,GAAGxH,oBAAoB,CAACF,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAC;IAC1FpE,oBAAoB,CAAC0L,MAAM,CAACL,KAAK,CAAC;IAClC5G,UAAU,CAACiH,MAAM,CAACL,KAAK,CAAC;IACxBrI,SAAS,CAAC0I,MAAM,CAACrF,KAAK,CAAC;IACvB,OAAOqF,MAAM;EACd,CAAC,EAAE,CAAC1H,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAC,CAAC;EAE3D,MAAM+B,mBAAmB,GAAG;IAC3BkD,eAAe,EAAE,SAAS;IAC1BF,WAAW,EAAE,SAAS;IACtBwC,KAAK,EAAE;EACR,CAAC;EAED,MAAM,CAACjB,eAAe,EAAElE,kBAAkB,CAAC,GAAGhM,QAAQ,CAAgB;IACrE6L,KAAK,EAAE,OAAO;IAAE;IAChBpG,SAAS,EAAE,EAAE;IAAE;IACfqG,GAAG,EAAE,UAAU,CAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACgF,UAAU,EAAE/E,aAAa,CAAC,GAAG/L,QAAQ,CAAC2L,mBAAmB,CAAC;EACjE,MAAMyF,cAAc,GAAG1H,oBAAoB,CAACF,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAC;EAElG3J,SAAS,CAAC,MAAM;IACf,MAAMoR,kBAAkB,GAAGA,CAACpB,WAAgB,EAAErG,QAAa,KAAK;MAC/D,MAAMwH,cAAc,GAAG1H,oBAAoB,CAACuG,WAAW,EAAErG,QAAQ,CAAC;MAClE,IAAIwH,cAAc,EAAE;QACnB1L,YAAY,CAAC0L,cAAc,CAAC3L,SAAS,IAAI,EAAE,CAAC;QAC5CsG,aAAa,CAAC;UACb8C,eAAe,EAAEuC,cAAc,CAACE,OAAO,IAAI3F,mBAAmB,CAACkD,eAAe;UAC9EF,WAAW,EAAEyC,cAAc,CAACzC,WAAW,IAAIhD,mBAAmB,CAACgD,WAAW;UAC1EwC,KAAK,EAAEC,cAAc,CAACG,SAAS,IAAI5F,mBAAmB,CAACwF;QACxD,CAAC,CAAC;QACFnF,kBAAkB,CAAC;UAClBH,KAAK,EAAEuF,cAAc,CAAClB,eAAe,IAAI,OAAO;UAAE;UAClDzK,SAAS,EAAE2L,cAAc,CAAC3L,SAAS,IAAIA,SAAS;UAAE;UAClDqG,GAAG,EAAE,UAAU,CAAE;QAClB,CAAC,CAAC;MACH;IACD,CAAC;IACDuF,kBAAkB,CAAC7H,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAC;EAC1E,CAAC,EAAE,CAACJ,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAC,CAAC;;EAE3D;EACA,MAAM4H,iBAAiB,GAAGA,CAACR,CAAM,EAAES,UAAe,KAAK;IACtD,MAAM5F,KAAK,GAAGmF,CAAC,CAACjB,MAAM,CAAClE,KAAK;IAC5BE,aAAa,CAAE2F,IAAI,KAAM;MACxB,GAAGA,IAAI;MACP,CAACD,UAAU,GAAG5F;IACf,CAAC,CAAC,CAAC;EACJ,CAAC;EACF5L,SAAS,CAAC,MAAM;IAEd+L,kBAAkB,CAAC;MAClBH,KAAK,EAAEqE,eAAe,CAACrE,KAAK;MAAE;MAC9BpG,SAAS,EAAEA,SAAS;MAAE;MACtBqG,GAAG,EAAE,UAAU,CAAE;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN;EACA7L,SAAS,CAAC,MAAM;IACf,MAAM0R,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIxI,aAAa,CAACyI,OAAO,EAAE;QAC1B;QACAzI,aAAa,CAACyI,OAAO,CAAClG,KAAK,CAACiE,MAAM,GAAG,MAAM;QAC3C,MAAMkC,aAAa,GAAG1I,aAAa,CAACyI,OAAO,CAACE,YAAY;QACxD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpDhF,iBAAiB,CAACiF,YAAY,CAAC;;QAE/B;QACA,IAAIhF,YAAY,CAAC4E,OAAO,EAAE;UACzB;UACA,IAAI5E,YAAY,CAAC4E,OAAO,CAACK,YAAY,EAAE;YACtCjF,YAAY,CAAC4E,OAAO,CAACK,YAAY,CAAC,CAAC;UACpC;UACA;UACAC,UAAU,CAAC,MAAM;YAChB,IAAIlF,YAAY,CAAC4E,OAAO,IAAI5E,YAAY,CAAC4E,OAAO,CAACK,YAAY,EAAE;cAC9DjF,YAAY,CAAC4E,OAAO,CAACK,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDN,iBAAiB,CAAC,CAAC;IAGnB,MAAMQ,QAAQ,GAAG,CAChBD,UAAU,CAACP,iBAAiB,EAAE,EAAE,CAAC,EACjCO,UAAU,CAACP,iBAAiB,EAAE,GAAG,CAAC,EAClCO,UAAU,CAACP,iBAAiB,EAAE,GAAG,CAAC,EAClCO,UAAU,CAACP,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIS,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAIlJ,aAAa,CAACyI,OAAO,IAAIU,MAAM,CAACC,cAAc,EAAE;MACnDH,cAAc,GAAG,IAAIG,cAAc,CAAC,MAAM;QACzCL,UAAU,CAACP,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFS,cAAc,CAACI,OAAO,CAACrJ,aAAa,CAACyI,OAAO,CAAC;IAC9C;IAGA,IAAIzI,aAAa,CAACyI,OAAO,IAAIU,MAAM,CAACG,gBAAgB,EAAE;MACrDJ,gBAAgB,GAAG,IAAII,gBAAgB,CAAC,MAAM;QAC7CP,UAAU,CAACP,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFU,gBAAgB,CAACG,OAAO,CAACrJ,aAAa,CAACyI,OAAO,EAAE;QAC/Cc,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZV,QAAQ,CAACW,OAAO,CAACC,YAAY,CAAC;MAC9B,IAAIX,cAAc,EAAE;QACnBA,cAAc,CAACY,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIX,gBAAgB,EAAE;QACrBA,gBAAgB,CAACW,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAE1J,WAAW,CAAC,CAAC;EACjB,MAAM2J,mBAAmB,GAAIjC,CAAoB,IAAK;IACrD,MAAMkC,CAAoB,GAAGlC,CAAC,CAACjB,MAAM,CAAClE,KAA0B,CAAC,CAAC;IAClEG,kBAAkB,CAAC;MAClBH,KAAK,EAAEqH,CAAC;MAAE;MACVzN,SAAS,EAAEA,SAAS;MACpBqG,GAAG,EAAE/F,WAAqC,CAAE;IAC7C,CAAC,CAAC;EACH,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;;EAGA,oBACCvD,OAAA;IAAK2Q,SAAS,EAAC,gBAAgB;IAAAC,QAAA,GAC7BlK,cAAc,iBACd1G,OAAA,CAACjC,GAAG;MACH8S,EAAE,EAAE;QACH9H,QAAQ,EAAE,OAAO;QACjB8D,GAAG,EAAE,CAAC;QACND,IAAI,EAAE,CAAC;QACPhB,KAAK,EAAE,OAAO;QACduB,MAAM,EAAE,OAAO;QACfd,eAAe,EAAE,oBAAoB;QACrCW,MAAM,EAAE;MACT;IAAE;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACD,EACArJ,gBAAgB,IAAIF,WAAW,iBAC/B1B,OAAA,CAACH,UAAU;MACV6B,WAAW,EAAEA,WAAY;MACzBC,cAAc,EAAEA,cAAe;MAC/BE,WAAW,EAAEA;IAAY;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACD,eAEDjL,OAAA,CAACrC,MAAM;MACNgT,SAAS,EAAC,mBAAmB;MAC7BhM,IAAI,EAAEA,IAAK;MACXmM,OAAO,EAAE3I,WAAY;MACrB4I,UAAU,EAAElJ,YAAa;MACzBmJ,UAAU,EAAE;QACX9H,KAAK,EAAEuC;MACR,CAAE;MACFC,QAAQ,EAAE,KAAM;MAChBuF,mBAAmB,EAAE,IAAK;MAAAL,QAAA,gBAE1B5Q,OAAA;QAAKkJ,KAAK,EAAE;UAACgI,YAAY,EAAC,KAAK;UAACjF,OAAO,EAAC;QAAM,CAAE;QAAA2E,QAAA,EAC9ChN,OAAO,iBACP5D,OAAA,CAAChC,UAAU;UAAC2S,SAAS,EAAC;UACrB;UAAA;UAAAC,QAAA,eAEP5Q,OAAA,CAACL,SAAS;YAACkR,EAAE,EAAE;cAAEM,IAAI,EAAE,CAAC;cAACxC,KAAK,EAAC;YAAM;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNjL,OAAA,CAACJ,gBAAgB;QAEjBuL,GAAG,EAAEX,YAAa;QACrBtB,KAAK,EAAE;UACNkI,SAAS,EAAE,OAAO;UAClB,IAAIrN,QAAQ,GACR;YACEsN,mBAAmB,EAAE,IAAApP,cAAA,GAAGiH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4C,MAAM,cAAA7J,cAAA,cAAAA,cAAA,GAAI,CAAC,IAAI;YAC9CqP,oBAAoB,EAAE,IAAApP,cAAA,GAAGgH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4C,MAAM,cAAA5J,cAAA,cAAAA,cAAA,GAAI,CAAC,IAAI;YAC/CqP,sBAAsB,EAAE,KAAK;YAC7BC,uBAAuB,EAAE;UAC3B,CAAC,GACD;YACE3F,YAAY,EAAE,IAAA1J,cAAA,GAAG+G,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4C,MAAM,cAAA3J,cAAA,cAAAA,cAAA,GAAI,CAAC;UACrC,CAAC;QACP,CAAE;QAEEsP,OAAO,EAAE;UACRC,eAAe,EAAE,KAAK;UACtBC,eAAe,EAAE,IAAI;UACrBC,gBAAgB,EAAE,KAAK;UACvBC,WAAW,EAAE,IAAI;UACjBC,kBAAkB,EAAE,EAAE;UACtBC,kBAAkB,EAAE,IAAI;UACxBC,mBAAmB,EAAE;QACtB,CAAE;QAAApB,QAAA,eAEF5Q,OAAA,CAACpC,aAAa;UACbiT,EAAE,EAAE;YACHoB,OAAO,EAAE,CAAC;YACVC,UAAU,EAAE,MAAM;YAAE;YACpBzF,QAAQ,EAAE,QAAQ;YAClBR,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBnD,QAAQ,EAAE;UACX,CAAE;UACDoC,GAAG,EAAExE,aAAc;UAEpBgC,EAAE,EAAC,aAAa;UAAAiI,QAAA,eA0BhB5Q,OAAA,CAACjC,GAAG;YAAC8S,EAAE,EAAE;cAACoB,OAAO,EAAE,GAAG,CAAA/I,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiJ,OAAO,KAAI,EAAE;YAC/C,CAAE;YAAAvB,QAAA,GACK7L,QAAQ,CAACqN,GAAG,CAAC,CAAC1H,OAAO,EAAEzB,KAAK,kBAC5BjJ,OAAA,CAACjC,GAAG;cAEH8S,EAAE,EAAE;gBACH,GAAGnE,YAAY;gBACfS,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE;kBACVkF,cAAc,EAAEpJ,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK;kBAC3CqJ,cAAc,EAAErJ,KAAK,KAAK,CAAC,GAAG,qBAAqB,GAAG,aAAa;kBACnEsJ,cAAc,EAAEtJ,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG;gBAC1C;cACD,CAAE;cACFuJ,SAAS;cACTC,WAAW,EAAEA,CAAA,KAAM/I,eAAe,CAACT,KAAK,CAAE;cAC1CyJ,WAAW,EAAEA,CAAA,KAAM/I,eAAe,CAACV,KAAK,CAAE;cAC1C0J,SAAS,EAAE5I,aAAc;cAAA6G,QAAA,gBAEzB5Q,OAAA,CAAChC,UAAU;gBACV2S,SAAS,EAAC,WAAW;gBACrBE,EAAE,EAAElE,eAAgB;gBAAAiE,QAAA,eAEpB5Q,OAAA,CAACd,iBAAiB;kBACjB0T,QAAQ,EAAC,OAAO;kBAChB/B,EAAE,EAAE;oBAAElC,KAAK,EAAE;kBAAU;gBAAE;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,EAEZR,aAAa,CAACC,OAAO,EAAEzB,KAAK,CAAC,EAC7BA,KAAK,KAAK,CAAC,iBACXjJ,OAAA,CAACtB,OAAO;gBACP2P,KAAK,EACJxI,eAAe,CAAC,MAAM,CAAC,IAAIA,eAAe,CAAC,QAAQ,CAAC,IAAIA,eAAe,CAAC,OAAO,CAAC,GAC7EvD,SAAS,CAAC,6CAA6C,CAAC,GACxDA,SAAS,CAAC,aAAa,CAC1B;gBAAAsO,QAAA,eAED5Q,OAAA,CAAChC,UAAU;kBACV2S,SAAS,EAAC,aAAa;kBACvBkC,OAAO,EAAEvK,kBAAmB;kBAC5BuI,EAAE,EAAE;oBACHxE,eAAe,EAAE,SAAS;oBAC1B,SAAS,EAAE;sBACVA,eAAe,EAAE;oBAClB,CAAC;oBACDR,YAAY,EAAE,KAAK;oBACnBoG,OAAO,EAAE,gBAAgB;oBACzBlJ,QAAQ,EAAE,UAAU;oBACpB8D,GAAG,EAAE,MAAM;oBACXD,IAAI,EAAE,KAAK;oBACXE,SAAS,EAAE,uBAAuB;oBAClCxM,OAAO,EAAE,GAAG;oBACZyM,MAAM,EAAElH,eAAe,CAAC,MAAM,CAAC,IAAIA,eAAe,CAAC,QAAQ,CAAC,IAAIA,eAAe,CAAC,OAAO,CAAC,GACrF,aAAa,GACb;kBACJ,CAAE;kBACFiN,QAAQ,EAAEjN,eAAe,CAAC,MAAM,CAAC,IAAIA,eAAe,CAAC,QAAQ,CAAC,IAAIA,eAAe,CAAC,OAAO,CAAE;kBAAA+K,QAAA,eAE3F5Q,OAAA,CAACf,OAAO;oBACP2T,QAAQ,EAAC,OAAO;oBAChB/B,EAAE,EAAE;sBAAElC,KAAK,EAAE;oBAAO;kBAAE;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACT;YAAA,GA7DIhC,KAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8DN,CACL,CAAC,EAED8H,OAAO,CAAC/L,eAAe,CAACqC,KAAK,CAAC,gBAC9BrJ,OAAA;cACC2I,EAAE,EAAC,mBAAmB;cACtBgI,SAAS,EAAC,iCAAiC;cAAAC,QAAA,eAE3C5Q,OAAA;gBAAK2Q,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC7B5Q,OAAA;kBAAK2Q,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClC5Q,OAAA;oBAAK2Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEtO,SAAS,CAAC,YAAY;kBAAC;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DjL,OAAA,CAAChC,UAAU;oBACVgV,IAAI,EAAC,OAAO;oBACZ,cAAW,OAAO;oBAClBH,OAAO,EAAEA,CAAA,KAAMrF,uBAAuB,CAACxG,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAE;oBAAAwJ,QAAA,eAE9F5Q,OAAA,CAACL,SAAS;sBAAAmL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACNjL,OAAA;kBAAK2Q,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC5C5Q,OAAA;oBAAK2Q,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC9B5Q,OAAA,CAAC5B,WAAW;sBACX6U,SAAS;sBACTpC,EAAE,EAAE;wBAAEqC,YAAY,EAAE;sBAAO,CAAE;sBAAAtC,QAAA,gBAE3B5Q,OAAA,CAAC9B,UAAU;wBAAC2S,EAAE,EAAE;0BAAE+B,QAAQ,EAAE,MAAM;0BAAEO,UAAU,EAAE,MAAM;0BAAEC,EAAE,EAAE;wBAAM,CAAE;wBAAAxC,QAAA,EAAEtO,SAAS,CAAC,aAAa;sBAAC;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC9GjL,OAAA,CAACzB,SAAS;wBACT8K,KAAK,EAAEtG,iBAAkB;wBACzBiQ,IAAI,EAAC,OAAO;wBACXnC,EAAE,EAAE;0BACRwC,EAAE,EAAE,KAAK;0BACT9G,MAAM,EAAE,gBAAgB;0BACxBV,YAAY,EAAE,KAAK;0BAE1B,0BAA0B,EAAE;4BAC1BsB,MAAM,EAAE,MAAM;4BACd,0CAA0C,EAAE;8BAC1CZ,MAAM,EAAE;4BACV,CAAC;4BACD,gDAAgD,EAAE;8BAChDA,MAAM,EAAE;4BACV;0BACF,CAAC;0BACD,oCAAoC,EAAE;4BACpCA,MAAM,EAAE;0BACV;wBAEI,CAAE;wBACM+G,WAAW,EAAEhR,SAAS,CAAC,aAAa,CAAE;wBACxCiR,QAAQ,EAAG/E,CAAC,IAAKxL,oBAAoB,CAACwL,CAAC,CAACjB,MAAM,CAAClE,KAAK;sBAAE;wBAAAyB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,eACAjL,OAAA,CAAC9B,UAAU;wBAAC2S,EAAE,EAAE;0BAAE+B,QAAQ,EAAE,MAAM;0BAAEO,UAAU,EAAE,MAAM;0BAAEE,EAAE,EAAE;wBAAM,CAAE;wBAAAzC,QAAA,EAAEtO,SAAS,CAAC,eAAe;sBAAC;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAChHjL,OAAA,CAAC3B,MAAM;wBACNgL,KAAK,EAAEqE,eAAe,CAACrE,KAAM;wBAC7BmK,YAAY,EAAC,OAAO;wBACpBD,QAAQ,EAAE9C,mBAAoB;wBAC5BI,EAAE,EAAE;0BACTwC,EAAE,EAAE,KAAK;0BACT9G,MAAM,EAAE,gBAAgB;0BACxBV,YAAY,EAAE,KAAK;0BACnB4H,SAAS,EAAE,MAAM;0BACjB,qBAAqB,EAAE;4BACxBxB,OAAO,EAAE;0BACV,CAAC;0BAEN,0BAA0B,EAAE;4BAC1B9E,MAAM,EAAE,MAAM;4BACd,0CAA0C,EAAE;8BAC1CZ,MAAM,EAAE;4BACV,CAAC;4BACD,gDAAgD,EAAE;8BAChDA,MAAM,EAAE;4BACV;0BACF,CAAC;0BACD,oCAAoC,EAAE;4BACpCA,MAAM,EAAE;0BACV;wBAEI,CAAE;wBAAAqE,QAAA,gBAEM5Q,OAAA,CAAC1B,QAAQ;0BAAC+K,KAAK,EAAC,OAAO;0BAAAuH,QAAA,EAAEtO,SAAS,CAAC,OAAO;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eACvDjL,OAAA,CAAC1B,QAAQ;0BAAC+K,KAAK,EAAC,UAAU;0BAAAuH,QAAA,EAAEtO,SAAS,CAAC,UAAU;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eAC7DjL,OAAA,CAAC1B,QAAQ;0BAAC+K,KAAK,EAAC,UAAU;0BAAAuH,QAAA,EAAEtO,SAAS,CAAC,UAAU;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eAC7DjL,OAAA,CAAC1B,QAAQ;0BAAC+K,KAAK,EAAC,MAAM;0BAAAuH,QAAA,EAAEtO,SAAS,CAAC,MAAM;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC,eACrDjL,OAAA,CAAC1B,QAAQ;0BAAC+K,KAAK,EAAC,SAAS;0BAAAuH,QAAA,EAAEtO,SAAS,CAAC,SAAS;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,EACRyC,eAAe,CAACrE,KAAK,KAAK,UAAU,gBACpCrJ,OAAA,CAAAE,SAAA;wBAAA0Q,QAAA,gBACG5Q,OAAA,CAAC9B,UAAU;0BAAC2S,EAAE,EAAE;4BAAE+B,QAAQ,EAAE,MAAM;4BAAEO,UAAU,EAAE,MAAM;4BAAEC,EAAE,EAAE;0BAAM,CAAE;0BAAAxC,QAAA,EAAEtO,SAAS,CAAC,WAAW;wBAAC;0BAAAwI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAC5GjL,OAAA,CAACzB,SAAS;0BACT8K,KAAK,EAAEpG,SAAU;0BACjB+P,IAAI,EAAC,OAAO;0BACZM,WAAW,EAAC,mBAAmB;0BAC/BC,QAAQ,EAAG/E,CAAC,IAAK;4BAChB,MAAMC,MAAM,GAAGD,CAAC,CAACjB,MAAM,CAAClE,KAAK;4BAC7BnG,YAAY,CAACuL,MAAM,CAAC,CAAC,CAAC;4BACtBF,eAAe,CAACC,CAAC,CAAC,CAAC,CAAC;0BACpB,CAAE;0BACFR,KAAK,EAAE,CAAC,CAACL,cAAe;0BACxB+F,UAAU,EAAE/F;wBAAe;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC,eAEFjL,OAAA,CAACvB,iBAAiB;0BACjB4K,KAAK,EAAE9F,WAAY;0BACnBgQ,QAAQ,EAAEjG,gBAAiB;0BAC3BqG,SAAS;0BACP,cAAYrR,SAAS,CAAC,aAAa,CAAE;0BACvCuO,EAAE,EAAE;4BACH+C,GAAG,EAAE,KAAK;4BACVC,OAAO,EAAE,KAAK;4BACd1G,MAAM,EAAE;0BACT,CAAE;0BAAAyD,QAAA,EAED,CAAC,SAAS,EAAE,UAAU,CAAC,CAACwB,GAAG,CAAE9I,GAAG,IAAK;4BACrC,oBACCtJ,OAAA,CAACxB,YAAY;8BACZ6K,KAAK,EAAEC,GAAI;8BACX,cAAW,SAAS;8BACpBuH,EAAE,EAAE;gCACHtE,MAAM,EAAE,mBAAmB;gCAC3BuH,aAAa,EAAE,YAAY;gCAC3BnF,KAAK,EAAE,MAAM;gCACb9C,YAAY,EAAE,KAAK;gCACnBkI,IAAI,EAAE,CAAC;gCACP9B,OAAO,EAAE,cAAc;gCAEvB,gBAAgB,EAAE;kCACjB5F,eAAe,EAAE,qBAAqB;kCACtCsC,KAAK,EAAE,MAAM;kCACbpC,MAAM,EAAE;gCACT,CAAC;gCACD,SAAS,EAAE;kCACVF,eAAe,EAAE;gCAClB,CAAC;gCACD,cAAc,EAAE;kCACf2H,UAAU,EAAE,0CAA0C,CAAE;gCACzD;8BACD,CAAE;8BAAApD,QAAA,EAEDtH;4BAAG;8BAAAwB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACS,CAAC;0BAEjB,CAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACgB,CAAC;sBAAA,eACnB,CAAC,GACA,IAAI;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyCI,CAAC,eAGdjL,OAAA,CAACjC,GAAG;sBACH4S,SAAS,EAAC,mBAAmB;sBAC7BE,EAAE,EAAE;wBAAEhF,YAAY,EAAE;sBAAM,CAAE;sBAAA+E,QAAA,gBAE1B5Q,OAAA,CAAC9B,UAAU;wBAACyS,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAAEtO,SAAS,CAAC,YAAY;sBAAC;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eACpFjL,OAAA;wBACCiF,IAAI,EAAC,OAAO;wBACZoE,KAAK,EAAEiF,UAAU,CAACjC,eAAgB;wBAClCkH,QAAQ,EAAG/E,CAAC,IAAKQ,iBAAiB,CAACR,CAAC,EAAE,iBAAiB,CAAE;wBACzDmC,SAAS,EAAC;sBAAmB;wBAAA7F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENjL,OAAA,CAACjC,GAAG;sBACH4S,SAAS,EAAC,mBAAmB;sBAC7BE,EAAE,EAAE;wBAAEhF,YAAY,EAAE;sBAAM,CAAE;sBAAA+E,QAAA,gBAE1B5Q,OAAA,CAAC9B,UAAU;wBAACyS,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAAEtO,SAAS,CAAC,QAAQ;sBAAC;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAChFjL,OAAA;wBACCiF,IAAI,EAAC,OAAO;wBACZoE,KAAK,EAAEiF,UAAU,CAACnC,WAAY;wBAC9BoH,QAAQ,EAAG/E,CAAC,IAAKQ,iBAAiB,CAACR,CAAC,EAAE,aAAa,CAAE;wBACrDmC,SAAS,EAAC;sBAAmB;wBAAA7F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENjL,OAAA,CAACjC,GAAG;sBACH4S,SAAS,EAAC,mBAAmB;sBAC7BE,EAAE,EAAE;wBAAEhF,YAAY,EAAE;sBAAM,CAAE;sBAAA+E,QAAA,gBAE1B5Q,OAAA,CAAC9B,UAAU;wBAACyS,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAAEtO,SAAS,CAAC,MAAM;sBAAC;wBAAAwI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC9EjL,OAAA;wBACCiF,IAAI,EAAC,OAAO;wBACZoE,KAAK,EAAEiF,UAAU,CAACK,KAAM;wBACxB4E,QAAQ,EAAG/E,CAAC,IAAKQ,iBAAiB,CAACR,CAAC,EAAE,OAAO,CAAE;wBAC/CmC,SAAS,EAAC;sBAAmB;wBAAA7F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAEPjL,OAAA;kBAAK2Q,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eAClC5Q,OAAA,CAAC7B,MAAM;oBACN8V,OAAO,EAAC,WAAW;oBACnBpB,OAAO,EAAEA,CAAA,KAAM5E,kBAAkB,CAACjH,eAAe,CAACyG,WAAW,EAAEzG,eAAe,CAACI,QAAQ,CAAE;oBACzFuJ,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAEnBtO,SAAS,CAAC,OAAO;kBAAC;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,GACH,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGS;MAAC,GAtXZ,aAAaX,cAAc,EAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuXhB,CAAC,eACnBjL,OAAA,CAACjC,GAAG;QAAC8S,EAAE,EAAE;UAAGhF,YAAY,EAAE,IAAAzJ,cAAA,GAAG8G,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4C,MAAM,cAAA1J,cAAA,cAAAA,cAAA,GAAI,CAAC;QACnD,CAAE;QAAAwO,QAAA,EACG7M,QAAQ,KACPD,cAAc,KAAK,CAAC,IAAIA,cAAc,KAAK,EAAE,gBAC7C9D,OAAA,CAACkU,WAAW;UACXC,UAAU,EAAErN,WAAY;UACxB9C,KAAK,EAAEA,KAAK,CAACgE,MAAO;UACpBzD,aAAa,EAAIA;QAAc;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,GAEDnH,cAAc,KAAK,CAAC,gBACrB9D,OAAA;UAAA4Q,QAAA,eACC5Q,OAAA,CAACrB,cAAc;YACdsV,OAAO,EAAC,aAAa;YACpBpD,EAAE,EAAE;cACH1D,MAAM,EAAE,KAAK;cACbtB,YAAY,EAAE,MAAM;cACpBuI,MAAM,EAAE,UAAU;cAClB/H,eAAe,EAAEjM,SAAS,CAACmE,aAAa,EAAE,IAAI,CAAC;cAC/C,0BAA0B,EAAE;gBAC7B8H,eAAe,EAAE9H,aAAa,CAAE;cAC/B;YAAE,CAAE;YACN8E,KAAK,EAAGvC,WAAW,GAAG9C,KAAK,CAACgE,MAAM,GAAI;UAAI;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,GACHnH,cAAc,KAAK,CAAC,gBACvB9D,OAAA;UAAKkJ,KAAK,EAAE;YAAE+I,OAAO,EAAE;UAAM,CAAE;UAAArB,QAAA,eAC9B5Q,OAAA,CAACqU,iBAAiB;YAClBF,UAAU,EAAErN,WAAY;YACxB9C,KAAK,EAAEA,KAAK,CAACgE,MAAO;YACpBzD,aAAa,EAAEA;UAAc;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,GACJnH,cAAc,KAAK,CAAC,gBACtB9D,OAAA,CAACnB,WAAW;UACX,cAAW,YAAY;UACvBgS,EAAE,EAAE;YAAEoB,OAAO,EAAE;UAAM,CAAE;UAAArB,QAAA,eAEvB5Q,OAAA,CAAC9B,UAAU;YAAA0S,QAAA,GACV,OACK,EAAC9J,WAAW,EAAC,MAAI,EAAC9C,KAAK,CAACgE,MAAM;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,GACT,IAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACNpG,QAAQ,iBACR7E,OAAA,CAAC/B,OAAO;QACD0S,SAAS,EAAC,eAAe;QAC/BhM,IAAI,EAAEoO,OAAO,CAAClO,QAAQ,CAAE;QACxBA,QAAQ,EAAEA,QAAS;QACnBiM,OAAO,EAAErI,kBAAmB;QAC5B6L,YAAY,EAAE;UACbC,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACb,CAAE;QACFC,eAAe,EAAE;UAChBF,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACb,CAAE;QACFE,SAAS,EAAE;UACVC,KAAK,EAAE;YACN9D,EAAE,EAAE;cACHoB,OAAO,EAAE,MAAM;cACfhG,OAAO,EAAE,MAAM;cACf2H,GAAG,EAAE,MAAM;cACXhI,KAAK,EAAE,MAAM;cACboB,MAAM,EAAE;YACT;UACD;QACD,CAAE;QACF6D,EAAE,EAAE;UACH9H,QAAQ,EAAE,UAAU;UACpB;UACA;UACA+D,SAAS,EAAE;QACZ,CAAE;QAAA8D,QAAA,eAEF5Q,OAAA,CAACjC,GAAG;UACHkO,OAAO,EAAC,MAAM;UACdC,aAAa,EAAC,KAAK;UACnB0H,GAAG,EAAC,MAAM;UAAAhD,QAAA,gBAGV5Q,OAAA,CAACtB,OAAO;YACP2P,KAAK,EAAExI,eAAe,CAAC,MAAM,CAAC,GAAGvD,SAAS,CAAC,+CAA+C,CAAC,GAAG,EAAG;YACjGsS,WAAW,EAAE;cACZ/D,EAAE,EAAE;gBACH7D,MAAM,EAAE;cACT;YACD,CAAE;YAAA4D,QAAA,eAEF5Q,OAAA,CAACjC,GAAG;cACHkO,OAAO,EAAC,MAAM;cACdC,aAAa,EAAC,QAAQ;cACtBkB,UAAU,EAAC,QAAQ;cACnByD,EAAE,EAAE;gBACH9D,MAAM,EAAElH,eAAe,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG,SAAS;gBAC3DvF,OAAO,EAAEuF,eAAe,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;gBAC1CgP,GAAG,EAAE;kBACJjC,QAAQ,EAAE;gBACX;cACD,CAAE;cACFC,OAAO,EAAEA,CAAA,KAAM,CAAChN,eAAe,CAAC,MAAM,CAAC,IAAI6C,gBAAgB,CAAC,MAAM,CAAE;cAAAkI,QAAA,gBAEpE5Q,OAAA,CAACZ,UAAU;gBAAA0L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACdjL,OAAA,CAAC9B,UAAU;gBAAC+V,OAAO,EAAC,SAAS;gBAC5BpD,EAAE,EAAE;kBACH+B,QAAQ,EAAE;gBACX,CAAE;gBAAAhC,QAAA,EACDtO,SAAS,CAAC,WAAW;cAAC;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGVjL,OAAA,CAACtB,OAAO;YACP2P,KAAK,EAAExI,eAAe,CAAC,QAAQ,CAAC,GAAGvD,SAAS,CAAC,4CAA4C,CAAC,GAAG,EAAG;YAChGsS,WAAW,EAAE;cACZ/D,EAAE,EAAE;gBACH7D,MAAM,EAAE;cACT;YACD,CAAE;YAAA4D,QAAA,eAEF5Q,OAAA,CAACjC,GAAG;cACHkO,OAAO,EAAC,MAAM;cACdC,aAAa,EAAC,QAAQ;cACtBkB,UAAU,EAAC,QAAQ;cACnByD,EAAE,EAAE;gBACH9D,MAAM,EAAElH,eAAe,CAAC,QAAQ,CAAC,GAAG,aAAa,GAAG,SAAS;gBAC7DvF,OAAO,EAAEuF,eAAe,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;gBAC5CgP,GAAG,EAAE;kBACJjC,QAAQ,EAAE;gBACX;cACD,CAAE;cACFC,OAAO,EAAEA,CAAA,KAAM,CAAChN,eAAe,CAAC,QAAQ,CAAC,IAAI6C,gBAAgB,CAAC,QAAQ,CAAE;cAAAkI,QAAA,gBAExE5Q,OAAA,CAACT,IAAI;gBAAAuL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACRjL,OAAA,CAAC9B,UAAU;gBAAC+V,OAAO,EAAC,SAAS;gBAC5BpD,EAAE,EAAE;kBACH+B,QAAQ,EAAE;gBACX,CAAE;gBAAAhC,QAAA,EACDtO,SAAS,CAAC,QAAQ;cAAC;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGVjL,OAAA,CAACtB,OAAO;YACP2P,KAAK,EAAExI,eAAe,CAAC,OAAO,CAAC,GAAGvD,SAAS,CAAC,2CAA2C,CAAC,GAAG,EAAG;YAC9FsS,WAAW,EAAE;cACZ/D,EAAE,EAAE;gBACH7D,MAAM,EAAE;cACT;YACD,CAAE;YAAA4D,QAAA,eAEF5Q,OAAA,CAACjC,GAAG;cACHkO,OAAO,EAAC,MAAM;cACdC,aAAa,EAAC,QAAQ;cACtBkB,UAAU,EAAC,QAAQ;cACnByD,EAAE,EAAE;gBACH9D,MAAM,EAAElH,eAAe,CAAC,OAAO,CAAC,GAAG,aAAa,GAAG,SAAS;gBAC5DvF,OAAO,EAAEuF,eAAe,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;gBAC3CgP,GAAG,EAAE;kBACJjC,QAAQ,EAAE;gBACX;cACD,CAAE;cACFC,OAAO,EAAEA,CAAA,KAAM,CAAChN,eAAe,CAAC,OAAO,CAAC,IAAI6C,gBAAgB,CAAC,OAAO,CAAE;cAAAkI,QAAA,gBAEtE5Q,OAAA,CAACb,KAAK;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTjL,OAAA,CAAC9B,UAAU;gBAAC+V,OAAO,EAAC,SAAS;gBAC5BpD,EAAE,EAAE;kBACH+B,QAAQ,EAAE;gBACX,CAAE;gBAAAhC,QAAA,EACDtO,SAAS,CAAC,OAAO;cAAC;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACVjL,OAAA,CAACtB,OAAO;YACP2P,KAAK,EAAE/L,SAAS,CAAC,aAAa,CAAE;YAChCsS,WAAW,EAAE;cACZ/D,EAAE,EAAE;gBACH7D,MAAM,EAAE;cACT;YACD,CAAE;YAAA4D,QAAA,eAEF5Q,OAAA;cAAA4Q,QAAA,eACC5Q,OAAA,CAACjC,GAAG;gBACHkO,OAAO,EAAC,MAAM;gBACdC,aAAa,EAAC,QAAQ;gBACtBkB,UAAU,EAAC,QAAQ;gBACnByD,EAAE,EAAE;kBACH9D,MAAM,EAAE,SAAS;kBACjBzM,OAAO,EAAE,GAAG;kBACZuU,GAAG,EAAE;oBACJjC,QAAQ,EAAE;kBACX;gBACD;gBACA;gBAAA;gBAAAhC,QAAA,gBAEA5Q,OAAA,CAACV,YAAY;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChBjL,OAAA,CAAC9B,UAAU;kBAAC+V,OAAO,EAAC,SAAS;kBAC5BpD,EAAE,EAAE;oBACH+B,QAAQ,EAAE;kBACX,CAAE;kBAAAhC,QAAA,EACDtO,SAAS,CAAC,OAAO;gBAAC;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAWVjL,OAAA,CAACtB,OAAO;YACP2P,KAAK,EAAE/L,SAAS,CAAC,aAAa,CAAE;YAChCsS,WAAW,EAAE;cACZ/D,EAAE,EAAE;gBACH7D,MAAM,EAAE;cACT;YACD,CAAE;YAAA4D,QAAA,eAEF5Q,OAAA;cAAA4Q,QAAA,eACC5Q,OAAA,CAACjC,GAAG;gBACHkO,OAAO,EAAC,MAAM;gBACdC,aAAa,EAAC,QAAQ;gBACtBkB,UAAU,EAAC,QAAQ;gBACnByD,EAAE,EAAE;kBACH9D,MAAM,EAAE,SAAS;kBACjBzM,OAAO,EAAE,GAAG;kBACZuU,GAAG,EAAE;oBACJjC,QAAQ,EAAE;kBACX;gBACD,CAAE;gBACFC,OAAO,EAAEA,CAAA,KAAMnK,gBAAgB,CAAC,MAAM,CAAE;gBAAAkI,QAAA,gBAExC5Q,OAAA,CAACX,IAAI;kBAAAyL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACRjL,OAAA,CAAC9B,UAAU;kBAAC+V,OAAO,EAAC,SAAS;kBAC5BpD,EAAE,EAAE;oBACH+B,QAAQ,EAAE;kBACX,CAAE;kBAAAhC,QAAA,EACDtO,SAAS,CAAC,MAAM;gBAAC;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAER,CAAC;AAACnJ,EAAA,CAlxCIjB,UAAU;EAAA,QAkBUf,cAAc,EAqCnCJ,cAAc,EA8CKA,cAAc,EAqBjCA,cAAc,EAEJ5B,QAAQ,EACDD,aAAa;AAAA;AAAAiX,EAAA,GA7H7BjU,UAAU;AAmxChB,eAAeA,UAAU;AACzB,MAAMqT,WAAW,GAAGA,CAAC;EAAElQ,KAAK;EAAEmQ,UAAU;EAAE5P;AAAwE,CAAC,KAAK;EACvH,oBACCvE,OAAA,CAACpB,aAAa;IACbqV,OAAO,EAAC,MAAM;IACdjQ,KAAK,EAAEA,KAAM;IACb+E,QAAQ,EAAC,QAAQ;IACjBoL,UAAU,EAAEA,UAAU,GAAG,CAAE;IAC3BtD,EAAE,EAAE;MAAEkE,QAAQ,EAAE,CAAC;MAAE9I,OAAO,EAAE,MAAM;MAAEoB,cAAc,EAAE,QAAQ;MAAC2H,UAAU,EAAC,SAAS;MAAC,+BAA+B,EAAE;QAClH3I,eAAe,EAAE9H,aAAa,CAAE;MAC/B;IAAE,CAAE;IACN0Q,UAAU,eAAEjV,OAAA,CAAAE,SAAA,mBAAI,CAAE;IAClBgV,UAAU,eAAElV,OAAA,CAAAE,SAAA,mBAAI;EAAE;IAAA4K,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAElB,CAAC;AAEJ,CAAC;AAACkK,GAAA,GAfIjB,WAAW;AAgBjB,MAAMG,iBAAiB,GAAGA,CAAC;EAAErQ,KAAK;EAAEmQ,UAAU;EAAC5P;AAAwE,CAAC,KAAK;EAC5H,oBACCvE,OAAA,CAACjC,GAAG;IAAC8S,EAAE,EAAE;MAAEkE,QAAQ,EAAE;IAAE,CAAE;IAAAnE,QAAA,eAEvB5Q,OAAA,CAACjC,GAAG;MACL8S,EAAE,EAAE;QACF5E,OAAO,EAAE,MAAM;QACfoB,cAAc,EAAE,QAAQ;QACxBuG,GAAG,EAAE,KAAK,CAAE;QACd;MACA,CAAE;MAAAhD,QAAA,EAEDwE,KAAK,CAACC,IAAI,CAAC;QAAErN,MAAM,EAAEhE;MAAM,CAAC,CAAC,CAACoO,GAAG,CAAC,CAACkD,CAAC,EAAErM,KAAK,kBAC1CjJ,OAAA;QAEDkJ,KAAK,EAAE;UACL0C,KAAK,EAAE,MAAM;UACbuB,MAAM,EAAE,KAAK;UACbd,eAAe,EAAEpD,KAAK,KAAKkL,UAAU,GAAG,CAAC,GAAG5P,aAAa,GAAGnE,SAAS,CAACmE,aAAa,EAAE,IAAI,CAAC;UAAE;UAC5FsH,YAAY,EAAE;QAChB;MAAE,GANG5C,KAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOR,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAER,CAAC;AAACsK,GAAA,GA1BIlB,iBAAiB;AAAA,IAAAS,EAAA,EAAAK,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}