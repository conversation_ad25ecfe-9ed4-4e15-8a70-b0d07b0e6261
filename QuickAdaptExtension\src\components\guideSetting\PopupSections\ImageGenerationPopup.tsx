import React, { useContext, useState, useEffect } from 'react';
import { closeIcon, magicPen } from '../../../assets/icons/icons';
import { Box,Typography } from '@mui/material';
import { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';
import { AccountContext } from '../../login/AccountContext';
import { FileUpload } from '../../../models/FileUpload';
import { timeStamp } from 'console';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '../guideList/SnackbarContext';
import useDrawerStore  from '../../../store/drawerStore';





const ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {
  //const [isOpen, setIsOpen] = useState(false);

  //const togglePopup = () => setIsOpen(!isOpen);
  const { t: translate } = useTranslation();
  const { openSnackbar } = useSnackbar();

  // Get imageAnchorEl from store to know which image to replace
  const { imageAnchorEl } = useDrawerStore((state) => state);

  const [isHovered, setIsHovered] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [popupPosition, setPopupPosition] = useState<{
    position: 'external' | 'internal';
    top: number;
    left: number;
    right?: number;
  }>({
    position: 'external',
    top: 55,
    left: 0,
    right: 0
  });


const [selectedStyle, setSelectedStyle] = useState('Professional');
  const [selectedColor, setSelectedColor] = useState('Black & White');
  const [selectedRatio, setSelectedRatio] = useState('16:9');

  const { accountId } = useContext(AccountContext);

  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];
  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];
  const ratios = ['1:1', '16:9','9:16'];
  const [description, setDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);


  // Function to get guidepopup position and dimensions
  const getGuidePopupPosition = () => {
    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||
                    document.getElementById('guide-popup');
    if (element) {
      const rect = element.getBoundingClientRect();
      return {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      };
    }
    return null;
  };

  // Function to calculate adaptive positioning
  const calculatePosition = () => {
    const guidePopupPos = getGuidePopupPosition();
    if (!guidePopupPos) return;

    const viewportWidth = window.innerWidth;
    const popupWidth = 280; // ImageGenerationPopup width
    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup

    // Calculate if there's enough space for external positioning
    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;
    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20; // 20px margin from viewport edge

    if (hasSpaceForExternal) {
      // External positioning: to the right of guidepopup with gap
      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;
      setPopupPosition({
        position: 'external',
        top: 55, // Keep consistent with current top positioning
        left: Math.min(externalLeft, viewportWidth - popupWidth - 10), // Ensure it doesn't overflow viewport
      });
    } else {
      // Internal positioning: align to the right side within guidepopup
      const internalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15; // 15px padding from right edge
      setPopupPosition({
        position: 'internal',
        top: Math.max(55, guidePopupPos.top), // Align with guidepopup top or maintain minimum top
        left: Math.max(guidePopupPos.left + 15, internalLeft), // Ensure it doesn't go beyond left edge with 15px padding
      });
    }
  };

  // Update positioning when popup opens or guidepopup changes
  useEffect(() => {
    if (openGenAiImagePopup) {
      calculatePosition();

      // Set up listeners for responsive behavior
      const handleResize = () => calculatePosition();
      const handleScroll = () => calculatePosition();

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);

      // Observer for guidepopup changes
      const guidePopupElement = document.querySelector('.qadpt-guide-popup .MuiDialog-paper');
      let resizeObserver: ResizeObserver | null = null;
      let mutationObserver: MutationObserver | null = null;

      if (guidePopupElement && window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          setTimeout(calculatePosition, 50);
        });
        resizeObserver.observe(guidePopupElement);
      }

      if (guidePopupElement && window.MutationObserver) {
        mutationObserver = new MutationObserver(() => {
          setTimeout(calculatePosition, 50);
        });
        mutationObserver.observe(guidePopupElement, {
          attributes: true,
          attributeFilter: ['style', 'class']
        });
      }

      // Cleanup function
      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
        if (resizeObserver) resizeObserver.disconnect();
        if (mutationObserver) mutationObserver.disconnect();
      };
    }
  }, [openGenAiImagePopup]);

  const handleClose = () => {
  setOpenGenAiImagePopup(false);
  };

  const onImageGenerated = (imageUrl: any) => {
    
    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()

    let file: any = {
      Url: imageUrl,
      FileName: `Generated Image ${Date.now()}`,
      IsAiGenerated: true,
    };
    

    handleImageUploadFormApp(file);
    setOpenGenAiImagePopup(false);

  }

const handleEnhanceDescription = async () => {
  if (description.trim() === "") {
    openSnackbar("Please enter a description first.", "error");
    return;
  }

  setIsEnhancing(true);
  try {
    const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);
    if (enhancedPrompt) {
      setDescription(enhancedPrompt);
    }
  } catch (error) {
    console.error("Error enhancing description:", error);
  } finally {
    setIsEnhancing(false);
  }
};

const GenerateImage = () => {
  if (description === "") return;

  // Validate that we have the correct image context
  // containerId is required, but buttonId can be empty for new image generation
  if (!imageAnchorEl.containerId) {
    openSnackbar("Error: Unable to identify which image container to use. Please try again.", "error");
    return;
  }

  setIsGenerating(true); // Start loader
    const controller = new AbortController();
  setAbortController(controller);

const userPromptWithSelectedOptions = `User Asked: ${description} ${
  selectedStyle !== "" ? " and Image Should be in the Style " + selectedStyle : ""
} ${
  selectedColor !== "" ? " and Image Should be in Color Palette " + selectedColor : ""
} ${
  selectedRatio !== "" ? " and Image Should be in Aspect Ratio " + selectedRatio : ""
}`;


  GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {
    onImageGenerated(imageUrl);
    setIsGenerating(false); // Stop loader
    setAbortController(null);
  }, openSnackbar, controller.signal);
};

    const blackMagicPen = magicPen.replace(/stroke="white"/g, 'stroke="black"');
  

  return (
    <>
      <div
        className={`image-generation-popup ${popupPosition.position}`}
        style={{
          position: 'fixed',
          top: `${popupPosition.top}px`,
          height: 'calc(100vh - 55px)',
          width: '280px',
          left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',
          backgroundColor: '#fff',
          boxShadow: popupPosition.position === 'external'
            ? '-2px 0 8px rgba(0, 0, 0, 0.2)'
            : '0 4px 12px rgba(0, 0, 0, 0.15)',
          transition: 'left 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          zIndex: popupPosition.position === 'external' ? 1000 : 1001,
          padding: '12px',
          alignItems: "end",
          borderRadius: popupPosition.position === 'internal' ? '8px' : '0',
          maxHeight: popupPosition.position === 'internal' ? '500px' : 'calc(100vh - 55px)',
          border: popupPosition.position === 'internal' ? '1px solid rgba(95, 158, 160, 0.2)' : 'none',
        }}
          >
              <Box sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  
              }}
              >
                  <Typography sx={{fontSize: "18px !important"}}>
            
            {translate("Generate Images")}
                  </Typography>
                
                
                    <span style={{ cursor: 'pointer' }} onClick={handleClose} dangerouslySetInnerHTML={{ __html: closeIcon }}/>
              </Box>
              

        <div style={{ position: 'relative', width: '93%', marginTop: '10px' }}>
  <textarea
    rows={3}
    placeholder={translate("Please describe your image...")}
    value={description}
    onChange={(e) => setDescription(e.target.value)}
    style={{
      width: '93%',
      height: '93px',
      padding: '16px', // Extra right padding for button space
      fontSize: '12px',
      borderRadius: '6px',
      border: '1px solid #ccc',
      resize: 'none',
    }}
  />
  <button
    onClick={handleEnhanceDescription}
    disabled={isEnhancing || description.trim() === ""}
    style={{
      position: 'absolute',
      top: '75%',
      left: '10px',
      transform: 'translateY(-50%)',
      backgroundColor: isEnhancing || description.trim() === "" ? '#aaa9a9ff' : '#aaa9a9ff',
      color: 'black',
      border: 'none',
      padding: '4px 10px',
      borderRadius: '100px',
      fontSize: '10px',
      cursor: isEnhancing || description.trim() === "" ? 'not-allowed' : 'pointer',
      whiteSpace: 'nowrap',
      alignItems: 'center',
      display: 'flex',
      gap: '6px',
      opacity: isEnhancing || description.trim() === "" ? '0.3' : '0.5',
    }}
          >
    {isEnhancing ? (
      <div
        style={{
          width: '10px',
          height: '10px',
          border: '1px solid #333',
          borderTop: '1px solid transparent',
          borderRadius: '50%',
          animation: 'spin 0.8s linear infinite',
        }}
      />
    ) : (
      <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} />
    )}

    {isEnhancing ? translate("Enhancing...") : translate("Enhance with AI")}
  </button>
</div>


              {/* You can add form, preview, upload, etc. inside here */}
              
              <div >
        <h2 > {translate("Style")}</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }} >
          {styles.map((style) => (
            <button
              key={style}
              onClick={() => setSelectedStyle(style)}
                    style={{
                      padding: '5px 12px',
                      borderRadius: '8px',
                      border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',
                      backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',
                      color: '#333',
                      cursor: 'pointer',
                      fontSize: '11px',
                    }}
            >
               {translate(`${style}`)}
            </button>
          ))}
        </div>
              </div>
              
              <div >
        <h2 > {translate("Colors")}</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>
          {colors.map((color) => (
            <button
              key={color}
              onClick={() => setSelectedColor(color)}

                    style={{
                      padding: '5px 12px',
                      borderRadius: '8px',
                      border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',
                      backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',
                      color: '#333',
                      cursor: 'pointer',
                      fontSize: '11px',
                    }}
              
            >
               {translate(`${color}`)}
            </button>
          ))}
        </div>
        </div>
        <div>
  <h2>{translate("Ratio")}</h2>
  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>
{ratios.map((ratio) => (
  <button
    key={ratio}
    onClick={() => setSelectedRatio(ratio)}
    style={{
      padding: '5px 12px',
      borderRadius: '8px',
      border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',
      backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',
      color: '#333',
      cursor: 'pointer',
      fontSize: '11px',
    }}
  >
    {ratio}
  </button>
))}

  </div>
</div>

<button
  onClick={() => {
    if (isGenerating) {
      // Cancel if generating
      abortController?.abort();
      setIsGenerating(false);
      setAbortController(null);
    } else if (description.trim() !== '') {
      // Don't call setReplaceImage(true) here as it's already set when popup opens
      // and we need to maintain the correct imageAnchorEl context
      GenerateImage();
    }
  }}
  onMouseEnter={() => setIsHovered(true)}
  onMouseLeave={() => setIsHovered(false)}
  disabled={isGenerating && !abortController}
  style={{
    marginTop: '20px',
    width: '100%',
    padding: '10px 16px',
    borderRadius: '8px',
    backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',
    color: '#fff',
    border: 'none',
    fontSize: '14px',
    cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    transition: 'background-color 0.3s',
    opacity: description.trim() === '' ? 0.7 : 1,
  }}
>
  {isGenerating ? (
    <>
      <div
        style={{
          width: '14px',
          height: '14px',
          border: '2px solid #fff',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 0.8s linear infinite',
        }}
      />
      {isHovered ? translate('Cancel Generation') : translate('Generating...')}
    </>
  ) : (
    <>
      <span dangerouslySetInnerHTML={{ __html: magicPen }} />
      {translate('Generate Image')}
    </>
  )}
</button>


          </div>

          {/* CSS for smooth animations */}
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }

              /* Smooth transition for position changes */
              .image-generation-popup {
                transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                           box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                           border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              }

              /* Visual indicator for internal positioning */
              .image-generation-popup.internal {
                border: 1px solid rgba(95, 158, 160, 0.2);
              }
            `}
          </style>
    </>
  );
};

export default ImageGenerationPopup;
