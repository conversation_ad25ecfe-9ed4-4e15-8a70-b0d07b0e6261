{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\ImageGenerationPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState, useEffect } from 'react';\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\nimport { Box, Typography } from '@mui/material';\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\nimport { AccountContext } from '../../login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { useSnackbar } from '../guideList/SnackbarContext';\nimport useDrawerStore from '../../../store/drawerStore';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageGenerationPopup = ({\n  openGenAiImagePopup,\n  setOpenGenAiImagePopup,\n  handleImageUploadFormApp,\n  setReplaceImage\n}) => {\n  _s();\n  //const [isOpen, setIsOpen] = useState(false);\n\n  //const togglePopup = () => setIsOpen(!isOpen);\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    openSnackbar\n  } = useSnackbar();\n\n  // Get imageAnchorEl from store to know which image to replace\n  const {\n    imageAnchorEl\n  } = useDrawerStore(state => state);\n  const [isHovered, setIsHovered] = useState(false);\n  const [abortController, setAbortController] = useState(null);\n  const [popupPosition, setPopupPosition] = useState({\n    top: 55,\n    left: 0\n  });\n  const [selectedStyle, setSelectedStyle] = useState('Professional');\n  const [selectedColor, setSelectedColor] = useState('Black & White');\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\n  const ratios = ['1:1', '16:9', '9:16'];\n  const [description, setDescription] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [isEnhancing, setIsEnhancing] = useState(false);\n\n  // Function to get guidepopup position and dimensions\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n\n  // Function to calculate adaptive positioning\n  const calculatePosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return;\n    const viewportWidth = window.innerWidth;\n    const popupWidth = 260; // ImageGenerationPopup width\n    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup\n\n    // Calculate if there's enough space for external positioning\n    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;\n    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20; // 20px margin from viewport edge\n\n    if (hasSpaceForExternal) {\n      // External positioning: to the right of guidepopup with gap\n      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;\n      setPopupPosition({\n        top: 55,\n        // Keep consistent with current top positioning\n        left: Math.min(externalLeft, viewportWidth - popupWidth - 10) // Ensure it doesn't overflow viewport\n      });\n    } else {\n      // Internal positioning: align to the right side within guidepopup\n      const internalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15; // 15px padding from right edge\n      setPopupPosition({\n        top: Math.max(55, guidePopupPos.top),\n        // Align with guidepopup top or maintain minimum top\n        left: Math.max(guidePopupPos.left + 15, internalLeft) // Ensure it doesn't go beyond left edge with 15px padding\n      });\n    }\n  };\n\n  // Update positioning when popup opens\n  useEffect(() => {\n    if (openGenAiImagePopup) {\n      calculatePosition();\n\n      // Set up listeners for responsive behavior\n      const handleResize = () => calculatePosition();\n      window.addEventListener('resize', handleResize);\n\n      // Cleanup function\n      return () => {\n        window.removeEventListener('resize', handleResize);\n      };\n    }\n  }, [openGenAiImagePopup]);\n  const handleClose = () => {\n    setOpenGenAiImagePopup(false);\n  };\n  const onImageGenerated = imageUrl => {\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\n\n    let file = {\n      Url: imageUrl,\n      FileName: `Generated Image ${Date.now()}`,\n      IsAiGenerated: true\n    };\n    handleImageUploadFormApp(file);\n    setOpenGenAiImagePopup(false);\n  };\n  const handleEnhanceDescription = async () => {\n    if (description.trim() === \"\") {\n      openSnackbar(\"Please enter a description first.\", \"error\");\n      return;\n    }\n    setIsEnhancing(true);\n    try {\n      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\n      if (enhancedPrompt) {\n        setDescription(enhancedPrompt);\n      }\n    } catch (error) {\n      console.error(\"Error enhancing description:\", error);\n    } finally {\n      setIsEnhancing(false);\n    }\n  };\n  const GenerateImage = () => {\n    if (description === \"\") return;\n\n    // Validate that we have the correct image context\n    // containerId is required, but buttonId can be empty for new image generation\n    if (!imageAnchorEl.containerId) {\n      openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\n      return;\n    }\n    setIsGenerating(true); // Start loader\n    const controller = new AbortController();\n    setAbortController(controller);\n    const userPromptWithSelectedOptions = `User Asked: ${description} ${selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"} ${selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"} ${selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"}`;\n    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, imageUrl => {\n      onImageGenerated(imageUrl);\n      setIsGenerating(false); // Stop loader\n      setAbortController(null);\n    }, openSnackbar, controller.signal);\n  };\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: `${popupPosition.top}px`,\n        height: 'calc(100vh - 55px)',\n        width: '260px',\n        left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',\n        backgroundColor: '#fff',\n        boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.2)',\n        transition: 'left 0.3s ease-in-out',\n        zIndex: 1000,\n        padding: '12px',\n        alignItems: \"end\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"18px !important\"\n          },\n          children: translate(\"Generate Images\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer'\n          },\n          onClick: handleClose,\n          dangerouslySetInnerHTML: {\n            __html: closeIcon\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '93%',\n          marginTop: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          rows: 3,\n          placeholder: translate(\"Please describe your image...\"),\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          style: {\n            width: '93%',\n            height: '93px',\n            padding: '16px',\n            // Extra right padding for button space\n            fontSize: '12px',\n            borderRadius: '6px',\n            border: '1px solid #ccc',\n            resize: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleEnhanceDescription,\n          disabled: isEnhancing || description.trim() === \"\",\n          style: {\n            position: 'absolute',\n            top: '75%',\n            left: '10px',\n            transform: 'translateY(-50%)',\n            backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\n            color: 'black',\n            border: 'none',\n            padding: '4px 10px',\n            borderRadius: '100px',\n            fontSize: '10px',\n            cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\n            whiteSpace: 'nowrap',\n            alignItems: 'center',\n            display: 'flex',\n            gap: '6px',\n            opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5'\n          },\n          children: [isEnhancing ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '10px',\n              height: '10px',\n              border: '1px solid #333',\n              borderTop: '1px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 7\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: blackMagicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 7\n          }, this), isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Style\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: styles.map(style => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedStyle(style),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${style}`)\n          }, style, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Colors\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: colors.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedColor(color),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${color}`)\n          }, color, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: translate(\"Ratio\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: ratios.map(ratio => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedRatio(ratio),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: ratio\n          }, ratio, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 3\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (isGenerating) {\n            // Cancel if generating\n            abortController === null || abortController === void 0 ? void 0 : abortController.abort();\n            setIsGenerating(false);\n            setAbortController(null);\n          } else if (description.trim() !== '') {\n            // Don't call setReplaceImage(true) here as it's already set when popup opens\n            // and we need to maintain the correct imageAnchorEl context\n            GenerateImage();\n          }\n        },\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        disabled: isGenerating && !abortController,\n        style: {\n          marginTop: '20px',\n          width: '100%',\n          padding: '10px 16px',\n          borderRadius: '8px',\n          backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\n          color: '#fff',\n          border: 'none',\n          fontSize: '14px',\n          cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '8px',\n          transition: 'background-color 0.3s',\n          opacity: description.trim() === '' ? 0.7 : 1\n        },\n        children: isGenerating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '14px',\n              height: '14px',\n              border: '2px solid #fff',\n              borderTop: '2px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 7\n          }, this), isHovered ? translate('Cancel Generation') : translate('Generating...')]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: magicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 7\n          }, this), translate('Generate Image')]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ImageGenerationPopup, \"VlZ7NpvPH0NndLSODDF9xbDa0Sg=\", false, function () {\n  return [useTranslation, useSnackbar, useDrawerStore];\n});\n_c = ImageGenerationPopup;\nexport default ImageGenerationPopup;\nvar _c;\n$RefreshReg$(_c, \"ImageGenerationPopup\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "closeIcon", "magicPen", "Box", "Typography", "GenerateImageWithUserPrompt", "EnhanceUserPrompt", "AccountContext", "useTranslation", "useSnackbar", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageGenerationPopup", "openGenAiImagePopup", "setOpenGenAiImagePopup", "handleImageUploadFormApp", "setReplaceImage", "_s", "t", "translate", "openSnackbar", "imageAnchorEl", "state", "isHovered", "setIsHovered", "abortController", "setAbortController", "popupPosition", "setPopupPosition", "top", "left", "selected<PERSON><PERSON><PERSON>", "setSelectedStyle", "selectedColor", "setSelectedColor", "selectedRatio", "setSelectedRatio", "accountId", "styles", "colors", "ratios", "description", "setDescription", "isGenerating", "setIsGenerating", "isEnhancing", "setIsEnhancing", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "width", "height", "calculatePosition", "guidePopupPos", "viewportWidth", "window", "innerWidth", "popup<PERSON><PERSON><PERSON>", "gap", "requiredSpaceForExternal", "hasSpaceForExternal", "externalLeft", "Math", "min", "internalLeft", "max", "handleResize", "addEventListener", "removeEventListener", "handleClose", "onImageGenerated", "imageUrl", "file", "Url", "FileName", "Date", "now", "IsAiGenerated", "handleEnhanceDescription", "trim", "enhancedPrompt", "error", "console", "GenerateImage", "containerId", "controller", "AbortController", "userPromptWithSelectedOptions", "signal", "blackMagicPen", "replace", "children", "style", "position", "backgroundColor", "boxShadow", "transition", "zIndex", "padding", "alignItems", "sx", "display", "justifyContent", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cursor", "onClick", "dangerouslySetInnerHTML", "__html", "marginTop", "rows", "placeholder", "value", "onChange", "e", "target", "borderRadius", "border", "resize", "disabled", "transform", "color", "whiteSpace", "opacity", "borderTop", "animation", "flexWrap", "map", "ratio", "abort", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/ImageGenerationPopup.tsx"], "sourcesContent": ["import React, { useContext, useState, useEffect } from 'react';\r\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\r\nimport { Box,Typography } from '@mui/material';\r\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { FileUpload } from '../../../models/FileUpload';\r\nimport { timeStamp } from 'console';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useSnackbar } from '../guideList/SnackbarContext';\r\nimport useDrawerStore  from '../../../store/drawerStore';\r\n\r\n\r\n\r\n\r\n\r\nconst ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {\r\n  //const [isOpen, setIsOpen] = useState(false);\r\n\r\n  //const togglePopup = () => setIsOpen(!isOpen);\r\n  const { t: translate } = useTranslation();\r\n  const { openSnackbar } = useSnackbar();\r\n\r\n  // Get imageAnchorEl from store to know which image to replace\r\n  const { imageAnchorEl } = useDrawerStore((state) => state);\r\n\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [abortController, setAbortController] = useState<AbortController | null>(null);\r\n  const [popupPosition, setPopupPosition] = useState<{\r\n    top: number;\r\n    left: number;\r\n  }>({\r\n    top: 55,\r\n    left: 0\r\n  });\r\n\r\n\r\nconst [selectedStyle, setSelectedStyle] = useState('Professional');\r\n  const [selectedColor, setSelectedColor] = useState('Black & White');\r\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\r\n\r\n  const { accountId } = useContext(AccountContext);\r\n\r\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\r\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\r\n  const ratios = ['1:1', '16:9','9:16'];\r\n  const [description, setDescription] = useState('');\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n\r\n  // Function to get guidepopup position and dimensions\r\n  const getGuidePopupPosition = () => {\r\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n                    document.getElementById('guide-popup');\r\n    if (element) {\r\n      const rect = element.getBoundingClientRect();\r\n      return {\r\n        top: rect.top,\r\n        left: rect.left,\r\n        width: rect.width,\r\n        height: rect.height\r\n      };\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Function to calculate adaptive positioning\r\n  const calculatePosition = () => {\r\n    const guidePopupPos = getGuidePopupPosition();\r\n    if (!guidePopupPos) return;\r\n\r\n    const viewportWidth = window.innerWidth;\r\n    const popupWidth = 260; // ImageGenerationPopup width\r\n    const gap = 15; // Required gap between guidepopup and ImageGenerationPopup\r\n\r\n    // Calculate if there's enough space for external positioning\r\n    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;\r\n    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20; // 20px margin from viewport edge\r\n\r\n    if (hasSpaceForExternal) {\r\n      // External positioning: to the right of guidepopup with gap\r\n      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;\r\n      setPopupPosition({\r\n        top: 55, // Keep consistent with current top positioning\r\n        left: Math.min(externalLeft, viewportWidth - popupWidth - 10), // Ensure it doesn't overflow viewport\r\n      });\r\n    } else {\r\n      // Internal positioning: align to the right side within guidepopup\r\n      const internalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15; // 15px padding from right edge\r\n      setPopupPosition({\r\n        top: Math.max(55, guidePopupPos.top), // Align with guidepopup top or maintain minimum top\r\n        left: Math.max(guidePopupPos.left + 15, internalLeft), // Ensure it doesn't go beyond left edge with 15px padding\r\n      });\r\n    }\r\n  };\r\n\r\n  // Update positioning when popup opens\r\n  useEffect(() => {\r\n    if (openGenAiImagePopup) {\r\n      calculatePosition();\r\n\r\n      // Set up listeners for responsive behavior\r\n      const handleResize = () => calculatePosition();\r\n\r\n      window.addEventListener('resize', handleResize);\r\n\r\n      // Cleanup function\r\n      return () => {\r\n        window.removeEventListener('resize', handleResize);\r\n      };\r\n    }\r\n  }, [openGenAiImagePopup]);\r\n\r\n  const handleClose = () => {\r\n  setOpenGenAiImagePopup(false);\r\n  };\r\n\r\n  const onImageGenerated = (imageUrl: any) => {\r\n    \r\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\r\n\r\n    let file: any = {\r\n      Url: imageUrl,\r\n      FileName: `Generated Image ${Date.now()}`,\r\n      IsAiGenerated: true,\r\n    };\r\n    \r\n\r\n    handleImageUploadFormApp(file);\r\n    setOpenGenAiImagePopup(false);\r\n\r\n  }\r\n\r\nconst handleEnhanceDescription = async () => {\r\n  if (description.trim() === \"\") {\r\n    openSnackbar(\"Please enter a description first.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsEnhancing(true);\r\n  try {\r\n    const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\r\n    if (enhancedPrompt) {\r\n      setDescription(enhancedPrompt);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error enhancing description:\", error);\r\n  } finally {\r\n    setIsEnhancing(false);\r\n  }\r\n};\r\n\r\nconst GenerateImage = () => {\r\n  if (description === \"\") return;\r\n\r\n  // Validate that we have the correct image context\r\n  // containerId is required, but buttonId can be empty for new image generation\r\n  if (!imageAnchorEl.containerId) {\r\n    openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsGenerating(true); // Start loader\r\n    const controller = new AbortController();\r\n  setAbortController(controller);\r\n\r\nconst userPromptWithSelectedOptions = `User Asked: ${description} ${\r\n  selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"\r\n} ${\r\n  selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"\r\n} ${\r\n  selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"\r\n}`;\r\n\r\n\r\n  GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {\r\n    onImageGenerated(imageUrl);\r\n    setIsGenerating(false); // Stop loader\r\n    setAbortController(null);\r\n  }, openSnackbar, controller.signal);\r\n};\r\n\r\n    const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n  \r\n\r\n  return (\r\n    <>\r\n      <div\r\n        style={{\r\n          position: 'fixed',\r\n          top: `${popupPosition.top}px`,\r\n          height: 'calc(100vh - 55px)',\r\n          width: '260px',\r\n          left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',\r\n          backgroundColor: '#fff',\r\n          boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.2)',\r\n          transition: 'left 0.3s ease-in-out',\r\n          zIndex: 1000,\r\n          padding: '12px',\r\n          alignItems: \"end\"\r\n        }}\r\n          >\r\n              <Box sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"space-between\",\r\n                  \r\n              }}\r\n              >\r\n                  <Typography sx={{fontSize: \"18px !important\"}}>\r\n            \r\n            {translate(\"Generate Images\")}\r\n                  </Typography>\r\n                \r\n                \r\n                    <span style={{ cursor: 'pointer' }} onClick={handleClose} dangerouslySetInnerHTML={{ __html: closeIcon }}/>\r\n              </Box>\r\n              \r\n\r\n        <div style={{ position: 'relative', width: '93%', marginTop: '10px' }}>\r\n  <textarea\r\n    rows={3}\r\n    placeholder={translate(\"Please describe your image...\")}\r\n    value={description}\r\n    onChange={(e) => setDescription(e.target.value)}\r\n    style={{\r\n      width: '93%',\r\n      height: '93px',\r\n      padding: '16px', // Extra right padding for button space\r\n      fontSize: '12px',\r\n      borderRadius: '6px',\r\n      border: '1px solid #ccc',\r\n      resize: 'none',\r\n    }}\r\n  />\r\n  <button\r\n    onClick={handleEnhanceDescription}\r\n    disabled={isEnhancing || description.trim() === \"\"}\r\n    style={{\r\n      position: 'absolute',\r\n      top: '75%',\r\n      left: '10px',\r\n      transform: 'translateY(-50%)',\r\n      backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\r\n      color: 'black',\r\n      border: 'none',\r\n      padding: '4px 10px',\r\n      borderRadius: '100px',\r\n      fontSize: '10px',\r\n      cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\r\n      whiteSpace: 'nowrap',\r\n      alignItems: 'center',\r\n      display: 'flex',\r\n      gap: '6px',\r\n      opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5',\r\n    }}\r\n          >\r\n    {isEnhancing ? (\r\n      <div\r\n        style={{\r\n          width: '10px',\r\n          height: '10px',\r\n          border: '1px solid #333',\r\n          borderTop: '1px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n    ) : (\r\n      <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} />\r\n    )}\r\n\r\n    {isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")}\r\n  </button>\r\n</div>\r\n\r\n\r\n              {/* You can add form, preview, upload, etc. inside here */}\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Style\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }} >\r\n          {styles.map((style) => (\r\n            <button\r\n              key={style}\r\n              onClick={() => setSelectedStyle(style)}\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n            >\r\n               {translate(`${style}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n              </div>\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Colors\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n          {colors.map((color) => (\r\n            <button\r\n              key={color}\r\n              onClick={() => setSelectedColor(color)}\r\n\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n              \r\n            >\r\n               {translate(`${color}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n        </div>\r\n        <div>\r\n  <h2>{translate(\"Ratio\")}</h2>\r\n  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n{ratios.map((ratio) => (\r\n  <button\r\n    key={ratio}\r\n    onClick={() => setSelectedRatio(ratio)}\r\n    style={{\r\n      padding: '5px 12px',\r\n      borderRadius: '8px',\r\n      border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n      backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\r\n      color: '#333',\r\n      cursor: 'pointer',\r\n      fontSize: '11px',\r\n    }}\r\n  >\r\n    {ratio}\r\n  </button>\r\n))}\r\n\r\n  </div>\r\n</div>\r\n\r\n<button\r\n  onClick={() => {\r\n    if (isGenerating) {\r\n      // Cancel if generating\r\n      abortController?.abort();\r\n      setIsGenerating(false);\r\n      setAbortController(null);\r\n    } else if (description.trim() !== '') {\r\n      // Don't call setReplaceImage(true) here as it's already set when popup opens\r\n      // and we need to maintain the correct imageAnchorEl context\r\n      GenerateImage();\r\n    }\r\n  }}\r\n  onMouseEnter={() => setIsHovered(true)}\r\n  onMouseLeave={() => setIsHovered(false)}\r\n  disabled={isGenerating && !abortController}\r\n  style={{\r\n    marginTop: '20px',\r\n    width: '100%',\r\n    padding: '10px 16px',\r\n    borderRadius: '8px',\r\n    backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\r\n    color: '#fff',\r\n    border: 'none',\r\n    fontSize: '14px',\r\n    cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '8px',\r\n    transition: 'background-color 0.3s',\r\n    opacity: description.trim() === '' ? 0.7 : 1,\r\n  }}\r\n>\r\n  {isGenerating ? (\r\n    <>\r\n      <div\r\n        style={{\r\n          width: '14px',\r\n          height: '14px',\r\n          border: '2px solid #fff',\r\n          borderTop: '2px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n      {isHovered ? translate('Cancel Generation') : translate('Generating...')}\r\n    </>\r\n  ) : (\r\n    <>\r\n      <span dangerouslySetInnerHTML={{ __html: magicPen }} />\r\n      {translate('Generate Image')}\r\n    </>\r\n  )}\r\n</button>\r\n\r\n\r\n          </div>\r\n\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ImageGenerationPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,SAAS,EAAEC,QAAQ,QAAQ,6BAA6B;AACjE,SAASC,GAAG,EAACC,UAAU,QAAQ,eAAe;AAC9C,SAASC,2BAA2B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC5F,SAASC,cAAc,QAAQ,4BAA4B;AAG3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAOC,cAAc,MAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMzD,MAAMC,oBAAoB,GAAGA,CAAC;EAACC,mBAAmB;EAACC,sBAAsB;EAACC,wBAAwB;EAACC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC1H;;EAEA;EACA,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGd,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEe;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAM;IAAEe;EAAc,CAAC,GAAGd,cAAc,CAAEe,KAAK,IAAKA,KAAK,CAAC;EAE1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAG/C;IACDiC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE;EACR,CAAC,CAAC;EAGJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,cAAc,CAAC;EAChE,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,eAAe,CAAC;EACnE,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,MAAM,CAAC;EAE1D,MAAM;IAAEyC;EAAU,CAAC,GAAG1C,UAAU,CAACS,cAAc,CAAC;EAEhD,MAAMkC,MAAM,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;EACzF,MAAMC,MAAM,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;EACxF,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAC,MAAM,CAAC;EACrC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;;EAGrD;EACA,MAAMmD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IAC7DD,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACLxB,GAAG,EAAEuB,IAAI,CAACvB,GAAG;QACbC,IAAI,EAAEsB,IAAI,CAACtB,IAAI;QACfwB,KAAK,EAAEF,IAAI,CAACE,KAAK;QACjBC,MAAM,EAAEH,IAAI,CAACG;MACf,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,aAAa,GAAGV,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACU,aAAa,EAAE;IAEpB,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,UAAU,GAAG,GAAG,CAAC,CAAC;IACxB,MAAMC,GAAG,GAAG,EAAE,CAAC,CAAC;;IAEhB;IACA,MAAMC,wBAAwB,GAAGN,aAAa,CAAC3B,IAAI,GAAG2B,aAAa,CAACH,KAAK,GAAGQ,GAAG,GAAGD,UAAU;IAC5F,MAAMG,mBAAmB,GAAGD,wBAAwB,IAAIL,aAAa,GAAG,EAAE,CAAC,CAAC;;IAE5E,IAAIM,mBAAmB,EAAE;MACvB;MACA,MAAMC,YAAY,GAAGR,aAAa,CAAC3B,IAAI,GAAG2B,aAAa,CAACH,KAAK,GAAGQ,GAAG;MACnElC,gBAAgB,CAAC;QACfC,GAAG,EAAE,EAAE;QAAE;QACTC,IAAI,EAAEoC,IAAI,CAACC,GAAG,CAACF,YAAY,EAAEP,aAAa,GAAGG,UAAU,GAAG,EAAE,CAAC,CAAE;MACjE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMO,YAAY,GAAGX,aAAa,CAAC3B,IAAI,GAAG2B,aAAa,CAACH,KAAK,GAAGO,UAAU,GAAG,EAAE,CAAC,CAAC;MACjFjC,gBAAgB,CAAC;QACfC,GAAG,EAAEqC,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEZ,aAAa,CAAC5B,GAAG,CAAC;QAAE;QACtCC,IAAI,EAAEoC,IAAI,CAACG,GAAG,CAACZ,aAAa,CAAC3B,IAAI,GAAG,EAAE,EAAEsC,YAAY,CAAC,CAAE;MACzD,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACAvE,SAAS,CAAC,MAAM;IACd,IAAIgB,mBAAmB,EAAE;MACvB2C,iBAAiB,CAAC,CAAC;;MAEnB;MACA,MAAMc,YAAY,GAAGA,CAAA,KAAMd,iBAAiB,CAAC,CAAC;MAE9CG,MAAM,CAACY,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACXX,MAAM,CAACa,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MACpD,CAAC;IACH;EACF,CAAC,EAAE,CAACzD,mBAAmB,CAAC,CAAC;EAEzB,MAAM4D,WAAW,GAAGA,CAAA,KAAM;IAC1B3D,sBAAsB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM4D,gBAAgB,GAAIC,QAAa,IAAK;IAE1C;;IAEA,IAAIC,IAAS,GAAG;MACdC,GAAG,EAAEF,QAAQ;MACbG,QAAQ,EAAE,mBAAmBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACzCC,aAAa,EAAE;IACjB,CAAC;IAGDlE,wBAAwB,CAAC6D,IAAI,CAAC;IAC9B9D,sBAAsB,CAAC,KAAK,CAAC;EAE/B,CAAC;EAEH,MAAMoE,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAIzC,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B/D,YAAY,CAAC,mCAAmC,EAAE,OAAO,CAAC;MAC1D;IACF;IAEA0B,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMsC,cAAc,GAAG,MAAMjF,iBAAiB,CAACsC,WAAW,EAAEJ,SAAS,EAAEjB,YAAY,CAAC;MACpF,IAAIgE,cAAc,EAAE;QAClB1C,cAAc,CAAC0C,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRvC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI9C,WAAW,KAAK,EAAE,EAAE;;IAExB;IACA;IACA,IAAI,CAACpB,aAAa,CAACmE,WAAW,EAAE;MAC9BpE,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;MAClG;IACF;IAEAwB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACrB,MAAM6C,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC1ChE,kBAAkB,CAAC+D,UAAU,CAAC;IAEhC,MAAME,6BAA6B,GAAG,eAAelD,WAAW,IAC9DV,aAAa,KAAK,EAAE,GAAG,oCAAoC,GAAGA,aAAa,GAAG,EAAE,IAEhFE,aAAa,KAAK,EAAE,GAAG,wCAAwC,GAAGA,aAAa,GAAG,EAAE,IAEpFE,aAAa,KAAK,EAAE,GAAG,uCAAuC,GAAGA,aAAa,GAAG,EAAE,EACnF;IAGAjC,2BAA2B,CAACyF,6BAA6B,EAAEtD,SAAS,EAAGsC,QAAa,IAAK;MACvFD,gBAAgB,CAACC,QAAQ,CAAC;MAC1B/B,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MACxBlB,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,EAAEN,YAAY,EAAEqE,UAAU,CAACG,MAAM,CAAC;EACrC,CAAC;EAEG,MAAMC,aAAa,GAAG9F,QAAQ,CAAC+F,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAG7E,oBACErF,OAAA,CAAAE,SAAA;IAAAoF,QAAA,eACEtF,OAAA;MACEuF,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBpE,GAAG,EAAE,GAAGF,aAAa,CAACE,GAAG,IAAI;QAC7B0B,MAAM,EAAE,oBAAoB;QAC5BD,KAAK,EAAE,OAAO;QACdxB,IAAI,EAAEjB,mBAAmB,GAAG,GAAGc,aAAa,CAACG,IAAI,IAAI,GAAG,QAAQ;QAChEoE,eAAe,EAAE,MAAM;QACvBC,SAAS,EAAE,+BAA+B;QAC1CC,UAAU,EAAE,uBAAuB;QACnCC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,gBAEItF,OAAA,CAACT,GAAG;QAACwG,EAAE,EAAE;UACLC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE;QAEpB,CAAE;QAAAX,QAAA,gBAEEtF,OAAA,CAACR,UAAU;UAACuG,EAAE,EAAE;YAACG,QAAQ,EAAE;UAAiB,CAAE;UAAAZ,QAAA,EAEnD5E,SAAS,CAAC,iBAAiB;QAAC;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAGXtG,OAAA;UAAMuF,KAAK,EAAE;YAAEgB,MAAM,EAAE;UAAU,CAAE;UAACC,OAAO,EAAExC,WAAY;UAACyC,uBAAuB,EAAE;YAAEC,MAAM,EAAErH;UAAU;QAAE;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC,eAGZtG,OAAA;QAAKuF,KAAK,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAE3C,KAAK,EAAE,KAAK;UAAE8D,SAAS,EAAE;QAAO,CAAE;QAAArB,QAAA,gBAC5EtF,OAAA;UACE4G,IAAI,EAAE,CAAE;UACRC,WAAW,EAAEnG,SAAS,CAAC,+BAA+B,CAAE;UACxDoG,KAAK,EAAE9E,WAAY;UACnB+E,QAAQ,EAAGC,CAAC,IAAK/E,cAAc,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDvB,KAAK,EAAE;YACL1C,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,MAAM;YACd+C,OAAO,EAAE,MAAM;YAAE;YACjBK,QAAQ,EAAE,MAAM;YAChBgB,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,gBAAgB;YACxBC,MAAM,EAAE;UACV;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFtG,OAAA;UACEwG,OAAO,EAAE/B,wBAAyB;UAClC4C,QAAQ,EAAEjF,WAAW,IAAIJ,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAG;UACnDa,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBpE,GAAG,EAAE,KAAK;YACVC,IAAI,EAAE,MAAM;YACZiG,SAAS,EAAE,kBAAkB;YAC7B7B,eAAe,EAAErD,WAAW,IAAIJ,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,WAAW;YACrF6C,KAAK,EAAE,OAAO;YACdJ,MAAM,EAAE,MAAM;YACdtB,OAAO,EAAE,UAAU;YACnBqB,YAAY,EAAE,OAAO;YACrBhB,QAAQ,EAAE,MAAM;YAChBK,MAAM,EAAEnE,WAAW,IAAIJ,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,aAAa,GAAG,SAAS;YAC5E8C,UAAU,EAAE,QAAQ;YACpB1B,UAAU,EAAE,QAAQ;YACpBE,OAAO,EAAE,MAAM;YACf3C,GAAG,EAAE,KAAK;YACVoE,OAAO,EAAErF,WAAW,IAAIJ,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG;UAC9D,CAAE;UAAAY,QAAA,GAEDlD,WAAW,gBACVpC,OAAA;YACEuF,KAAK,EAAE;cACL1C,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdqE,MAAM,EAAE,gBAAgB;cACxBO,SAAS,EAAE,uBAAuB;cAClCR,YAAY,EAAE,KAAK;cACnBS,SAAS,EAAE;YACb;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFtG,OAAA;YAAMyG,uBAAuB,EAAE;cAAEC,MAAM,EAAEtB;YAAc;UAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5D,EAEAlE,WAAW,GAAG1B,SAAS,CAAC,cAAc,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAC;QAAA;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAKQtG,OAAA;QAAAsF,QAAA,gBACNtF,OAAA;UAAAsF,QAAA,GAAK,GAAC,EAAC5E,SAAS,CAAC,OAAO,CAAC;QAAA;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/BtG,OAAA;UAAKuF,KAAK,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE4B,QAAQ,EAAE,MAAM;YAAEvE,GAAG,EAAE,KAAK;YAAEsD,SAAS,EAAE;UAAO,CAAE;UAAArB,QAAA,EAC9EzD,MAAM,CAACgG,GAAG,CAAEtC,KAAK,iBAChBvF,OAAA;YAEEwG,OAAO,EAAEA,CAAA,KAAMjF,gBAAgB,CAACgE,KAAK,CAAE;YACjCA,KAAK,EAAE;cACLM,OAAO,EAAE,UAAU;cACnBqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE7F,aAAa,KAAKiE,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxEE,eAAe,EAAEnE,aAAa,KAAKiE,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEgC,KAAK,EAAE,MAAM;cACbhB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAZ,QAAA,EAEN5E,SAAS,CAAC,GAAG6E,KAAK,EAAE;UAAC,GAZlBA,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAENtG,OAAA;QAAAsF,QAAA,gBACNtF,OAAA;UAAAsF,QAAA,GAAK,GAAC,EAAC5E,SAAS,CAAC,QAAQ,CAAC;QAAA;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChCtG,OAAA;UAAKuF,KAAK,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE4B,QAAQ,EAAE,MAAM;YAAEvE,GAAG,EAAE,KAAK;YAAEsD,SAAS,EAAE;UAAO,CAAE;UAAArB,QAAA,EAC9ExD,MAAM,CAAC+F,GAAG,CAAEN,KAAK,iBAChBvH,OAAA;YAEEwG,OAAO,EAAEA,CAAA,KAAM/E,gBAAgB,CAAC8F,KAAK,CAAE;YAEjChC,KAAK,EAAE;cACLM,OAAO,EAAE,UAAU;cACnBqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE3F,aAAa,KAAK+F,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxE9B,eAAe,EAAEjE,aAAa,KAAK+F,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEA,KAAK,EAAE,MAAM;cACbhB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAZ,QAAA,EAGN5E,SAAS,CAAC,GAAG6G,KAAK,EAAE;UAAC,GAdlBA,KAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNtG,OAAA;QAAAsF,QAAA,gBACNtF,OAAA;UAAAsF,QAAA,EAAK5E,SAAS,CAAC,OAAO;QAAC;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BtG,OAAA;UAAKuF,KAAK,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE4B,QAAQ,EAAE,MAAM;YAAEvE,GAAG,EAAE,KAAK;YAAEsD,SAAS,EAAE;UAAO,CAAE;UAAArB,QAAA,EAClFvD,MAAM,CAAC8F,GAAG,CAAEC,KAAK,iBAChB9H,OAAA;YAEEwG,OAAO,EAAEA,CAAA,KAAM7E,gBAAgB,CAACmG,KAAK,CAAE;YACvCvC,KAAK,EAAE;cACLM,OAAO,EAAE,UAAU;cACnBqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAEzF,aAAa,KAAKoG,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxErC,eAAe,EAAE/D,aAAa,KAAKoG,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEP,KAAK,EAAE,MAAM;cACbhB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAZ,QAAA,EAEDwC;UAAK,GAZDA,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtG,OAAA;QACEwG,OAAO,EAAEA,CAAA,KAAM;UACb,IAAItE,YAAY,EAAE;YAChB;YACAlB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+G,KAAK,CAAC,CAAC;YACxB5F,eAAe,CAAC,KAAK,CAAC;YACtBlB,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM,IAAIe,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC;YACA;YACAI,aAAa,CAAC,CAAC;UACjB;QACF,CAAE;QACFkD,YAAY,EAAEA,CAAA,KAAMjH,YAAY,CAAC,IAAI,CAAE;QACvCkH,YAAY,EAAEA,CAAA,KAAMlH,YAAY,CAAC,KAAK,CAAE;QACxCsG,QAAQ,EAAEnF,YAAY,IAAI,CAAClB,eAAgB;QAC3CuE,KAAK,EAAE;UACLoB,SAAS,EAAE,MAAM;UACjB9D,KAAK,EAAE,MAAM;UACbgD,OAAO,EAAE,WAAW;UACpBqB,YAAY,EAAE,KAAK;UACnBzB,eAAe,EAAEvD,YAAY,GAAG,SAAS,GAAGF,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UAC7F6C,KAAK,EAAE,MAAM;UACbJ,MAAM,EAAE,MAAM;UACdjB,QAAQ,EAAE,MAAM;UAChBK,MAAM,EAAErE,YAAY,IAAIF,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UACzEsB,OAAO,EAAE,MAAM;UACfF,UAAU,EAAE,QAAQ;UACpBG,cAAc,EAAE,QAAQ;UACxB5C,GAAG,EAAE,KAAK;UACVsC,UAAU,EAAE,uBAAuB;UACnC8B,OAAO,EAAEzF,WAAW,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;QAC7C,CAAE;QAAAY,QAAA,EAEDpD,YAAY,gBACXlC,OAAA,CAAAE,SAAA;UAAAoF,QAAA,gBACEtF,OAAA;YACEuF,KAAK,EAAE;cACL1C,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdqE,MAAM,EAAE,gBAAgB;cACxBO,SAAS,EAAE,uBAAuB;cAClCR,YAAY,EAAE,KAAK;cACnBS,SAAS,EAAE;YACb;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDxF,SAAS,GAAGJ,SAAS,CAAC,mBAAmB,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAC;QAAA,eACxE,CAAC,gBAEHV,OAAA,CAAAE,SAAA;UAAAoF,QAAA,gBACEtF,OAAA;YAAMyG,uBAAuB,EAAE;cAAEC,MAAM,EAAEpH;YAAS;UAAE;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtD5F,SAAS,CAAC,gBAAgB,CAAC;QAAA,eAC5B;MACH;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGM;EAAC,gBAEV,CAAC;AAEP,CAAC;AAAC9F,EAAA,CA3YIL,oBAAoB;EAAA,QAICP,cAAc,EACdC,WAAW,EAGVC,cAAc;AAAA;AAAAoI,EAAA,GARpC/H,oBAAoB;AA6Y1B,eAAeA,oBAAoB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}