{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\ImageGenerationPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState } from 'react';\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\nimport { Box, Typography } from '@mui/material';\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\nimport { AccountContext } from '../../login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { useSnackbar } from '../guideList/SnackbarContext';\nimport useDrawerStore from '../../../store/drawerStore';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageGenerationPopup = ({\n  openGenAiImagePopup,\n  setOpenGenAiImagePopup,\n  handleImageUploadFormApp,\n  setReplaceImage\n}) => {\n  _s();\n  //const [isOpen, setIsOpen] = useState(false);\n\n  //const togglePopup = () => setIsOpen(!isOpen);\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    openSnackbar\n  } = useSnackbar();\n\n  // Get imageAnchorEl from store to know which image to replace\n  const {\n    imageAnchorEl\n  } = useDrawerStore(state => state);\n  const [isHovered, setIsHovered] = useState(false);\n  const [abortController, setAbortController] = useState(null);\n  const [selectedStyle, setSelectedStyle] = useState('Professional');\n  const [selectedColor, setSelectedColor] = useState('Black & White');\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\n  const ratios = ['1:1', '16:9', '9:16'];\n  const [description, setDescription] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [isEnhancing, setIsEnhancing] = useState(false);\n  const handleClose = () => {\n    setOpenGenAiImagePopup(false);\n  };\n  const onImageGenerated = imageUrl => {\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\n\n    let file = {\n      Url: imageUrl,\n      FileName: `Generated Image ${Date.now()}`,\n      IsAiGenerated: true\n    };\n    handleImageUploadFormApp(file);\n    setOpenGenAiImagePopup(false);\n  };\n  const handleEnhanceDescription = async () => {\n    if (description.trim() === \"\") {\n      openSnackbar(\"Please enter a description first.\", \"error\");\n      return;\n    }\n    setIsEnhancing(true);\n    try {\n      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\n      if (enhancedPrompt) {\n        setDescription(enhancedPrompt);\n      }\n    } catch (error) {\n      console.error(\"Error enhancing description:\", error);\n    } finally {\n      setIsEnhancing(false);\n    }\n  };\n  const GenerateImage = () => {\n    if (description === \"\") return;\n\n    // Validate that we have the correct image context\n    // containerId is required, but buttonId can be empty for new image generation\n    if (!imageAnchorEl.containerId) {\n      openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\n      return;\n    }\n    setIsGenerating(true); // Start loader\n    const controller = new AbortController();\n    setAbortController(controller);\n    const userPromptWithSelectedOptions = `User Asked: ${description} ${selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"} ${selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"} ${selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"}`;\n    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, imageUrl => {\n      onImageGenerated(imageUrl);\n      setIsGenerating(false); // Stop loader\n      setAbortController(null);\n    }, openSnackbar, controller.signal);\n  };\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: \"55px\",\n        height: 'calc(100vh - 55px)',\n        width: '280px',\n        right: openGenAiImagePopup ? '0px' : \"-320px\",\n        backgroundColor: '#fff',\n        boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.2)',\n        transition: 'right 0.3s ease-in-out',\n        zIndex: 1000,\n        padding: '12px',\n        alignItems: \"end\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"space-between\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"18px !important\"\n          },\n          children: translate(\"Generate Images\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            cursor: 'pointer'\n          },\n          onClick: handleClose,\n          dangerouslySetInnerHTML: {\n            __html: closeIcon\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          width: '93%',\n          marginTop: '10px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          rows: 3,\n          placeholder: translate(\"Please describe your image...\"),\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          style: {\n            width: '93%',\n            height: '93px',\n            padding: '16px',\n            // Extra right padding for button space\n            fontSize: '12px',\n            borderRadius: '6px',\n            border: '1px solid #ccc',\n            resize: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleEnhanceDescription,\n          disabled: isEnhancing || description.trim() === \"\",\n          style: {\n            position: 'absolute',\n            top: '75%',\n            left: '10px',\n            transform: 'translateY(-50%)',\n            backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\n            color: 'black',\n            border: 'none',\n            padding: '4px 10px',\n            borderRadius: '100px',\n            fontSize: '10px',\n            cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\n            whiteSpace: 'nowrap',\n            alignItems: 'center',\n            display: 'flex',\n            gap: '6px',\n            opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5'\n          },\n          children: [isEnhancing ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '10px',\n              height: '10px',\n              border: '1px solid #333',\n              borderTop: '1px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 7\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: blackMagicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 7\n          }, this), isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Style\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: styles.map(style => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedStyle(style),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${style}`)\n          }, style, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\" \", translate(\"Colors\")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: colors.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedColor(color),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: translate(`${color}`)\n          }, color, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: translate(\"Ratio\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '8px',\n            marginTop: '10px'\n          },\n          children: ratios.map(ratio => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedRatio(ratio),\n            style: {\n              padding: '5px 12px',\n              borderRadius: '8px',\n              border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\n              backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\n              color: '#333',\n              cursor: 'pointer',\n              fontSize: '11px'\n            },\n            children: ratio\n          }, ratio, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 3\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (isGenerating) {\n            // Cancel if generating\n            abortController === null || abortController === void 0 ? void 0 : abortController.abort();\n            setIsGenerating(false);\n            setAbortController(null);\n          } else if (description.trim() !== '') {\n            // Don't call setReplaceImage(true) here as it's already set when popup opens\n            // and we need to maintain the correct imageAnchorEl context\n            GenerateImage();\n          }\n        },\n        onMouseEnter: () => setIsHovered(true),\n        onMouseLeave: () => setIsHovered(false),\n        disabled: isGenerating && !abortController,\n        style: {\n          marginTop: '20px',\n          width: '100%',\n          padding: '10px 16px',\n          borderRadius: '8px',\n          backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\n          color: '#fff',\n          border: 'none',\n          fontSize: '14px',\n          cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '8px',\n          transition: 'background-color 0.3s',\n          opacity: description.trim() === '' ? 0.7 : 1\n        },\n        children: isGenerating ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '14px',\n              height: '14px',\n              border: '2px solid #fff',\n              borderTop: '2px solid transparent',\n              borderRadius: '50%',\n              animation: 'spin 0.8s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 7\n          }, this), isHovered ? translate('Cancel Generation') : translate('Generating...')]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: magicPen\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 7\n          }, this), translate('Generate Image')]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ImageGenerationPopup, \"BNzgAYiX5O7M2/25UAQrOHurSfw=\", false, function () {\n  return [useTranslation, useSnackbar, useDrawerStore];\n});\n_c = ImageGenerationPopup;\nexport default ImageGenerationPopup;\nvar _c;\n$RefreshReg$(_c, \"ImageGenerationPopup\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "closeIcon", "magicPen", "Box", "Typography", "GenerateImageWithUserPrompt", "EnhanceUserPrompt", "AccountContext", "useTranslation", "useSnackbar", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageGenerationPopup", "openGenAiImagePopup", "setOpenGenAiImagePopup", "handleImageUploadFormApp", "setReplaceImage", "_s", "t", "translate", "openSnackbar", "imageAnchorEl", "state", "isHovered", "setIsHovered", "abortController", "setAbortController", "selected<PERSON><PERSON><PERSON>", "setSelectedStyle", "selectedColor", "setSelectedColor", "selectedRatio", "setSelectedRatio", "accountId", "styles", "colors", "ratios", "description", "setDescription", "isGenerating", "setIsGenerating", "isEnhancing", "setIsEnhancing", "handleClose", "onImageGenerated", "imageUrl", "file", "Url", "FileName", "Date", "now", "IsAiGenerated", "handleEnhanceDescription", "trim", "enhancedPrompt", "error", "console", "GenerateImage", "containerId", "controller", "AbortController", "userPromptWithSelectedOptions", "signal", "blackMagicPen", "replace", "children", "style", "position", "top", "height", "width", "right", "backgroundColor", "boxShadow", "transition", "zIndex", "padding", "alignItems", "sx", "display", "justifyContent", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cursor", "onClick", "dangerouslySetInnerHTML", "__html", "marginTop", "rows", "placeholder", "value", "onChange", "e", "target", "borderRadius", "border", "resize", "disabled", "left", "transform", "color", "whiteSpace", "gap", "opacity", "borderTop", "animation", "flexWrap", "map", "ratio", "abort", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/ImageGenerationPopup.tsx"], "sourcesContent": ["import React, { useContext, useState, useEffect, useRef } from 'react';\r\nimport { closeIcon, magicPen } from '../../../assets/icons/icons';\r\nimport { Box,Typography } from '@mui/material';\r\nimport { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';\r\nimport { AccountContext } from '../../login/AccountContext';\r\nimport { FileUpload } from '../../../models/FileUpload';\r\nimport { timeStamp } from 'console';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useSnackbar } from '../guideList/SnackbarContext';\r\nimport useDrawerStore  from '../../../store/drawerStore';\r\n\r\n\r\n\r\n\r\n\r\nconst ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {\r\n  //const [isOpen, setIsOpen] = useState(false);\r\n\r\n  //const togglePopup = () => setIsOpen(!isOpen);\r\n  const { t: translate } = useTranslation();\r\n  const { openSnackbar } = useSnackbar();\r\n\r\n  // Get imageAnchorEl from store to know which image to replace\r\n  const { imageAnchorEl } = useDrawerStore((state) => state);\r\n\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [abortController, setAbortController] = useState<AbortController | null>(null);\r\n\r\n\r\nconst [selectedStyle, setSelectedStyle] = useState('Professional');\r\n  const [selectedColor, setSelectedColor] = useState('Black & White');\r\n  const [selectedRatio, setSelectedRatio] = useState('16:9');\r\n\r\n  const { accountId } = useContext(AccountContext);\r\n\r\n  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];\r\n  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];\r\n  const ratios = ['1:1', '16:9','9:16'];\r\n  const [description, setDescription] = useState('');\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n\r\n\r\n  const handleClose = () => {\r\n  setOpenGenAiImagePopup(false);\r\n  };\r\n\r\n  const onImageGenerated = (imageUrl: any) => {\r\n    \r\n    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()\r\n\r\n    let file: any = {\r\n      Url: imageUrl,\r\n      FileName: `Generated Image ${Date.now()}`,\r\n      IsAiGenerated: true,\r\n    };\r\n    \r\n\r\n    handleImageUploadFormApp(file);\r\n    setOpenGenAiImagePopup(false);\r\n\r\n  }\r\n\r\nconst handleEnhanceDescription = async () => {\r\n  if (description.trim() === \"\") {\r\n    openSnackbar(\"Please enter a description first.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsEnhancing(true);\r\n  try {\r\n    const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);\r\n    if (enhancedPrompt) {\r\n      setDescription(enhancedPrompt);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error enhancing description:\", error);\r\n  } finally {\r\n    setIsEnhancing(false);\r\n  }\r\n};\r\n\r\nconst GenerateImage = () => {\r\n  if (description === \"\") return;\r\n\r\n  // Validate that we have the correct image context\r\n  // containerId is required, but buttonId can be empty for new image generation\r\n  if (!imageAnchorEl.containerId) {\r\n    openSnackbar(\"Error: Unable to identify which image container to use. Please try again.\", \"error\");\r\n    return;\r\n  }\r\n\r\n  setIsGenerating(true); // Start loader\r\n    const controller = new AbortController();\r\n  setAbortController(controller);\r\n\r\nconst userPromptWithSelectedOptions = `User Asked: ${description} ${\r\n  selectedStyle !== \"\" ? \" and Image Should be in the Style \" + selectedStyle : \"\"\r\n} ${\r\n  selectedColor !== \"\" ? \" and Image Should be in Color Palette \" + selectedColor : \"\"\r\n} ${\r\n  selectedRatio !== \"\" ? \" and Image Should be in Aspect Ratio \" + selectedRatio : \"\"\r\n}`;\r\n\r\n\r\n  GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {\r\n    onImageGenerated(imageUrl);\r\n    setIsGenerating(false); // Stop loader\r\n    setAbortController(null);\r\n  }, openSnackbar, controller.signal);\r\n};\r\n\r\n    const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n  \r\n\r\n  return (\r\n    <>\r\n      <div\r\n        style={{\r\n          position: 'fixed',\r\n          top: \"55px\",\r\n          height: 'calc(100vh - 55px)',\r\n          width: '280px',\r\n          right: openGenAiImagePopup ? '0px' : \"-320px\",\r\n          backgroundColor: '#fff',\r\n          boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.2)',\r\n          transition: 'right 0.3s ease-in-out',\r\n          zIndex: 1000,\r\n          padding: '12px',\r\n          alignItems: \"end\"\r\n        }}\r\n          >\r\n              <Box sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"space-between\",\r\n                  \r\n              }}\r\n              >\r\n                  <Typography sx={{fontSize: \"18px !important\"}}>\r\n            \r\n            {translate(\"Generate Images\")}\r\n                  </Typography>\r\n                \r\n                \r\n                    <span style={{ cursor: 'pointer' }} onClick={handleClose} dangerouslySetInnerHTML={{ __html: closeIcon }}/>\r\n              </Box>\r\n              \r\n\r\n        <div style={{ position: 'relative', width: '93%', marginTop: '10px' }}>\r\n  <textarea\r\n    rows={3}\r\n    placeholder={translate(\"Please describe your image...\")}\r\n    value={description}\r\n    onChange={(e) => setDescription(e.target.value)}\r\n    style={{\r\n      width: '93%',\r\n      height: '93px',\r\n      padding: '16px', // Extra right padding for button space\r\n      fontSize: '12px',\r\n      borderRadius: '6px',\r\n      border: '1px solid #ccc',\r\n      resize: 'none',\r\n    }}\r\n  />\r\n  <button\r\n    onClick={handleEnhanceDescription}\r\n    disabled={isEnhancing || description.trim() === \"\"}\r\n    style={{\r\n      position: 'absolute',\r\n      top: '75%',\r\n      left: '10px',\r\n      transform: 'translateY(-50%)',\r\n      backgroundColor: isEnhancing || description.trim() === \"\" ? '#aaa9a9ff' : '#aaa9a9ff',\r\n      color: 'black',\r\n      border: 'none',\r\n      padding: '4px 10px',\r\n      borderRadius: '100px',\r\n      fontSize: '10px',\r\n      cursor: isEnhancing || description.trim() === \"\" ? 'not-allowed' : 'pointer',\r\n      whiteSpace: 'nowrap',\r\n      alignItems: 'center',\r\n      display: 'flex',\r\n      gap: '6px',\r\n      opacity: isEnhancing || description.trim() === \"\" ? '0.3' : '0.5',\r\n    }}\r\n          >\r\n    {isEnhancing ? (\r\n      <div\r\n        style={{\r\n          width: '10px',\r\n          height: '10px',\r\n          border: '1px solid #333',\r\n          borderTop: '1px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n    ) : (\r\n      <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} />\r\n    )}\r\n\r\n    {isEnhancing ? translate(\"Enhancing...\") : translate(\"Enhance with AI\")}\r\n  </button>\r\n</div>\r\n\r\n\r\n              {/* You can add form, preview, upload, etc. inside here */}\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Style\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }} >\r\n          {styles.map((style) => (\r\n            <button\r\n              key={style}\r\n              onClick={() => setSelectedStyle(style)}\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedStyle === style ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedStyle === style ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n            >\r\n               {translate(`${style}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n              </div>\r\n              \r\n              <div >\r\n        <h2 > {translate(\"Colors\")}</h2>\r\n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n          {colors.map((color) => (\r\n            <button\r\n              key={color}\r\n              onClick={() => setSelectedColor(color)}\r\n\r\n                    style={{\r\n                      padding: '5px 12px',\r\n                      borderRadius: '8px',\r\n                      border: selectedColor === color ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n                      backgroundColor: selectedColor === color ? '#e3f2fd' : '#f0f0f0',\r\n                      color: '#333',\r\n                      cursor: 'pointer',\r\n                      fontSize: '11px',\r\n                    }}\r\n              \r\n            >\r\n               {translate(`${color}`)}\r\n            </button>\r\n          ))}\r\n        </div>\r\n        </div>\r\n        <div>\r\n  <h2>{translate(\"Ratio\")}</h2>\r\n  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '10px' }}>\r\n{ratios.map((ratio) => (\r\n  <button\r\n    key={ratio}\r\n    onClick={() => setSelectedRatio(ratio)}\r\n    style={{\r\n      padding: '5px 12px',\r\n      borderRadius: '8px',\r\n      border: selectedRatio === ratio ? '1px solid #5F9EA0' : '1px solid #ccc',\r\n      backgroundColor: selectedRatio === ratio ? '#e3f2fd' : '#f0f0f0',\r\n      color: '#333',\r\n      cursor: 'pointer',\r\n      fontSize: '11px',\r\n    }}\r\n  >\r\n    {ratio}\r\n  </button>\r\n))}\r\n\r\n  </div>\r\n</div>\r\n\r\n<button\r\n  onClick={() => {\r\n    if (isGenerating) {\r\n      // Cancel if generating\r\n      abortController?.abort();\r\n      setIsGenerating(false);\r\n      setAbortController(null);\r\n    } else if (description.trim() !== '') {\r\n      // Don't call setReplaceImage(true) here as it's already set when popup opens\r\n      // and we need to maintain the correct imageAnchorEl context\r\n      GenerateImage();\r\n    }\r\n  }}\r\n  onMouseEnter={() => setIsHovered(true)}\r\n  onMouseLeave={() => setIsHovered(false)}\r\n  disabled={isGenerating && !abortController}\r\n  style={{\r\n    marginTop: '20px',\r\n    width: '100%',\r\n    padding: '10px 16px',\r\n    borderRadius: '8px',\r\n    backgroundColor: isGenerating ? '#5F9EA0' : description.trim() === '' ? '#5F9EA0' : '#5F9EA0',\r\n    color: '#fff',\r\n    border: 'none',\r\n    fontSize: '14px',\r\n    cursor: isGenerating || description.trim() === '' ? 'pointer' : 'pointer',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '8px',\r\n    transition: 'background-color 0.3s',\r\n    opacity: description.trim() === '' ? 0.7 : 1,\r\n  }}\r\n>\r\n  {isGenerating ? (\r\n    <>\r\n      <div\r\n        style={{\r\n          width: '14px',\r\n          height: '14px',\r\n          border: '2px solid #fff',\r\n          borderTop: '2px solid transparent',\r\n          borderRadius: '50%',\r\n          animation: 'spin 0.8s linear infinite',\r\n        }}\r\n      />\r\n      {isHovered ? translate('Cancel Generation') : translate('Generating...')}\r\n    </>\r\n  ) : (\r\n    <>\r\n      <span dangerouslySetInnerHTML={{ __html: magicPen }} />\r\n      {translate('Generate Image')}\r\n    </>\r\n  )}\r\n</button>\r\n\r\n\r\n          </div>\r\n          \r\n    </>\r\n  );\r\n};\r\n\r\nexport default ImageGenerationPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAA2B,OAAO;AACtE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,6BAA6B;AACjE,SAASC,GAAG,EAACC,UAAU,QAAQ,eAAe;AAC9C,SAASC,2BAA2B,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC5F,SAASC,cAAc,QAAQ,4BAA4B;AAG3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAOC,cAAc,MAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMzD,MAAMC,oBAAoB,GAAGA,CAAC;EAACC,mBAAmB;EAACC,sBAAsB;EAACC,wBAAwB;EAACC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC1H;;EAEA;EACA,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGd,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEe;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAM;IAAEe;EAAc,CAAC,GAAGd,cAAc,CAAEe,KAAK,IAAKA,KAAK,CAAC;EAE1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAyB,IAAI,CAAC;EAGtF,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,cAAc,CAAC;EAChE,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,eAAe,CAAC;EACnE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,MAAM,CAAC;EAE1D,MAAM;IAAEoC;EAAU,CAAC,GAAGrC,UAAU,CAACQ,cAAc,CAAC;EAEhD,MAAM8B,MAAM,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC;EACzF,MAAMC,MAAM,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;EACxF,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,EAAC,MAAM,CAAC;EACrC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAGrD,MAAM8C,WAAW,GAAGA,CAAA,KAAM;IAC1B7B,sBAAsB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM8B,gBAAgB,GAAIC,QAAa,IAAK;IAE1C;;IAEA,IAAIC,IAAS,GAAG;MACdC,GAAG,EAAEF,QAAQ;MACbG,QAAQ,EAAE,mBAAmBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACzCC,aAAa,EAAE;IACjB,CAAC;IAGDpC,wBAAwB,CAAC+B,IAAI,CAAC;IAC9BhC,sBAAsB,CAAC,KAAK,CAAC;EAE/B,CAAC;EAEH,MAAMsC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAIf,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7BjC,YAAY,CAAC,mCAAmC,EAAE,OAAO,CAAC;MAC1D;IACF;IAEAsB,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF,MAAMY,cAAc,GAAG,MAAMnD,iBAAiB,CAACkC,WAAW,EAAEJ,SAAS,EAAEb,YAAY,CAAC;MACpF,IAAIkC,cAAc,EAAE;QAClBhB,cAAc,CAACgB,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRb,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIpB,WAAW,KAAK,EAAE,EAAE;;IAExB;IACA;IACA,IAAI,CAAChB,aAAa,CAACqC,WAAW,EAAE;MAC9BtC,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;MAClG;IACF;IAEAoB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IACrB,MAAMmB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC1ClC,kBAAkB,CAACiC,UAAU,CAAC;IAEhC,MAAME,6BAA6B,GAAG,eAAexB,WAAW,IAC9DV,aAAa,KAAK,EAAE,GAAG,oCAAoC,GAAGA,aAAa,GAAG,EAAE,IAEhFE,aAAa,KAAK,EAAE,GAAG,wCAAwC,GAAGA,aAAa,GAAG,EAAE,IAEpFE,aAAa,KAAK,EAAE,GAAG,uCAAuC,GAAGA,aAAa,GAAG,EAAE,EACnF;IAGA7B,2BAA2B,CAAC2D,6BAA6B,EAAE5B,SAAS,EAAGY,QAAa,IAAK;MACvFD,gBAAgB,CAACC,QAAQ,CAAC;MAC1BL,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MACxBd,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC,EAAEN,YAAY,EAAEuC,UAAU,CAACG,MAAM,CAAC;EACrC,CAAC;EAEG,MAAMC,aAAa,GAAGhE,QAAQ,CAACiE,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAG7E,oBACEvD,OAAA,CAAAE,SAAA;IAAAsD,QAAA,eACExD,OAAA;MACEyD,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXC,MAAM,EAAE,oBAAoB;QAC5BC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE1D,mBAAmB,GAAG,KAAK,GAAG,QAAQ;QAC7C2D,eAAe,EAAE,MAAM;QACvBC,SAAS,EAAE,+BAA+B;QAC1CC,UAAU,EAAE,wBAAwB;QACpCC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE;MACd,CAAE;MAAAZ,QAAA,gBAEIxD,OAAA,CAACT,GAAG;QAAC8E,EAAE,EAAE;UACLC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE;QAEpB,CAAE;QAAAf,QAAA,gBAEExD,OAAA,CAACR,UAAU;UAAC6E,EAAE,EAAE;YAACG,QAAQ,EAAE;UAAiB,CAAE;UAAAhB,QAAA,EAEnD9C,SAAS,CAAC,iBAAiB;QAAC;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAGX5E,OAAA;UAAMyD,KAAK,EAAE;YAAEoB,MAAM,EAAE;UAAU,CAAE;UAACC,OAAO,EAAE5C,WAAY;UAAC6C,uBAAuB,EAAE;YAAEC,MAAM,EAAE3F;UAAU;QAAE;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CAAC,eAGZ5E,OAAA;QAAKyD,KAAK,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEG,KAAK,EAAE,KAAK;UAAEoB,SAAS,EAAE;QAAO,CAAE;QAAAzB,QAAA,gBAC5ExD,OAAA;UACEkF,IAAI,EAAE,CAAE;UACRC,WAAW,EAAEzE,SAAS,CAAC,+BAA+B,CAAE;UACxD0E,KAAK,EAAExD,WAAY;UACnByD,QAAQ,EAAGC,CAAC,IAAKzD,cAAc,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChD3B,KAAK,EAAE;YACLI,KAAK,EAAE,KAAK;YACZD,MAAM,EAAE,MAAM;YACdO,OAAO,EAAE,MAAM;YAAE;YACjBK,QAAQ,EAAE,MAAM;YAChBgB,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,gBAAgB;YACxBC,MAAM,EAAE;UACV;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF5E,OAAA;UACE8E,OAAO,EAAEnC,wBAAyB;UAClCgD,QAAQ,EAAE3D,WAAW,IAAIJ,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAG;UACnDa,KAAK,EAAE;YACLC,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,KAAK;YACViC,IAAI,EAAE,MAAM;YACZC,SAAS,EAAE,kBAAkB;YAC7B9B,eAAe,EAAE/B,WAAW,IAAIJ,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,WAAW,GAAG,WAAW;YACrFkD,KAAK,EAAE,OAAO;YACdL,MAAM,EAAE,MAAM;YACdtB,OAAO,EAAE,UAAU;YACnBqB,YAAY,EAAE,OAAO;YACrBhB,QAAQ,EAAE,MAAM;YAChBK,MAAM,EAAE7C,WAAW,IAAIJ,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,aAAa,GAAG,SAAS;YAC5EmD,UAAU,EAAE,QAAQ;YACpB3B,UAAU,EAAE,QAAQ;YACpBE,OAAO,EAAE,MAAM;YACf0B,GAAG,EAAE,KAAK;YACVC,OAAO,EAAEjE,WAAW,IAAIJ,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG;UAC9D,CAAE;UAAAY,QAAA,GAEDxB,WAAW,gBACVhC,OAAA;YACEyD,KAAK,EAAE;cACLI,KAAK,EAAE,MAAM;cACbD,MAAM,EAAE,MAAM;cACd6B,MAAM,EAAE,gBAAgB;cACxBS,SAAS,EAAE,uBAAuB;cAClCV,YAAY,EAAE,KAAK;cACnBW,SAAS,EAAE;YACb;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF5E,OAAA;YAAM+E,uBAAuB,EAAE;cAAEC,MAAM,EAAE1B;YAAc;UAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5D,EAEA5C,WAAW,GAAGtB,SAAS,CAAC,cAAc,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAC;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAKQ5E,OAAA;QAAAwD,QAAA,gBACNxD,OAAA;UAAAwD,QAAA,GAAK,GAAC,EAAC9C,SAAS,CAAC,OAAO,CAAC;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/B5E,OAAA;UAAKyD,KAAK,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAE8B,QAAQ,EAAE,MAAM;YAAEJ,GAAG,EAAE,KAAK;YAAEf,SAAS,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAC9E/B,MAAM,CAAC4E,GAAG,CAAE5C,KAAK,iBAChBzD,OAAA;YAEE8E,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAACsC,KAAK,CAAE;YACjCA,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAEvE,aAAa,KAAKuC,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxEM,eAAe,EAAE7C,aAAa,KAAKuC,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEqC,KAAK,EAAE,MAAM;cACbjB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAhB,QAAA,EAEN9C,SAAS,CAAC,GAAG+C,KAAK,EAAE;UAAC,GAZlBA,KAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEN5E,OAAA;QAAAwD,QAAA,gBACNxD,OAAA;UAAAwD,QAAA,GAAK,GAAC,EAAC9C,SAAS,CAAC,QAAQ,CAAC;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChC5E,OAAA;UAAKyD,KAAK,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAE8B,QAAQ,EAAE,MAAM;YAAEJ,GAAG,EAAE,KAAK;YAAEf,SAAS,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAC9E9B,MAAM,CAAC2E,GAAG,CAAEP,KAAK,iBAChB9F,OAAA;YAEE8E,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACyE,KAAK,CAAE;YAEjCrC,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAErE,aAAa,KAAK0E,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxE/B,eAAe,EAAE3C,aAAa,KAAK0E,KAAK,GAAG,SAAS,GAAG,SAAS;cAChEA,KAAK,EAAE,MAAM;cACbjB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAhB,QAAA,EAGN9C,SAAS,CAAC,GAAGoF,KAAK,EAAE;UAAC,GAdlBA,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN5E,OAAA;QAAAwD,QAAA,gBACNxD,OAAA;UAAAwD,QAAA,EAAK9C,SAAS,CAAC,OAAO;QAAC;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B5E,OAAA;UAAKyD,KAAK,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAE8B,QAAQ,EAAE,MAAM;YAAEJ,GAAG,EAAE,KAAK;YAAEf,SAAS,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAClF7B,MAAM,CAAC0E,GAAG,CAAEC,KAAK,iBAChBtG,OAAA;YAEE8E,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAAC+E,KAAK,CAAE;YACvC7C,KAAK,EAAE;cACLU,OAAO,EAAE,UAAU;cACnBqB,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAEnE,aAAa,KAAKgF,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;cACxEvC,eAAe,EAAEzC,aAAa,KAAKgF,KAAK,GAAG,SAAS,GAAG,SAAS;cAChER,KAAK,EAAE,MAAM;cACbjB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAhB,QAAA,EAED8C;UAAK,GAZDA,KAAK;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5E,OAAA;QACE8E,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIhD,YAAY,EAAE;YAChB;YACAd,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuF,KAAK,CAAC,CAAC;YACxBxE,eAAe,CAAC,KAAK,CAAC;YACtBd,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM,IAAIW,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC;YACA;YACAI,aAAa,CAAC,CAAC;UACjB;QACF,CAAE;QACFwD,YAAY,EAAEA,CAAA,KAAMzF,YAAY,CAAC,IAAI,CAAE;QACvC0F,YAAY,EAAEA,CAAA,KAAM1F,YAAY,CAAC,KAAK,CAAE;QACxC4E,QAAQ,EAAE7D,YAAY,IAAI,CAACd,eAAgB;QAC3CyC,KAAK,EAAE;UACLwB,SAAS,EAAE,MAAM;UACjBpB,KAAK,EAAE,MAAM;UACbM,OAAO,EAAE,WAAW;UACpBqB,YAAY,EAAE,KAAK;UACnBzB,eAAe,EAAEjC,YAAY,GAAG,SAAS,GAAGF,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UAC7FkD,KAAK,EAAE,MAAM;UACbL,MAAM,EAAE,MAAM;UACdjB,QAAQ,EAAE,MAAM;UAChBK,MAAM,EAAE/C,YAAY,IAAIF,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,SAAS,GAAG,SAAS;UACzE0B,OAAO,EAAE,MAAM;UACfF,UAAU,EAAE,QAAQ;UACpBG,cAAc,EAAE,QAAQ;UACxByB,GAAG,EAAE,KAAK;UACV/B,UAAU,EAAE,uBAAuB;UACnCgC,OAAO,EAAErE,WAAW,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;QAC7C,CAAE;QAAAY,QAAA,EAED1B,YAAY,gBACX9B,OAAA,CAAAE,SAAA;UAAAsD,QAAA,gBACExD,OAAA;YACEyD,KAAK,EAAE;cACLI,KAAK,EAAE,MAAM;cACbD,MAAM,EAAE,MAAM;cACd6B,MAAM,EAAE,gBAAgB;cACxBS,SAAS,EAAE,uBAAuB;cAClCV,YAAY,EAAE,KAAK;cACnBW,SAAS,EAAE;YACb;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD9D,SAAS,GAAGJ,SAAS,CAAC,mBAAmB,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAC;QAAA,eACxE,CAAC,gBAEHV,OAAA,CAAAE,SAAA;UAAAsD,QAAA,gBACExD,OAAA;YAAM+E,uBAAuB,EAAE;cAAEC,MAAM,EAAE1F;YAAS;UAAE;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtDlE,SAAS,CAAC,gBAAgB,CAAC;QAAA,eAC5B;MACH;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGM;EAAC,gBAEV,CAAC;AAEP,CAAC;AAACpE,EAAA,CArUIL,oBAAoB;EAAA,QAICP,cAAc,EACdC,WAAW,EAGVC,cAAc;AAAA;AAAA4G,EAAA,GARpCvG,oBAAoB;AAuU1B,eAAeA,oBAAoB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}