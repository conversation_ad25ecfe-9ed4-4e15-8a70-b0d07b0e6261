{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Imagesection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport { magicPen } from '../../../assets/icons/icons';\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"./PopupSections.css\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport ImageGenerationPopup from \"./ImageGenerationPopup\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  setImageSrc,\n  setImageName,\n  onDelete,\n  onClone,\n  isCloneDisabled\n}) => {\n  _s();\n  var _imagesContainer$find;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadImage,\n    imagesContainer,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceImage,\n    cloneImageContainer,\n    deleteImageContainer,\n    updateImageContainer,\n    toggleFit,\n    setImageSrc: storeImageSrc\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const [openGenAiImagePopup, setOpenGenAiImagePopup] = useState(false);\n  const guidePopupRef = useRef(null);\n  const [popoverPositions, setPopoverPositions] = useState({\n    imagePopover: {},\n    settingsPopover: {}\n  });\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n\n      // Check for double extensions (e.g. file.html.png) or missing/invalid extension\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        setImageSrc(base64Image);\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n    setModelOpen(false);\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      setImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.FileName,\n          id: imageAnchorEl.buttonId,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files3;\n    const file = (_event$target$files3 = event.target.files) === null || _event$target$files3 === void 0 ? void 0 : _event$target$files3[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.name,\n          id: imageAnchorEl.buttonId,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    // @ts-ignore\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n  };\n  const open = Boolean(imageAnchorEl.value);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n    // setModelOpen(true);\n    // setReplaceImage(true);\n  };\n  const currentContainerColor = ((_imagesContainer$find = imagesContainer.find(item => item.id === imageAnchorEl.containerId)) === null || _imagesContainer$find === void 0 ? void 0 : _imagesContainer$find.style.backgroundColor) || \"transparent\";\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n\n    // Call the delete function from the store\n    deleteImageContainer(imageAnchorEl.containerId);\n\n    // Call the onDelete callback if provided\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n    cloneImageContainer(imageAnchorEl.containerId);\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n  const getImagePopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {};\n    const popoverHeight = 40;\n    const requiredGap = 10;\n    const minTopMargin = 8;\n    const viewportHeight = window.innerHeight;\n    const viewportWidth = window.innerWidth;\n    const popoverWidth = 500; // Static width for horizontal positioning only\n    const positionWithGap = guidePopupPos.top - popoverHeight - requiredGap;\n    let shouldUseAbovePositioning = true;\n    const bannerElement = document.querySelector('.qadpt-ext-banner');\n    if (bannerElement) {\n      const bannerRect = bannerElement.getBoundingClientRect();\n      const bannerBottom = bannerRect.bottom;\n      if (positionWithGap <= bannerBottom) {\n        shouldUseAbovePositioning = false;\n      }\n    }\n    if (shouldUseAbovePositioning && positionWithGap < minTopMargin) {\n      const spaceAboveGuidePopup = guidePopupPos.top;\n      if (spaceAboveGuidePopup < popoverHeight + requiredGap) {\n        shouldUseAbovePositioning = false;\n      }\n    }\n    let topPosition;\n    let isPositionedOnTop = false;\n    if (!shouldUseAbovePositioning) {\n      topPosition = guidePopupPos.top + requiredGap;\n      isPositionedOnTop = true;\n    } else if (positionWithGap >= minTopMargin) {\n      topPosition = positionWithGap;\n    } else {\n      const availableSpaceAbove = guidePopupPos.top - popoverHeight - minTopMargin;\n      const gaps = [15, 10, 5, 2];\n      const gap = gaps.find(g => availableSpaceAbove >= g) || 2;\n      topPosition = gap === 2 ? Math.max(minTopMargin, guidePopupPos.top - popoverHeight - 2) : guidePopupPos.top - popoverHeight - gap;\n    }\n    const maxTopPosition = viewportHeight - popoverHeight;\n    topPosition = Math.min(topPosition, maxTopPosition);\n    if (!isPositionedOnTop) {\n      topPosition = Math.max(topPosition, minTopMargin);\n    }\n    let leftPosition = guidePopupPos.left + guidePopupPos.width / 2 - 250;\n    leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - popoverWidth - 10));\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: 999999\n    };\n  };\n  const getSettingsPopoverPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return {};\n    const viewportHeight = window.innerHeight;\n    const viewportWidth = window.innerWidth;\n    const settingsPopupHeight = 200;\n    const settingsPopupWidth = 300;\n    let leftPosition = guidePopupPos.left + guidePopupPos.width + 10;\n    let topPosition = guidePopupPos.top + guidePopupPos.height / 2 - settingsPopupHeight / 2 + 10;\n    if (leftPosition + settingsPopupWidth > viewportWidth - 10) {\n      leftPosition = guidePopupPos.left - settingsPopupWidth - 10; // Position to the left instead\n    }\n    topPosition = Math.max(10, Math.min(topPosition, viewportHeight - settingsPopupHeight - 10));\n    leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - settingsPopupWidth - 10));\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: 999999\n    };\n  };\n  const updatePopoverPositions = () => {\n    setPopoverPositions({\n      imagePopover: getImagePopoverPosition(),\n      settingsPopover: getSettingsPopoverPosition()\n    });\n  };\n  useEffect(() => {\n    const handlePositionUpdate = () => updatePopoverPositions();\n    if (open || openSettingsPopover) {\n      updatePopoverPositions();\n      const positionCheckInterval = setInterval(() => {\n        if (open || openSettingsPopover) {\n          updatePopoverPositions();\n        } else {\n          clearInterval(positionCheckInterval);\n        }\n      }, 200);\n      window.qadptPositionCheckInterval = positionCheckInterval;\n    }\n    window.addEventListener('resize', handlePositionUpdate);\n    window.addEventListener('scroll', handlePositionUpdate);\n    const perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n    if (perfectScrollbarElement) {\n      perfectScrollbarElement.addEventListener('ps-scroll-y', handlePositionUpdate);\n      perfectScrollbarElement.addEventListener('scroll', handlePositionUpdate);\n    }\n    const elementsToObserve = [document.getElementById('guide-popup'), document.querySelector('.qadpt-guide-popup .MuiDialog-paper'), document.querySelector('.qadpt-guide-popup .ps')].filter(Boolean);\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (window.ResizeObserver && elementsToObserve.length > 0) {\n      resizeObserver = new ResizeObserver(entries => {\n        const hasSignificantChange = entries.some(entry => {\n          const {\n            width,\n            height\n          } = entry.contentRect;\n          const element = entry.target;\n          const lastWidth = parseFloat(element.dataset.lastWidth || '0');\n          const lastHeight = parseFloat(element.dataset.lastHeight || '0');\n          if (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {\n            element.dataset.lastWidth = width.toString();\n            element.dataset.lastHeight = height.toString();\n            return true;\n          }\n          return false;\n        });\n        if (hasSignificantChange) {\n          updatePopoverPositions();\n          setTimeout(updatePopoverPositions, 50);\n        }\n      });\n      elementsToObserve.forEach(element => resizeObserver.observe(element));\n    }\n    const guidePopupElement = elementsToObserve[0];\n    if (guidePopupElement && window.MutationObserver) {\n      mutationObserver = new MutationObserver(mutations => {\n        const shouldUpdate = mutations.some(mutation => {\n          return mutation.type === 'childList' || mutation.type === 'attributes' && ['style', 'class'].includes(mutation.attributeName || '');\n        });\n        if (shouldUpdate) {\n          setTimeout(updatePopoverPositions, 50);\n        }\n      });\n      mutationObserver.observe(guidePopupElement, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      var _resizeObserver, _mutationObserver;\n      window.removeEventListener('resize', handlePositionUpdate);\n      window.removeEventListener('scroll', handlePositionUpdate);\n      const perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\n      if (perfectScrollbarElement) {\n        perfectScrollbarElement.removeEventListener('ps-scroll-y', handlePositionUpdate);\n        perfectScrollbarElement.removeEventListener('scroll', handlePositionUpdate);\n      }\n      const intervalId = window.qadptPositionCheckInterval;\n      if (intervalId) {\n        clearInterval(intervalId);\n        delete window.qadptPositionCheckInterval;\n      }\n      (_resizeObserver = resizeObserver) === null || _resizeObserver === void 0 ? void 0 : _resizeObserver.disconnect();\n      (_mutationObserver = mutationObserver) === null || _mutationObserver === void 0 ? void 0 : _mutationObserver.disconnect();\n    };\n  }, [open, openSettingsPopover]);\n  const blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [imagesContainer.map(item => {\n      var _item$images$, _item$images$2, _item$images$3, _item$style;\n      const imageSrc = (_item$images$ = item.images[0]) === null || _item$images$ === void 0 ? void 0 : _item$images$.url;\n      const imageId = (_item$images$2 = item.images[0]) === null || _item$images$2 === void 0 ? void 0 : _item$images$2.id;\n      const objectFit = ((_item$images$3 = item.images[0]) === null || _item$images$3 === void 0 ? void 0 : _item$images$3.objectFit) || IMG_OBJECT_FIT;\n      const currentSecHeight = (item === null || item === void 0 ? void 0 : (_item$style = item.style) === null || _item$style === void 0 ? void 0 : _item$style.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n      const id = item.id;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-start\",\n          alignItems: \"center\",\n          margin: \"0px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: item.style.backgroundColor,\n            height: `${item.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n          },\n          children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 9\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: iconTextStyle,\n              component: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: uploadfile\n                },\n                style: {\n                  display: \"inline-block\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                align: \"center\",\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Upload file\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                fontSize: \"14px\"\n              },\n              children: translate(\"Drag & Drop to upload file\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                marginTop: \"8px\",\n                fontSize: \"14px\"\n              },\n              children: translate(\"Or\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 10\n            }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n              value: imageLink,\n              onChange: e => setImageLink(e.target.value),\n              onKeyDown: handleLinkSubmit,\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 11\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: iconRowStyle,\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      pointerEvents: \"auto\",\n                      cursor: \"pointer\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: hyperlink\n                      },\n                      style: {\n                        color: \"black\",\n                        cursor: \"pointer\",\n                        fontSize: \"32px\",\n                        opacity: \"0.5\",\n                        pointerEvents: \"none\"\n                      },\n                      id: \"hyperlink\",\n                      className: \"qadpt-image-upload\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 5\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 3\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    onClick: event => {\n                      //setModelOpen(true);\n                    },\n                    dangerouslySetInnerHTML: {\n                      __html: files\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\",\n                      opacity: \"0.5\"\n                    },\n                    id: \"folder\",\n                    className: \"qadpt-image-upload\"\n                    //title=\"Coming Soon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Upload File\"),\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    onClick: event => {\n                      var _document$getElementB2;\n                      event === null || event === void 0 ? void 0 : event.stopPropagation();\n                      (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n                    },\n                    id: \"file-upload1\",\n                    className: \"qadpt-image-upload\",\n                    dangerouslySetInnerHTML: {\n                      __html: uploadicon\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 14\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  id: \"file-upload\",\n                  style: {\n                    display: \"none\"\n                  },\n                  accept: \"image/*\",\n                  onChange: handleImageUpload\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n                  open: snackbarOpen,\n                  autoHideDuration: 3000,\n                  onClose: closeSnackbar,\n                  anchorOrigin: {\n                    vertical: 'bottom',\n                    horizontal: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Alert, {\n                    onClose: closeSnackbar,\n                    severity: snackbarSeverity,\n                    sx: {\n                      width: '100%'\n                    },\n                    children: snackbarMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 13\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-genai-container\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: event => {\n                    event.stopPropagation(); // Prevent the parent onClick from firing\n\n                    // Set up the imageAnchorEl context for AI generation\n                    setImageAnchorEl({\n                      buttonId: '',\n                      // Empty for new image generation\n                      containerId: id,\n                      // Use the current container ID\n                      value: null\n                    });\n                    setReplaceImage(false); // This is for new image, not replacement\n                    setOpenGenAiImagePopup(true);\n                  },\n                  className: \"qadpt-genai-btn\",\n                  children: translate(\"Generate With AI\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 7\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 6\n      }, this);\n    }), Boolean(imageAnchorEl.value) ? /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-imgsec-popover\",\n      id: id,\n      open: open,\n      anchorEl: null,\n      onClose: handleClose,\n      anchorReference: \"none\",\n      slotProps: {\n        paper: {\n          style: {\n            ...popoverPositions.imagePopover,\n            height: 'auto',\n            width: 'auto',\n            padding: '5px 10px',\n            marginLeft: 'auto',\n            marginRight: 'auto'\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"15px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"6px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: replaceimageicon\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            sx: {\n              cursor: \"pointer\"\n            },\n            onClick: triggerImageUpload,\n            children: translate(\"Replace\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: \"replace-upload\",\n            style: {\n              display: \"none\"\n            },\n            accept: \"image/*\",\n            onChange: handleReplaceImage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Generated Image with AI\"),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => {\n                var _imagesContainer$find2, _imagesContainer$find3, _imagesContainer$find4;\n                setImageAnchorEl({\n                  buttonId: currentImageSectionInfo.currentContainerId ? (_imagesContainer$find2 = (_imagesContainer$find3 = imagesContainer.find(c => c.id === currentImageSectionInfo.currentContainerId)) === null || _imagesContainer$find3 === void 0 ? void 0 : (_imagesContainer$find4 = _imagesContainer$find3.images[0]) === null || _imagesContainer$find4 === void 0 ? void 0 : _imagesContainer$find4.id) !== null && _imagesContainer$find2 !== void 0 ? _imagesContainer$find2 : '' : '',\n                  containerId: currentImageSectionInfo.currentContainerId,\n                  value: null\n                });\n                setReplaceImage(true);\n                setOpenGenAiImagePopup(true);\n              },\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: \"20px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: blackMagicPen\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 7\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 5\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 3\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            },\n            style: {\n              display: \"flex\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 1\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              className: \"qadpt-imgset\",\n              open: openSettingsPopover,\n              anchorEl: null,\n              onClose: handleCloseSettingsPopover,\n              anchorReference: \"none\",\n              slotProps: {\n                paper: {\n                  style: {\n                    ...popoverPositions.settingsPopover,\n                    width: \"205px\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 903,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 904,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 905,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 906,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 907,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 888,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 880,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      // Get current image's objectFit to determine selected state\n                      const currentContainer = imagesContainer.find(c => c.id === imageAnchorEl.containerId);\n                      const currentImage = currentContainer === null || currentContainer === void 0 ? void 0 : currentContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n\n                      // Determine if this button should be selected\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item)\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 934,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 918,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: selectedColor,\n                  borderRadius: \"100%\",\n                  width: \"20px\",\n                  height: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 971,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 5\n    }, this) : null, isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp,\n      setFormOfUpload: setFormOfUpload,\n      formOfUpload: formOfUpload,\n      handleReplaceImage: handleReplaceImage,\n      isReplaceImage: isReplaceImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1019,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1035,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1022,\n      columnNumber: 4\n    }, this), openGenAiImagePopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(ImageGenerationPopup, {\n        openGenAiImagePopup: openGenAiImagePopup,\n        setOpenGenAiImagePopup: setOpenGenAiImagePopup,\n        handleImageUploadFormApp: handleImageUploadFormApp,\n        setReplaceImage: setReplaceImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 7\n      }, this)\n    }, void 0, false)]\n  }, void 0, true);\n};\n_s(ImageSection, \"Wegg3onExz9cOW0zXX5Kh2IBgXw=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "magicPen", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "ImageGenerationPopup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "_s", "_imagesContainer$find", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "openGenAiImagePopup", "setOpenGenAiImagePopup", "guidePopupRef", "popoverPositions", "setPopoverPositions", "imagePopover", "settingsPopover", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "getGuidePopupPosition", "element", "querySelector", "rect", "getBoundingClientRect", "top", "left", "getImagePopoverPosition", "guidePopupPos", "popoverHeight", "requiredGap", "minTopMargin", "viewportHeight", "window", "innerHeight", "viewportWidth", "innerWidth", "popoverWidth", "positionWithGap", "shouldUseAbovePositioning", "bannerElement", "bannerRect", "bannerBottom", "bottom", "spaceAboveGuidePopup", "topPosition", "isPositionedOnTop", "availableSpaceAbove", "gaps", "g", "maxTopPosition", "leftPosition", "position", "zIndex", "getSettingsPopoverPosition", "settingsPopupHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePopoverPositions", "handlePositionUpdate", "positionCheckInterval", "setInterval", "clearInterval", "qadptPositionCheckInterval", "addEventListener", "perfectScrollbarElement", "elementsToObserve", "filter", "resizeObserver", "mutationObserver", "ResizeObserver", "entries", "hasSignificantChange", "some", "entry", "contentRect", "lastWidth", "parseFloat", "dataset", "lastHeight", "abs", "toString", "setTimeout", "for<PERSON>ach", "observe", "guidePopupElement", "MutationObserver", "mutations", "shouldUpdate", "mutation", "type", "attributeName", "childList", "subtree", "attributes", "attributeFilter", "_resizeObserver", "_mutationObserver", "removeEventListener", "intervalId", "disconnect", "blackMagicPen", "replace", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "anchorReference", "slotProps", "paper", "marginLeft", "marginRight", "_imagesContainer$find2", "_imagesContainer$find3", "_imagesContainer$find4", "c", "size", "disabled", "p", "mt", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect", "transform<PERSON><PERSON>in", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n\t<PERSON>,\r\n\tTypography,\r\n\tPopover,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert,\r\n} from \"@mui/material\";\r\nimport {  magicPen } from '../../../assets/icons/icons';\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport CachedIcon from '@mui/icons-material/Cached';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\nimport ImageGenerationPopup from \"./ImageGenerationPopup\"\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\tconst [openGenAiImagePopup, setOpenGenAiImagePopup] = useState(false);\r\n\tconst guidePopupRef = useRef<HTMLElement | null>(null);\r\n\tconst [popoverPositions, setPopoverPositions] = useState({\r\n\t\timagePopover: {},\r\n\t\tsettingsPopover: {}\r\n\t});\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n   \t\t // Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t\t// setModelOpen(true);\r\n\t\t// setReplaceImage(true);\r\n\t};\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Call the delete function from the store\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\tconst handleCloneImgContainer = () => {\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\tconst getGuidePopupPosition = () => {\r\n\t\tconst element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n\t\t\t\t\t\tdocument.getElementById('guide-popup');\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top,\r\n\t\t\t\tleft: rect.left,\r\n\t\t\t\twidth: rect.width,\r\n\t\t\t\theight: rect.height\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\tconst getImagePopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return {};\r\n\t\tconst popoverHeight = 40;\r\n\t\tconst requiredGap = 10;\r\n\t\tconst minTopMargin = 8;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst popoverWidth = 500; // Static width for horizontal positioning only\r\n\t\tconst positionWithGap = guidePopupPos.top - popoverHeight - requiredGap;\r\n\t\tlet shouldUseAbovePositioning = true;\r\n\t\tconst bannerElement = document.querySelector('.qadpt-ext-banner') as HTMLElement;\r\n\t\tif (bannerElement) {\r\n\t\t\tconst bannerRect = bannerElement.getBoundingClientRect();\r\n\t\t\tconst bannerBottom = bannerRect.bottom;\r\n\t\t\tif (positionWithGap <= bannerBottom) {\r\n\t\t\t\tshouldUseAbovePositioning = false;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (shouldUseAbovePositioning && positionWithGap < minTopMargin) {\r\n\t\t\tconst spaceAboveGuidePopup = guidePopupPos.top;\r\n\t\t\tif (spaceAboveGuidePopup < (popoverHeight + requiredGap)) {\r\n\t\t\t\tshouldUseAbovePositioning = false;\r\n\t\t\t}\r\n\t\t}\r\n\t\tlet topPosition;\r\n\t\tlet isPositionedOnTop = false;\r\n\t\tif (!shouldUseAbovePositioning) {\r\n\t\t\ttopPosition = guidePopupPos.top + requiredGap;\r\n\t\t\tisPositionedOnTop = true;\r\n\t\t} else if (positionWithGap >= minTopMargin) {\r\n\t\t\ttopPosition = positionWithGap;\r\n\t\t} else {\r\n\t\t\tconst availableSpaceAbove = guidePopupPos.top - popoverHeight - minTopMargin;\r\n\t\t\tconst gaps = [15, 10, 5, 2];\r\n\t\t\tconst gap = gaps.find(g => availableSpaceAbove >= g) || 2;\r\n\t\t\ttopPosition = gap === 2\r\n\t\t\t\t? Math.max(minTopMargin, guidePopupPos.top - popoverHeight - 2)\r\n\t\t\t\t: guidePopupPos.top - popoverHeight - gap;\r\n\t\t}\r\n\t\tconst maxTopPosition = viewportHeight - popoverHeight;\r\n\t\ttopPosition = Math.min(topPosition, maxTopPosition);\r\n\t\tif (!isPositionedOnTop) {\r\n\t\t\ttopPosition = Math.max(topPosition, minTopMargin);\r\n\t\t}\r\n\t\tlet leftPosition = guidePopupPos.left + (guidePopupPos.width / 2) - 250;\r\n\t\tleftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - popoverWidth - 10));\r\n\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: 999999\r\n\t\t};\r\n\t};\r\n\tconst getSettingsPopoverPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return {};\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst settingsPopupHeight = 200; \r\n\t\tconst settingsPopupWidth = 300; \r\n\t\tlet leftPosition = guidePopupPos.left + guidePopupPos.width + 10; \r\n\t\tlet topPosition = guidePopupPos.top + (guidePopupPos.height / 2) - (settingsPopupHeight/2) + 10;\r\n\t\tif (leftPosition + settingsPopupWidth > viewportWidth - 10) {\r\n\t\t\tleftPosition = guidePopupPos.left - settingsPopupWidth - 10; // Position to the left instead\r\n\t\t}\r\n\t\ttopPosition = Math.max(10, Math.min(topPosition, viewportHeight - settingsPopupHeight - 10));\r\n\t\tleftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - settingsPopupWidth - 10));\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: 999999\r\n\t\t};\r\n\t};\r\n\tconst updatePopoverPositions = () => {\r\n\t\tsetPopoverPositions({\r\n\t\t\timagePopover: getImagePopoverPosition(),\r\n\t\t\tsettingsPopover: getSettingsPopoverPosition()\r\n\t\t});\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst handlePositionUpdate = () => updatePopoverPositions();\r\n\t\tif (open || openSettingsPopover) {\r\n\t\t\tupdatePopoverPositions();\r\n\t\t\tconst positionCheckInterval = setInterval(() => {\r\n\t\t\t\tif (open || openSettingsPopover) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tclearInterval(positionCheckInterval);\r\n\t\t\t\t}\r\n\t\t\t}, 200); \r\n\t\t\t(window as any).qadptPositionCheckInterval = positionCheckInterval;\r\n\t\t}\r\n\t\twindow.addEventListener('resize', handlePositionUpdate);\r\n\t\twindow.addEventListener('scroll', handlePositionUpdate);\r\n\t\tconst perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\t\tif (perfectScrollbarElement) {\r\n\t\t\tperfectScrollbarElement.addEventListener('ps-scroll-y', handlePositionUpdate);\r\n\t\t\tperfectScrollbarElement.addEventListener('scroll', handlePositionUpdate);\r\n\t\t}\r\n\t\tconst elementsToObserve = [\r\n\t\t\tdocument.getElementById('guide-popup'),\r\n\t\t\tdocument.querySelector('.qadpt-guide-popup .MuiDialog-paper'),\r\n\t\t\tdocument.querySelector('.qadpt-guide-popup .ps')\r\n\t\t].filter(Boolean) as HTMLElement[];\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\t\tif (window.ResizeObserver && elementsToObserve.length > 0) {\r\n\t\t\tresizeObserver = new ResizeObserver((entries) => {\r\n\t\t\t\tconst hasSignificantChange = entries.some((entry) => {\r\n\t\t\t\t\tconst { width, height } = entry.contentRect;\r\n\t\t\t\t\tconst element = entry.target as HTMLElement;\r\n\t\t\t\t\tconst lastWidth = parseFloat(element.dataset.lastWidth || '0');\r\n\t\t\t\t\tconst lastHeight = parseFloat(element.dataset.lastHeight || '0');\r\n\t\t\t\t\tif (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {\r\n\t\t\t\t\t\telement.dataset.lastWidth = width.toString();\r\n\t\t\t\t\t\telement.dataset.lastHeight = height.toString();\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t});\r\n\t\t\t\tif (hasSignificantChange) {\r\n\t\t\t\t\tupdatePopoverPositions();\r\n\t\t\t\t\tsetTimeout(updatePopoverPositions, 50);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\telementsToObserve.forEach(element => resizeObserver!.observe(element));\r\n\t\t}\r\n\t\tconst guidePopupElement = elementsToObserve[0]; \r\n\t\tif (guidePopupElement && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver((mutations) => {\r\n\t\t\t\tconst shouldUpdate = mutations.some((mutation) => {\r\n\t\t\t\t\treturn mutation.type === 'childList' ||\r\n\t\t\t\t\t\t(mutation.type === 'attributes' &&\r\n\t\t\t\t\t\t ['style', 'class'].includes(mutation.attributeName || ''));\r\n\t\t\t\t});\r\n\t\t\t\tif (shouldUpdate) {\r\n\t\t\t\t\tsetTimeout(updatePopoverPositions, 50); \r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(guidePopupElement, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', handlePositionUpdate);\r\n\t\t\twindow.removeEventListener('scroll', handlePositionUpdate);\r\n\t\t\tconst perfectScrollbarElement = document.querySelector('.qadpt-guide-popup .ps');\r\n\t\t\tif (perfectScrollbarElement) {\r\n\t\t\t\tperfectScrollbarElement.removeEventListener('ps-scroll-y', handlePositionUpdate);\r\n\t\t\t\tperfectScrollbarElement.removeEventListener('scroll', handlePositionUpdate);\r\n\t\t\t}\r\n\t\t\tconst intervalId = (window as any).qadptPositionCheckInterval;\r\n\t\t\tif (intervalId) {\r\n\t\t\t\tclearInterval(intervalId);\r\n\t\t\t\tdelete (window as any).qadptPositionCheckInterval;\r\n\t\t\t}\r\n\t\t\tresizeObserver?.disconnect();\r\n\t\t\tmutationObserver?.disconnect();\r\n\t\t};\r\n\t}, [open, openSettingsPopover]);\r\n\r\n\tconst blackMagicPen = magicPen.replace(/stroke=\"white\"/g, 'stroke=\"black\"');\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box >\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t className=\"qadpt-genai-container\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\tevent.stopPropagation(); // Prevent the parent onClick from firing\r\n\r\n\t\t\t\t\t\t\t\t\t\t// Set up the imageAnchorEl context for AI generation\r\n\t\t\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\t\t\tbuttonId: '', // Empty for new image generation\r\n\t\t\t\t\t\t\t\t\t\t\tcontainerId: id, // Use the current container ID\r\n\t\t\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\tsetReplaceImage(false); // This is for new image, not replacement\r\n\t\t\t\t\t\t\t\t\t\tsetOpenGenAiImagePopup(true);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-genai-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t {translate(\"Generate With AI\")}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t...popoverPositions.imagePopover,\r\n\t\t\t\t\t\t\t\theight: 'auto',\r\n\t\t\t\t\t\t\t\twidth: 'auto',\r\n\t\t\t\t\t\t\t\tpadding: '5px 10px',\r\n\t\t\t\t\t\t\t\tmarginLeft: 'auto',\r\n\t\t\t\t\t\t\t\tmarginRight: 'auto',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"15px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n  className=\"qadpt-tool-items\"\r\n  sx={{ display: \"flex\", alignItems: \"center\", gap: \"6px\" }}\r\n>\r\n  <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n  <Typography\r\n    fontSize=\"12px\"\r\n    sx={{ cursor: \"pointer\" }}\r\n    onClick={triggerImageUpload}\r\n  >\r\n    {translate(\"Replace\")}\r\n  </Typography>\r\n  <input\r\n    type=\"file\"\r\n    id=\"replace-upload\"\r\n    style={{ display: \"none\" }}\r\n    accept=\"image/*\"\r\n    onChange={handleReplaceImage}\r\n  />\r\n  <Tooltip title={translate(\"Generated Image with AI\")}>\r\n    <IconButton\r\n      onClick={() => {\r\n        setImageAnchorEl({\r\n          buttonId: currentImageSectionInfo.currentContainerId\r\n            ? imagesContainer.find(c => c.id === currentImageSectionInfo.currentContainerId)?.images[0]?.id ?? ''\r\n            : '',\r\n          containerId: currentImageSectionInfo.currentContainerId,\r\n          value: null,\r\n        });\r\n        setReplaceImage(true);\r\n        setOpenGenAiImagePopup(true);\r\n      }}\r\n      size=\"small\"\r\n    >\r\n      <span style={{fontSize:\"20px\"}} dangerouslySetInnerHTML={{ __html: blackMagicPen }} />\r\n    </IconButton>\r\n  </Tooltip>\r\n</Box>\r\n\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }}\r\n\t\t\t\t\t\t\tstyle={{ display: \"flex\" }}/>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\"}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={null}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorReference=\"none\"\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\t\t\t\t\t\t...popoverPositions.settingsPopover,\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")}>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\r\n\t\t\t{\r\n\t\t\t\topenGenAiImagePopup && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<ImageGenerationPopup openGenAiImagePopup={openGenAiImagePopup} setOpenGenAiImagePopup={setOpenGenAiImagePopup} handleImageUploadFormApp={handleImageUploadFormApp} setReplaceImage={setReplaceImage} />\r\n\t\t\t\t\t</>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACCC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACC,eAAe;AACtB,SAAUC,QAAQ,QAAQ,6BAA6B;AACvD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAK9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,QACR,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,qBAAqB;AAC5B,OAAOC,0BAA0B,MAAM,yCAAyC;AAChF,OAAOC,oBAAoB,MAAM,wBAAwB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLkC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,SAAS;IACTjB,WAAW,EAAEkB;EACd,CAAC,GAAGhC,cAAc,CAAEiC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAMoE,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAkD;IAC7GyE,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC6E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9E,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC+E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGhF,QAAQ,CAInE;IAAEyE,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEvD;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACqF,WAAW,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACyF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1F,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAAC2F,aAAa,EAAEC,gBAAgB,CAAC,GAAG5F,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAAC6F,cAAc,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMiG,aAAa,GAAGhG,MAAM,CAAqB,IAAI,CAAC;EACtD,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC;IACxDoG,YAAY,EAAE,CAAC,CAAC;IAChBC,eAAe,EAAE,CAAC;EACnB,CAAC,CAAC;EAGF,MAAMC,mBAAmB,GAAGC,OAAO,CAACd,gBAAgB,CAAC;EAErD,MAAMe,kBAAkB,GAAIC,KAAU,IAAK;IAC1CrB,iBAAiB,CAACqB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrEf,mBAAmB,CAACe,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxCpB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMqB,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACb9B,MAAM,EAAE,MAAM;IACd+B,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCP,KAAK,EAAE,MAAM;IACb9B,MAAM,EAAE,MAAM;IACdmC,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBF,KAAK,EAAE;EACR,CAAC;EAED,MAAMc,iBAAiB,GAAIrB,KAA0C,IAAK;IAAA,IAAAsB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGtB,KAAK,CAACC,MAAM,CAACxF,KAAK,cAAA6G,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MAAA,IAAAC,oBAAA;MACT,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;;MAE7B;MACC,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvCtE,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrB4C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BxE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrB4C,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACDlE,YAAY,EAAAwF,oBAAA,GAACxB,KAAK,CAACC,MAAM,CAACxF,KAAK,cAAA+G,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC;MAE1C,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3ClF,aAAa,CAACiF,WAAW,CAAC;QAC1BnG,WAAW,CAACmG,WAAW,CAAC;QACxB1F,WAAW,CAACE,aAAa,CAAC0F,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEP,WAAW;UAChBQ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEtH;QACZ,CAAC,CAAC;MACH,CAAC;MACD0G,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;IACA1C,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMgE,wBAAwB,GAAItB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACTtE,aAAa,CAACsE,IAAI,CAACuB,GAAG,CAAC;MACvB/G,WAAW,CAACwF,IAAI,CAACuB,GAAG,CAAC;MACrB,IAAI1D,cAAc,EAAE;QACnBxC,YAAY,CAACF,aAAa,CAAC0F,WAAW,EAAE1F,aAAa,CAACqG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAE5F,aAAa,CAACqG,QAAQ;UAC1BN,GAAG,EAAElB,IAAI,CAACuB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEtH;QACZ,CAAC,CAAC;QACFgE,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACN7C,WAAW,CAACE,aAAa,CAAC0F,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAElB,IAAI,CAACuB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEtH;QACZ,CAAC,CAAC;MACH;IACD;IACAwD,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,MAAMoE,kBAAkB,GAAIjD,KAA0C,IAAK;IAAA,IAAAkD,oBAAA;IAC1E,MAAM3B,IAAI,IAAA2B,oBAAA,GAAGlD,KAAK,CAACC,MAAM,CAACxF,KAAK,cAAAyI,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI3B,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxBrF,YAAY,CAACF,aAAa,CAAC0F,WAAW,EAAE1F,aAAa,CAACqG,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAE5F,aAAa,CAACqG,QAAQ;UAC1BN,GAAG,EAAEV,MAAM,CAACI,MAAM;UAClBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEtH;QACZ,CAAC,CAAC;MACH,CAAC;MACD0G,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM4B,WAAW,GAAGA,CACnBnD,KAAoC,EACpCoC,WAAmB,EACnBgB,OAAe,EACf5E,OAAgB,EAChB6E,aAAqB,KACjB;IACJ;IACA,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,EAAE;IAC5D3F,gBAAgB,CAAC;MAChBoG,QAAQ,EAAEK,OAAO;MACjBhB,WAAW,EAAEA,WAAW;MACxB;MACAlC,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFnB,mBAAmB,CAAC,IAAI,CAAC;IACzBV,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAEoE,WAAW;MAC/B5D,OAAO;MACPC,MAAM,EAAE4E;IACT,CAAC,CAAC;IACFtF,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAMsF,WAAW,GAAGA,CAAA,KAAM;IACzB5G,gBAAgB,CAAC;MAChBoG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EAED,MAAMsD,IAAI,GAAG1D,OAAO,CAACpD,aAAa,CAACwD,KAAK,CAAC;EACzC,MAAMuD,eAAe,GAAG3D,OAAO,CAAC1B,mBAAmB,CAAC;EAEpD,MAAMkE,EAAE,GAAGkB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIzI,wBAAwB,EAAE;IAC5C,MAAM0I,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAGtI,cAAc,EAAEH,wBAAwB,CAAC;IACjF4B,oBAAoB,CAACL,aAAa,CAAC0F,WAAW,EAAE,OAAO,EAAE;MACxD3D,MAAM,EAAEoF;IACT,CAAC,CAAC;IACFtF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEoF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIxI,wBAAwB,EAAE;IAC5C,MAAMyI,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAGtI,cAAc,EAAEF,wBAAwB,CAAC;IACjF2B,oBAAoB,CAACL,aAAa,CAAC0F,WAAW,EAAE,OAAO,EAAE;MACxD3D,MAAM,EAAEoF;IACT,CAAC,CAAC;IACFtF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAEoF;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EACD,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;IAClD;IACA;EACD,CAAC;EACD,MAAMC,qBAAqB,GAC1B,EAAAlI,qBAAA,GAAAI,eAAe,CAAC+H,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAK5F,aAAa,CAAC0F,WAAW,CAAC,cAAA/F,qBAAA,uBAArEA,qBAAA,CAAuEqI,KAAK,CAAChC,eAAe,KAAI,aAAa;EAC9G;EACA,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IACjChI,gBAAgB,CAAC;MAChBoG,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACApD,oBAAoB,CAACJ,aAAa,CAAC0F,WAAW,CAAC;;IAE/C;IACA,IAAInG,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EACD,MAAM2I,gBAAgB,GAAI5E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC6E,GAAG,KAAK,OAAO,IAAI3G,SAAS,EAAE;MACvC1B,WAAW,CAACE,aAAa,CAAC0F,WAAW,EAAE;QACtCC,OAAO,EAAE,WAAW;QACpBC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAEvE,SAAS;QACdwE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAEtH;MACZ,CAAC,CAAC;MACF0C,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EACD,MAAM6G,uBAAuB,GAAGA,CAAA,KAAM;IACrC,IAAI3I,eAAe,EAAE;MACpB,OAAO,CAAC;IACT;IACAU,mBAAmB,CAACH,aAAa,CAAC0F,WAAW,CAAC;IAC9C,IAAIlG,OAAO,EAAE;MACZA,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EACD,MAAM6I,sBAAsB,GAAGA,CAAA,KAAM;IACpC1G,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD,MAAM2G,iBAAiB,GAAIC,KAAkB,IAAK;IACjD9F,gBAAgB,CAAC8F,KAAK,CAACC,GAAG,CAAC;IAC3BnI,oBAAoB,CAACL,aAAa,CAAC0F,WAAW,EAAE,OAAO,EAAE;MACxDM,eAAe,EAAEuC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EACD,MAAMC,0BAA0B,GAAInF,KAAoC,IAAK;IAC5E3B,sBAAsB,CAAC2B,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EACD,MAAMgF,qBAAqB,GAAGA,CAAA,KAAM;IACnC,MAAMC,OAAO,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,qCAAqC,CAAC,IACzElB,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC1C,IAAIgB,OAAO,EAAE;MACZ,MAAME,IAAI,GAAGF,OAAO,CAACG,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfnF,KAAK,EAAEgF,IAAI,CAAChF,KAAK;QACjB9B,MAAM,EAAE8G,IAAI,CAAC9G;MACd,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EACD,MAAMkH,uBAAuB,GAAGA,CAAA,KAAM;IACrC,MAAMC,aAAa,GAAGR,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACQ,aAAa,EAAE,OAAO,CAAC,CAAC;IAC7B,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,YAAY,GAAG,CAAC;IACtB,MAAMC,cAAc,GAAGC,MAAM,CAACC,WAAW;IACzC,MAAMC,aAAa,GAAGF,MAAM,CAACG,UAAU;IACvC,MAAMC,YAAY,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAMC,eAAe,GAAGV,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAGC,WAAW;IACvE,IAAIS,yBAAyB,GAAG,IAAI;IACpC,MAAMC,aAAa,GAAGpC,QAAQ,CAACkB,aAAa,CAAC,mBAAmB,CAAgB;IAChF,IAAIkB,aAAa,EAAE;MAClB,MAAMC,UAAU,GAAGD,aAAa,CAAChB,qBAAqB,CAAC,CAAC;MACxD,MAAMkB,YAAY,GAAGD,UAAU,CAACE,MAAM;MACtC,IAAIL,eAAe,IAAII,YAAY,EAAE;QACpCH,yBAAyB,GAAG,KAAK;MAClC;IACD;IACA,IAAIA,yBAAyB,IAAID,eAAe,GAAGP,YAAY,EAAE;MAChE,MAAMa,oBAAoB,GAAGhB,aAAa,CAACH,GAAG;MAC9C,IAAImB,oBAAoB,GAAIf,aAAa,GAAGC,WAAY,EAAE;QACzDS,yBAAyB,GAAG,KAAK;MAClC;IACD;IACA,IAAIM,WAAW;IACf,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAI,CAACP,yBAAyB,EAAE;MAC/BM,WAAW,GAAGjB,aAAa,CAACH,GAAG,GAAGK,WAAW;MAC7CgB,iBAAiB,GAAG,IAAI;IACzB,CAAC,MAAM,IAAIR,eAAe,IAAIP,YAAY,EAAE;MAC3Cc,WAAW,GAAGP,eAAe;IAC9B,CAAC,MAAM;MACN,MAAMS,mBAAmB,GAAGnB,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAGE,YAAY;MAC5E,MAAMiB,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAC3B,MAAM/F,GAAG,GAAG+F,IAAI,CAACxC,IAAI,CAACyC,CAAC,IAAIF,mBAAmB,IAAIE,CAAC,CAAC,IAAI,CAAC;MACzDJ,WAAW,GAAG5F,GAAG,KAAK,CAAC,GACpB6C,IAAI,CAACG,GAAG,CAAC8B,YAAY,EAAEH,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAG,CAAC,CAAC,GAC7DD,aAAa,CAACH,GAAG,GAAGI,aAAa,GAAG5E,GAAG;IAC3C;IACA,MAAMiG,cAAc,GAAGlB,cAAc,GAAGH,aAAa;IACrDgB,WAAW,GAAG/C,IAAI,CAACC,GAAG,CAAC8C,WAAW,EAAEK,cAAc,CAAC;IACnD,IAAI,CAACJ,iBAAiB,EAAE;MACvBD,WAAW,GAAG/C,IAAI,CAACG,GAAG,CAAC4C,WAAW,EAAEd,YAAY,CAAC;IAClD;IACA,IAAIoB,YAAY,GAAGvB,aAAa,CAACF,IAAI,GAAIE,aAAa,CAACrF,KAAK,GAAG,CAAE,GAAG,GAAG;IACvE4G,YAAY,GAAGrD,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAACoD,YAAY,EAAEhB,aAAa,GAAGE,YAAY,GAAG,EAAE,CAAC,CAAC;IAEtF,OAAO;MACNZ,GAAG,EAAEoB,WAAW;MAChBnB,IAAI,EAAEyB,YAAY;MAClBC,QAAQ,EAAE,OAAgB;MAC1BC,MAAM,EAAE;IACT,CAAC;EACF,CAAC;EACD,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxC,MAAM1B,aAAa,GAAGR,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACQ,aAAa,EAAE,OAAO,CAAC,CAAC;IAC7B,MAAMI,cAAc,GAAGC,MAAM,CAACC,WAAW;IACzC,MAAMC,aAAa,GAAGF,MAAM,CAACG,UAAU;IACvC,MAAMmB,mBAAmB,GAAG,GAAG;IAC/B,MAAMC,kBAAkB,GAAG,GAAG;IAC9B,IAAIL,YAAY,GAAGvB,aAAa,CAACF,IAAI,GAAGE,aAAa,CAACrF,KAAK,GAAG,EAAE;IAChE,IAAIsG,WAAW,GAAGjB,aAAa,CAACH,GAAG,GAAIG,aAAa,CAACnH,MAAM,GAAG,CAAE,GAAI8I,mBAAmB,GAAC,CAAE,GAAG,EAAE;IAC/F,IAAIJ,YAAY,GAAGK,kBAAkB,GAAGrB,aAAa,GAAG,EAAE,EAAE;MAC3DgB,YAAY,GAAGvB,aAAa,CAACF,IAAI,GAAG8B,kBAAkB,GAAG,EAAE,CAAC,CAAC;IAC9D;IACAX,WAAW,GAAG/C,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAAC8C,WAAW,EAAEb,cAAc,GAAGuB,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAC5FJ,YAAY,GAAGrD,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACC,GAAG,CAACoD,YAAY,EAAEhB,aAAa,GAAGqB,kBAAkB,GAAG,EAAE,CAAC,CAAC;IAC5F,OAAO;MACN/B,GAAG,EAAEoB,WAAW;MAChBnB,IAAI,EAAEyB,YAAY;MAClBC,QAAQ,EAAE,OAAgB;MAC1BC,MAAM,EAAE;IACT,CAAC;EACF,CAAC;EACD,MAAMI,sBAAsB,GAAGA,CAAA,KAAM;IACpC/H,mBAAmB,CAAC;MACnBC,YAAY,EAAEgG,uBAAuB,CAAC,CAAC;MACvC/F,eAAe,EAAE0H,0BAA0B,CAAC;IAC7C,CAAC,CAAC;EACH,CAAC;EACDhO,SAAS,CAAC,MAAM;IACf,MAAMoO,oBAAoB,GAAGA,CAAA,KAAMD,sBAAsB,CAAC,CAAC;IAC3D,IAAIjE,IAAI,IAAI3D,mBAAmB,EAAE;MAChC4H,sBAAsB,CAAC,CAAC;MACxB,MAAME,qBAAqB,GAAGC,WAAW,CAAC,MAAM;QAC/C,IAAIpE,IAAI,IAAI3D,mBAAmB,EAAE;UAChC4H,sBAAsB,CAAC,CAAC;QACzB,CAAC,MAAM;UACNI,aAAa,CAACF,qBAAqB,CAAC;QACrC;MACD,CAAC,EAAE,GAAG,CAAC;MACN1B,MAAM,CAAS6B,0BAA0B,GAAGH,qBAAqB;IACnE;IACA1B,MAAM,CAAC8B,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACvDzB,MAAM,CAAC8B,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACvD,MAAMM,uBAAuB,GAAG5D,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;IAChF,IAAI0C,uBAAuB,EAAE;MAC5BA,uBAAuB,CAACD,gBAAgB,CAAC,aAAa,EAAEL,oBAAoB,CAAC;MAC7EM,uBAAuB,CAACD,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IACzE;IACA,MAAMO,iBAAiB,GAAG,CACzB7D,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,EACtCD,QAAQ,CAACkB,aAAa,CAAC,qCAAqC,CAAC,EAC7DlB,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC,CAChD,CAAC4C,MAAM,CAACpI,OAAO,CAAkB;IAClC,IAAIqI,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IACpD,IAAInC,MAAM,CAACoC,cAAc,IAAIJ,iBAAiB,CAACnG,MAAM,GAAG,CAAC,EAAE;MAC1DqG,cAAc,GAAG,IAAIE,cAAc,CAAEC,OAAO,IAAK;QAChD,MAAMC,oBAAoB,GAAGD,OAAO,CAACE,IAAI,CAAEC,KAAK,IAAK;UACpD,MAAM;YAAElI,KAAK;YAAE9B;UAAO,CAAC,GAAGgK,KAAK,CAACC,WAAW;UAC3C,MAAMrD,OAAO,GAAGoD,KAAK,CAACxI,MAAqB;UAC3C,MAAM0I,SAAS,GAAGC,UAAU,CAACvD,OAAO,CAACwD,OAAO,CAACF,SAAS,IAAI,GAAG,CAAC;UAC9D,MAAMG,UAAU,GAAGF,UAAU,CAACvD,OAAO,CAACwD,OAAO,CAACC,UAAU,IAAI,GAAG,CAAC;UAChE,IAAIhF,IAAI,CAACiF,GAAG,CAACxI,KAAK,GAAGoI,SAAS,CAAC,GAAG,CAAC,IAAI7E,IAAI,CAACiF,GAAG,CAACtK,MAAM,GAAGqK,UAAU,CAAC,GAAG,CAAC,EAAE;YACzEzD,OAAO,CAACwD,OAAO,CAACF,SAAS,GAAGpI,KAAK,CAACyI,QAAQ,CAAC,CAAC;YAC5C3D,OAAO,CAACwD,OAAO,CAACC,UAAU,GAAGrK,MAAM,CAACuK,QAAQ,CAAC,CAAC;YAC9C,OAAO,IAAI;UACZ;UACA,OAAO,KAAK;QACb,CAAC,CAAC;QACF,IAAIT,oBAAoB,EAAE;UACzBd,sBAAsB,CAAC,CAAC;UACxBwB,UAAU,CAACxB,sBAAsB,EAAE,EAAE,CAAC;QACvC;MACD,CAAC,CAAC;MAEFQ,iBAAiB,CAACiB,OAAO,CAAC7D,OAAO,IAAI8C,cAAc,CAAEgB,OAAO,CAAC9D,OAAO,CAAC,CAAC;IACvE;IACA,MAAM+D,iBAAiB,GAAGnB,iBAAiB,CAAC,CAAC,CAAC;IAC9C,IAAImB,iBAAiB,IAAInD,MAAM,CAACoD,gBAAgB,EAAE;MACjDjB,gBAAgB,GAAG,IAAIiB,gBAAgB,CAAEC,SAAS,IAAK;QACtD,MAAMC,YAAY,GAAGD,SAAS,CAACd,IAAI,CAAEgB,QAAQ,IAAK;UACjD,OAAOA,QAAQ,CAACC,IAAI,KAAK,WAAW,IAClCD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC9B,CAAC,OAAO,EAAE,OAAO,CAAC,CAACnG,QAAQ,CAACkG,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAE;QAC7D,CAAC,CAAC;QACF,IAAIH,YAAY,EAAE;UACjBN,UAAU,CAACxB,sBAAsB,EAAE,EAAE,CAAC;QACvC;MACD,CAAC,CAAC;MACFW,gBAAgB,CAACe,OAAO,CAACC,iBAAiB,EAAE;QAC3CO,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IACA,OAAO,MAAM;MAAA,IAAAC,eAAA,EAAAC,iBAAA;MACZ/D,MAAM,CAACgE,mBAAmB,CAAC,QAAQ,EAAEvC,oBAAoB,CAAC;MAC1DzB,MAAM,CAACgE,mBAAmB,CAAC,QAAQ,EAAEvC,oBAAoB,CAAC;MAC1D,MAAMM,uBAAuB,GAAG5D,QAAQ,CAACkB,aAAa,CAAC,wBAAwB,CAAC;MAChF,IAAI0C,uBAAuB,EAAE;QAC5BA,uBAAuB,CAACiC,mBAAmB,CAAC,aAAa,EAAEvC,oBAAoB,CAAC;QAChFM,uBAAuB,CAACiC,mBAAmB,CAAC,QAAQ,EAAEvC,oBAAoB,CAAC;MAC5E;MACA,MAAMwC,UAAU,GAAIjE,MAAM,CAAS6B,0BAA0B;MAC7D,IAAIoC,UAAU,EAAE;QACfrC,aAAa,CAACqC,UAAU,CAAC;QACzB,OAAQjE,MAAM,CAAS6B,0BAA0B;MAClD;MACA,CAAAiC,eAAA,GAAA5B,cAAc,cAAA4B,eAAA,uBAAdA,eAAA,CAAgBI,UAAU,CAAC,CAAC;MAC5B,CAAAH,iBAAA,GAAA5B,gBAAgB,cAAA4B,iBAAA,uBAAhBA,iBAAA,CAAkBG,UAAU,CAAC,CAAC;IAC/B,CAAC;EACF,CAAC,EAAE,CAAC3G,IAAI,EAAE3D,mBAAmB,CAAC,CAAC;EAE/B,MAAMuK,aAAa,GAAGjQ,QAAQ,CAACkQ,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;EAE3E,oBACC1O,OAAA,CAAAE,SAAA;IAAAyO,QAAA,GACE7N,eAAe,CAAC8N,GAAG,CAAE9F,IAAI,IAAK;MAAA,IAAA+F,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,WAAA;MAC9B,MAAMC,QAAQ,IAAAJ,aAAA,GAAG/F,IAAI,CAACoG,MAAM,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAdA,aAAA,CAAgB/H,GAAG;MACpC,MAAMW,OAAO,IAAAqH,cAAA,GAAGhG,IAAI,CAACoG,MAAM,CAAC,CAAC,CAAC,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBnI,EAAE;MAClC,MAAMK,SAAS,GAAG,EAAA+H,cAAA,GAAAjG,IAAI,CAACoG,MAAM,CAAC,CAAC,CAAC,cAAAH,cAAA,uBAAdA,cAAA,CAAgB/H,SAAS,KAAItH,cAAc;MAC7D,MAAMyP,gBAAgB,GAAG,CAACrG,IAAI,aAAJA,IAAI,wBAAAkG,WAAA,GAAJlG,IAAI,CAAEC,KAAK,cAAAiG,WAAA,uBAAXA,WAAA,CAAalM,MAAM,KAAevD,4BAA4B;MACxF,MAAMoH,EAAE,GAAGmC,IAAI,CAACnC,EAAE;MAClB,oBACC3G,OAAA,CAAClC,GAAG;QAEHsR,EAAE,EAAE;UACHxK,KAAK,EAAE,MAAM;UACb9B,MAAM,EAAE,MAAM;UACd+B,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,YAAY;UAC5BC,UAAU,EAAE,QAAQ;UACpBE,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACX,CAAE;QAAAyJ,QAAA,eAEF3O,OAAA,CAAClC,GAAG;UACHsR,EAAE,EAAE;YACH,GAAGzK,mBAAmB;YACtBoC,eAAe,EAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe;YAC3CjE,MAAM,EAAE,GAAGgG,IAAI,CAACC,KAAK,CAACjG,MAAM;UAC7B,CAAE;UACFuM,OAAO,EAAGC,CAAC,IAAK9H,WAAW,CAAC8H,CAAC,EAAE3I,EAAE,EAAEc,OAAO,EAAEwH,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEE,gBAAgB,CAAE;UACvFI,SAAS,EAAE,KAAM;UACjB5I,EAAE,EAAEA,EAAG;UACP6I,WAAW,EAAEA,CAAA,KAAM;YAClBxO,gBAAgB,CAAC;cAChBoG,QAAQ,EAAEK,OAAO;cACjBhB,WAAW,EAAEE,EAAE;cACfpC,KAAK,EAAE;YACR,CAAC,CAAC;UACH,CAAE;UAAAoK,QAAA,EAEDM,QAAQ,gBACRjP,OAAA;YACCyP,GAAG,EAAER,QAAS;YACdS,GAAG,EAAC,UAAU;YACd3G,KAAK,EAAE;cAAE,GAAG5D,UAAU;cAAE6B;YAAU;UAAE;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEF9P,OAAA,CAAClC,GAAG;YACHsR,EAAE,EAAE;cACHW,SAAS,EAAE,QAAQ;cACnBnL,KAAK,EAAE,MAAM;cACb9B,MAAM,EAAE,MAAM;cACd+B,OAAO,EAAE,MAAM;cACfY,aAAa,EAAE,QAAQ;cACvBX,cAAc,EAAE;YACjB,CAAE;YAAA6J,QAAA,gBAEF3O,OAAA,CAAClC,GAAG;cACHsR,EAAE,EAAE5J,aAAc;cAClB+J,SAAS,EAAE,KAAM;cAAAZ,QAAA,gBAEjB3O,OAAA;gBACCgQ,uBAAuB,EAAE;kBAAEC,MAAM,EAAErR;gBAAW,CAAE;gBAChDmK,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAe;cAAE;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACF9P,OAAA,CAACjC,UAAU;gBACVmS,OAAO,EAAC,IAAI;gBACZC,KAAK,EAAC,QAAQ;gBACdf,EAAE,EAAE;kBAAEgB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EAE1C/N,SAAS,CAAC,aAAa;cAAC;gBAAA+O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN9P,OAAA,CAACjC,UAAU;cACVmS,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd7G,KAAK,EAAC,eAAe;cACrB8F,EAAE,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEvB/N,SAAS,CAAC,4BAA4B;YAAC;cAAA+O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACb9P,OAAA,CAACjC,UAAU;cACVmS,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd7G,KAAK,EAAC,eAAe;cACrB8F,EAAE,EAAE;gBAAE7J,SAAS,EAAE,KAAK;gBAAE6K,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEzC/N,SAAS,CAAC,IAAI;YAAC;cAAA+O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACZ3N,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAKsE,EAAE,gBACzE3G,OAAA,CAAC9B,SAAS;cACTqG,KAAK,EAAEhC,SAAU;cACjB+N,QAAQ,EAAGhB,CAAC,IAAK9M,YAAY,CAAC8M,CAAC,CAAChL,MAAM,CAACC,KAAK,CAAE;cAC9CgM,SAAS,EAAEtH,gBAAiB;cAC5BuH,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEA9P,OAAA,CAAClC,GAAG;cAAA6Q,QAAA,gBACH3O,OAAA,CAAClC,GAAG;gBAACsR,EAAE,EAAE/J,YAAa;gBAAAsJ,QAAA,gBACtB3O,OAAA,CAAC3B,OAAO;kBAACoS,KAAK,EAAE7P,SAAS,CAAC,aAAa,CAAE;kBAAA+N,QAAA,eACpD3O,OAAA;oBAAK+I,KAAK,EAAE;sBAAE2H,aAAa,EAAE,MAAM;sBAAEC,MAAM,EAAC;oBAAS,CAAE;oBAAAhC,QAAA,eACrD3O,OAAA;sBACEgQ,uBAAuB,EAAE;wBAAEC,MAAM,EAAEpR;sBAAU,CAAE;sBAC/CkK,KAAK,EAAE;wBACLO,KAAK,EAAE,OAAO;wBACdqH,MAAM,EAAE,SAAS;wBACjBP,QAAQ,EAAE,MAAM;wBAChBQ,OAAO,EAAE,KAAK;wBACdF,aAAa,EAAE;sBACjB,CAAE;sBACF/J,EAAE,EAAC,WAAW;sBACPkK,SAAS,EAAC;oBAAoB;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGG9P,OAAA,CAAC3B,OAAO;kBAACoS,KAAK,EAAE7P,SAAS,CAAC,aAAa,CAAE;kBAAA+N,QAAA,eACxC3O,OAAA;oBACCqP,OAAO,EAAGhL,KAAK,IAAK;sBACnB;oBAAA,CACC;oBACL2L,uBAAuB,EAAE;sBAAEC,MAAM,EAAEnR;oBAAM,CAAE;oBAC3CiK,KAAK,EAAE;sBAAEO,KAAK,EAAE,OAAO;sBAAEqH,MAAM,EAAE,SAAS;sBAAEP,QAAQ,EAAE,MAAM;sBAAEQ,OAAO,EAAE;oBAAM,CAAE;oBAC/EjK,EAAE,EAAC,QAAQ;oBACXkK,SAAS,EAAC;oBACV;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,eACV9P,OAAA,CAAC3B,OAAO;kBAACoS,KAAK,EAAE7P,SAAS,CAAC,aAAa,CAAE;kBAAA+N,QAAA,eAC3C3O,OAAA;oBACIqP,OAAO,EAAGhL,KAAK,IAAK;sBAAA,IAAAyM,sBAAA;sBAEtBzM,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0M,eAAe,CAAC,CAAC;sBACxB,CAAAD,sBAAA,GAAArI,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAoI,sBAAA,uBAAtCA,sBAAA,CAAwCnI,KAAK,CAAC,CAAC;oBAChD,CAAE;oBACFhC,EAAE,EAAC,cAAc;oBACjBkK,SAAS,EAAC,oBAAoB;oBAC9Bb,uBAAuB,EAAE;sBAAEC,MAAM,EAAElR;oBAAW,CAAE;oBAChDgK,KAAK,EAAE;sBAAEO,KAAK,EAAE,OAAO;sBAAEqH,MAAM,EAAE,SAAS;sBAAEP,QAAQ,EAAE;oBAAO;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACb9P,OAAA;kBACC8N,IAAI,EAAC,MAAM;kBACXnH,EAAE,EAAC,aAAa;kBAChBoC,KAAK,EAAE;oBAAElE,OAAO,EAAE;kBAAO,CAAE;kBAC3BmM,MAAM,EAAC,SAAS;kBAChBV,QAAQ,EAAE5K;gBAAkB;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACF9P,OAAA,CAAC1B,QAAQ;kBAACuJ,IAAI,EAAErG,YAAa;kBAACyP,gBAAgB,EAAE,IAAK;kBAACC,OAAO,EAAEhP,aAAc;kBAACiP,YAAY,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA1C,QAAA,eACxI3O,OAAA,CAACzB,KAAK;oBAAC2S,OAAO,EAAEhP,aAAc;oBAACoP,QAAQ,EAAE1P,gBAAiB;oBAACwN,EAAE,EAAE;sBAAExK,KAAK,EAAE;oBAAO,CAAE;oBAAA+J,QAAA,EAC/EjN;kBAAe;oBAAAiO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACV9P,OAAA,CAAClC,GAAG;gBACH+S,SAAS,EAAC,uBAAuB;gBAAAlC,QAAA,eAElC3O,OAAA;kBACCqP,OAAO,EAAGhL,KAAK,IAAK;oBACpBA,KAAK,CAAC0M,eAAe,CAAC,CAAC,CAAC,CAAC;;oBAEzB;oBACA/P,gBAAgB,CAAC;sBAChBoG,QAAQ,EAAE,EAAE;sBAAE;sBACdX,WAAW,EAAEE,EAAE;sBAAE;sBACjBpC,KAAK,EAAE;oBACR,CAAC,CAAC;oBACFb,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxBE,sBAAsB,CAAC,IAAI,CAAC;kBAC5B,CAAE;kBACGiN,SAAS,EAAC,iBAAiB;kBAAAlC,QAAA,EAG9B/N,SAAS,CAAC,kBAAkB;gBAAC;kBAAA+O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GA3KDnJ,EAAE;QAAAgJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4KH,CAAC;IAER,CAAC,CAAC,EACD3L,OAAO,CAACpD,aAAa,CAACwD,KAAK,CAAC,gBAC5BvE,OAAA,CAAChC,OAAO;MACP6S,SAAS,EAAC,sBAAsB;MAChClK,EAAE,EAAEA,EAAG;MACPkB,IAAI,EAAEA,IAAK;MACX0J,QAAQ,EAAE,IAAK;MACfL,OAAO,EAAEtJ,WAAY;MACrB4J,eAAe,EAAC,MAAM;MACtBC,SAAS,EAAE;QACVC,KAAK,EAAE;UACN3I,KAAK,EAAE;YACN,GAAGjF,gBAAgB,CAACE,YAAY;YAChClB,MAAM,EAAE,MAAM;YACd8B,KAAK,EAAE,MAAM;YACbI,OAAO,EAAE,UAAU;YACnB2M,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE;UACd;QACD;MACD,CAAE;MAAAjD,QAAA,eAEF3O,OAAA,CAAClC,GAAG;QACHsR,EAAE,EAAE;UACHvK,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXxC,MAAM,EAAE,MAAM;UACdkC,OAAO,EAAE,QAAQ;UACjBoL,QAAQ,EAAE;QACX,CAAE;QAAAzB,QAAA,gBAEF3O,OAAA,CAAClC,GAAG;UACR+S,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAEvK,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE;UAAM,CAAE;UAAAqJ,QAAA,gBAE1D3O,OAAA;YAAMgQ,uBAAuB,EAAE;cAAEC,MAAM,EAAEjR;YAAiB;UAAE;YAAA2Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/D9P,OAAA,CAACjC,UAAU;YACTqS,QAAQ,EAAC,MAAM;YACfhB,EAAE,EAAE;cAAEuB,MAAM,EAAE;YAAU,CAAE;YAC1BtB,OAAO,EAAE9G,kBAAmB;YAAAoG,QAAA,EAE3B/N,SAAS,CAAC,SAAS;UAAC;YAAA+O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACb9P,OAAA;YACE8N,IAAI,EAAC,MAAM;YACXnH,EAAE,EAAC,gBAAgB;YACnBoC,KAAK,EAAE;cAAElE,OAAO,EAAE;YAAO,CAAE;YAC3BmM,MAAM,EAAC,SAAS;YAChBV,QAAQ,EAAEhJ;UAAmB;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACF9P,OAAA,CAAC3B,OAAO;YAACoS,KAAK,EAAE7P,SAAS,CAAC,yBAAyB,CAAE;YAAA+N,QAAA,eACnD3O,OAAA,CAAC/B,UAAU;cACToR,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAwC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;gBACb/Q,gBAAgB,CAAC;kBACfoG,QAAQ,EAAEzE,uBAAuB,CAACN,kBAAkB,IAAAwP,sBAAA,IAAAC,sBAAA,GAChDhR,eAAe,CAAC+H,IAAI,CAACmJ,CAAC,IAAIA,CAAC,CAACrL,EAAE,KAAKhE,uBAAuB,CAACN,kBAAkB,CAAC,cAAAyP,sBAAA,wBAAAC,sBAAA,GAA9ED,sBAAA,CAAgF5C,MAAM,CAAC,CAAC,CAAC,cAAA6C,sBAAA,uBAAzFA,sBAAA,CAA2FpL,EAAE,cAAAkL,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GACnG,EAAE;kBACNpL,WAAW,EAAE9D,uBAAuB,CAACN,kBAAkB;kBACvDkC,KAAK,EAAE;gBACT,CAAC,CAAC;gBACFb,eAAe,CAAC,IAAI,CAAC;gBACrBE,sBAAsB,CAAC,IAAI,CAAC;cAC9B,CAAE;cACFqO,IAAI,EAAC,OAAO;cAAAtD,QAAA,eAEZ3O,OAAA;gBAAM+I,KAAK,EAAE;kBAACqH,QAAQ,EAAC;gBAAM,CAAE;gBAACJ,uBAAuB,EAAE;kBAAEC,MAAM,EAAExB;gBAAc;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGN9P,OAAA,CAAClC,GAAG;UACG+S,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAEvK,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAA4J,QAAA,gBAE9C3O,OAAA;YAAMgQ,uBAAuB,EAAE;cAAEC,MAAM,EAAE9Q;YAAc,CAAE;YACzD4J,KAAK,EAAE;cAAElE,OAAO,EAAE;YAAO;UAAE;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC7B9P,OAAA,CAAC3B,OAAO;YAACoS,KAAK,EAAE9N,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAGmB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA+N,QAAA,eAC/I3O,OAAA;cAAA2O,QAAA,eACC3O,OAAA,CAAC/B,UAAU;gBACVoR,OAAO,EAAEA,CAAA,KAAMhH,oBAAoB,CAAC1F,uBAAuB,CAACG,MAAM,CAAE;gBACpEmP,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAEvP,uBAAuB,CAACG,MAAM,IAAIrD,wBAAyB;gBACrE2P,EAAE,EAAE;kBACHwB,OAAO,EAAEjO,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7EkR,MAAM,EAAEhO,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAkP,QAAA,eAEF3O,OAAA,CAACvB,UAAU;kBAAC2R,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACV9P,OAAA,CAACjC,UAAU;YAACqS,QAAQ,EAAC,MAAM;YAAAzB,QAAA,EAAEhM,uBAAuB,CAACG;UAAM;YAAA6M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzE9P,OAAA,CAAC3B,OAAO;YAACoS,KAAK,EAAE9N,uBAAuB,CAACG,MAAM,IAAItD,wBAAwB,GAAGoB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAA+N,QAAA,eAC/I3O,OAAA;cAAA2O,QAAA,eACC3O,OAAA,CAAC/B,UAAU;gBACVoR,OAAO,EAAEA,CAAA,KAAMrH,oBAAoB,CAACrF,uBAAuB,CAACG,MAAM,CAAE;gBACpEmP,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAEvP,uBAAuB,CAACG,MAAM,IAAItD,wBAAyB;gBACrE4P,EAAE,EAAE;kBACHwB,OAAO,EAAEjO,uBAAuB,CAACG,MAAM,IAAItD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7EmR,MAAM,EAAEhO,uBAAuB,CAACG,MAAM,IAAItD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAmP,QAAA,eAEF3O,OAAA,CAACtB,OAAO;kBAAC0R,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9P,OAAA,CAAC3B,OAAO;UAACoS,KAAK,EAAE7P,SAAS,CAAC,UAAU,CAAE;UAAA+N,QAAA,eACtC3O,OAAA,CAAClC,GAAG;YAAA6Q,QAAA,gBACH3O,OAAA,CAAClC,GAAG;cAAC+S,SAAS,EAAC,kBAAkB;cAAAlC,QAAA,eAChC3O,OAAA,CAAC/B,UAAU;gBACVgU,IAAI,EAAC,OAAO;gBACZ5C,OAAO,EAAE7K,mBAAoB;gBAAAmK,QAAA,eAE7B3O,OAAA;kBACCgQ,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7Q;kBAAS,CAAE;kBAC9C2J,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO;gBAAE;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEL9P,OAAA,CAAChC,OAAO;cACE6S,SAAS,EAAC,cAAc;cAClChJ,IAAI,EAAE3D,mBAAoB;cAC1BqN,QAAQ,EAAE,IAAK;cACfL,OAAO,EAAExM,0BAA2B;cACpC8M,eAAe,EAAC,MAAM;cACtBC,SAAS,EAAE;gBACVC,KAAK,EAAE;kBACN3I,KAAK,EAAE;oBACN,GAAGjF,gBAAgB,CAACG,eAAe;oBACnCW,KAAK,EAAE;kBACR;gBACD;cACD,CAAE;cAAA+J,QAAA,eAEF3O,OAAA,CAAClC,GAAG;gBAACqU,CAAC,EAAE,CAAE;gBAAAxD,QAAA,gBACT3O,OAAA,CAAClC,GAAG;kBACH+G,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAA4J,QAAA,gBAEnB3O,OAAA,CAACjC,UAAU;oBACVmS,OAAO,EAAC,WAAW;oBACnBd,EAAE,EAAE;sBAAE9F,KAAK,EAAE;oBAAwB,CAAE;oBAAAqF,QAAA,EAErC/N,SAAS,CAAC,kBAAkB;kBAAC;oBAAA+O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb9P,OAAA,CAAC/B,UAAU;oBACVgU,IAAI,EAAC,OAAO;oBACZ5C,OAAO,EAAE3K,0BAA2B;oBAAAiK,QAAA,eAEpC3O,OAAA;sBACCgQ,uBAAuB,EAAE;wBAAEC,MAAM,EAAE5Q;sBAAU,CAAE;sBAC/C0J,KAAK,EAAE;wBAAEO,KAAK,EAAE;sBAAQ;oBAAE;sBAAAqG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN9P,OAAA,CAAC3B,OAAO;kBAACoS,KAAK,EAAE7P,SAAS,CAAC,aAAa,CAAE;kBAAA+N,QAAA,eAC1C3O,OAAA,CAAClC,GAAG;oBAACsU,EAAE,EAAE,CAAE;oBAAAzD,QAAA,gBACV3O,OAAA,CAACjC,UAAU;sBACVmS,OAAO,EAAC,OAAO;sBACb5G,KAAK,EAAC,eAAe;sBACrB8F,EAAE,EAAE;wBAAEiD,YAAY,EAAE;sBAAO,CAAE;sBAAA1D,QAAA,EAE5B/N,SAAS,CAAC,eAAe;oBAAC;sBAAA+O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACb9P,OAAA,CAAC9B,SAAS;sBACToU,MAAM;sBACNC,SAAS;sBACTrC,OAAO,EAAC,UAAU;sBAClB+B,IAAI,EAAC,OAAO;sBACZ1N,KAAK,EAAExB,cAAe;sBACtBuN,QAAQ,EAAElM,kBAAmB;sBAC7BgL,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3BoD,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFN,QAAQ;sBAAAvD,QAAA,gBAEN3O,OAAA,CAAC7B,QAAQ;wBAACoG,KAAK,EAAC,MAAM;wBAAAoK,QAAA,EAAE/N,SAAS,CAAC,MAAM;sBAAC;wBAAA+O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACrD9P,OAAA,CAAC7B,QAAQ;wBAACoG,KAAK,EAAC,cAAc;wBAAAoK,QAAA,EAAE/N,SAAS,CAAC,eAAe;sBAAC;wBAAA+O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtE9P,OAAA,CAAC7B,QAAQ;wBAACoG,KAAK,EAAC,SAAS;wBAAAoK,QAAA,EAAE/N,SAAS,CAAC,UAAU;sBAAC;wBAAA+O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC5D9P,OAAA,CAAC7B,QAAQ;wBAACoG,KAAK,EAAC,cAAc;wBAAAoK,QAAA,EAAE/N,SAAS,CAAC,eAAe;sBAAC;wBAAA+O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtE9P,OAAA,CAAC7B,QAAQ;wBAACoG,KAAK,EAAC,WAAW;wBAAAoK,QAAA,EAAE/N,SAAS,CAAC,YAAY;sBAAC;wBAAA+O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChE9P,OAAA,CAAC7B,QAAQ;wBAACoG,KAAK,EAAC,kBAAkB;wBAAAoK,QAAA,EAAE/N,SAAS,CAAC,oBAAoB;sBAAC;wBAAA+O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACV9P,OAAA,CAAClC,GAAG;kBAACsU,EAAE,EAAE,CAAE;kBAAAzD,QAAA,gBACV3O,OAAA,CAACjC,UAAU;oBACVmS,OAAO,EAAC,OAAO;oBACf5G,KAAK,EAAC,eAAe;oBAAAqF,QAAA,EAEnB/N,SAAS,CAAC,kBAAkB;kBAAC;oBAAA+O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb9P,OAAA,CAAClC,GAAG;oBACH+G,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACP8M,EAAE,EAAE,CAAE;oBAAAzD,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,GAAG,CAAE9F,IAAI,IAAK;sBAC9B;sBACA,MAAM2J,gBAAgB,GAAG3R,eAAe,CAAC+H,IAAI,CAAEmJ,CAAC,IAAKA,CAAC,CAACrL,EAAE,KAAK5F,aAAa,CAAC0F,WAAW,CAAC;sBACxF,MAAMiM,YAAY,GAAGD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEvD,MAAM,CAACrG,IAAI,CAAE8J,GAAG,IAAKA,GAAG,CAAChM,EAAE,KAAK5F,aAAa,CAACqG,QAAQ,CAAC;sBAC9F,MAAMwL,gBAAgB,GAAG,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE1L,SAAS,KAAItH,cAAc;;sBAElE;sBACA,MAAMmT,UAAU,GAAI/J,IAAI,KAAK,MAAM,IAAI8J,gBAAgB,KAAK,OAAO,IAC5D9J,IAAI,KAAK,KAAK,IAAI8J,gBAAgB,KAAK,SAAU;sBAExD,oBACC5S,OAAA,CAAC5B,MAAM;wBAENiR,OAAO,EAAEA,CAAA,KACRhO,SAAS,CAACN,aAAa,CAAC0F,WAAW,EAAE1F,aAAa,CAACqG,QAAQ,EAAE0B,IAAsB,CACnF;wBACDoH,OAAO,EAAC,UAAU;wBAClB+B,IAAI,EAAC,OAAO;wBACZ7C,EAAE,EAAE;0BACHxK,KAAK,EAAE,QAAQ;0BACf9B,MAAM,EAAE,MAAM;0BACdkC,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/B0N,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtC9L,eAAe,EACd8L,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BzJ,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVvC,eAAe,EACd8L,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAAlE,QAAA,EAED/N,SAAS,CAACkI,IAAI;sBAAC,GA1BXA,IAAI;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV9P,OAAA,CAAC3B,OAAO;UAACoS,KAAK,EAAE7P,SAAS,CAAC,kBAAkB,CAAE;UAAA+N,QAAA,eAC9C3O,OAAA,CAAClC,GAAG;YAAC+S,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChC3O,OAAA,CAAC/B,UAAU;cACVoR,OAAO,EAAE7F,0BAA2B;cACpCyI,IAAI,EAAC,OAAO;cAAAtD,QAAA,eAEZ3O,OAAA;gBACA+I,KAAK,EAAE;kBACNhC,eAAe,EAAExD,aAAa;kBAC9B6B,YAAY,EAAE,MAAM;kBACpBR,KAAK,EAAE,MAAM;kBACb9B,MAAM,EAAE;gBACT;cAAE;gBAAA6M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEV9P,OAAA,CAAC3B,OAAO;UAACoS,KAAK,EAAEjQ,eAAe,GAAGI,SAAS,CAAC,2CAA2C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAAA+N,QAAA,eACtH3O,OAAA,CAAClC,GAAG;YAAC+S,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChC3O,OAAA,CAAC/B,UAAU;cACVoR,OAAO,EAAElG,uBAAwB;cACjC8I,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAE1R,eAAgB;cAAAmO,QAAA,eAE1B3O,OAAA;gBACCgQ,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhR;gBAAS,CAAE;gBAC9C8J,KAAK,EAAE;kBAAE6H,OAAO,EAAEpQ,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAAmP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV9P,OAAA,CAAC3B,OAAO;UAACoS,KAAK,EAAE7P,SAAS,CAAC,gBAAgB,CAAE;UAAA+N,QAAA,eAE5C3O,OAAA,CAAClC,GAAG;YAAC+S,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChC3O,OAAA,CAAC/B,UAAU;cACVoR,OAAO,EAAErG,mBAAoB;cAC7BiJ,IAAI,EAAC,OAAO;cAAAtD,QAAA,eAEZ3O,OAAA;gBAAMgQ,uBAAuB,EAAE;kBAAEC,MAAM,EAAE/Q;gBAAW;cAAE;gBAAAyQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACP,IAAI,EAEP7M,WAAW,iBACVjD,OAAA,CAACH,0BAA0B;MAACyC,MAAM,EAAEW,WAAY;MAAC+P,gBAAgB,EAAEA,CAAA,KAAM9P,YAAY,CAAC,KAAK,CAAE;MAAC+P,aAAa,EAAE/L,wBAAyB;MAAC9D,eAAe,EAAEA,eAAgB;MAACD,YAAY,EAAEA,YAAa;MAACmE,kBAAkB,EAAEA,kBAAmB;MAAC7D,cAAc,EAAEA;IAAe;MAAAkM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAC7Q,eAEF9P,OAAA,CAAChC,OAAO;MACP6J,IAAI,EAAEC,eAAgB;MACtByJ,QAAQ,EAAE9O,mBAAoB;MAC9ByO,OAAO,EAAE9H,sBAAuB;MAChC+H,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACF6B,eAAe,EAAE;QAChB9B,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA1C,QAAA,eAEF3O,OAAA,CAAClC,GAAG;QAAA6Q,QAAA,gBACH3O,OAAA,CAACJ,YAAY;UACZ0J,KAAK,EAAEV,qBAAsB;UAC7B0H,QAAQ,EAAEjH;QAAkB;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACA9P,OAAA;UAAA2O,QAAA,EACF;AACL;AACA;AACA;AACA;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGTnM,mBAAmB,iBAClB3D,OAAA,CAAAE,SAAA;MAAAyO,QAAA,eACC3O,OAAA,CAACF,oBAAoB;QAAC6D,mBAAmB,EAAEA,mBAAoB;QAACC,sBAAsB,EAAEA,sBAAuB;QAACsD,wBAAwB,EAAEA,wBAAyB;QAACxD,eAAe,EAAEA;MAAgB;QAAAiM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC,gBACvM,CACF;EAAA,eAGD,CAAC;AAEL,CAAC;AAACrP,EAAA,CAt/BIN,YAAY;EAAA,QACQxB,cAAc,EAYnCW,cAAc;AAAA;AAAA6T,EAAA,GAbbhT,YAAY;AAw/BlB,eAAeA,YAAY;AAAC,IAAAgT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}